# Setup Telegram Webhook
$webhookUrl = "https://d404-103-181-40-120.ngrok-free.app/api/pingbot/telegram/webhook"
$apiUrl = "https://d404-103-181-40-120.ngrok-free.app/api/pingbot/telegram/set-webhook"

$body = @{
    webhookUrl = $webhookUrl
} | ConvertTo-Json

Write-Host "Setting up Telegram webhook..."
Write-Host "Webhook URL: $webhookUrl"
Write-Host "API URL: $apiUrl"

try {
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body
    Write-Host "✅ Webhook setup successful!"
    Write-Host "Response: $($response | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "❌ Error setting up webhook:"
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}

# Also get webhook info
Write-Host "`nGetting webhook info..."
try {
    $infoResponse = Invoke-RestMethod -Uri "https://d404-103-181-40-120.ngrok-free.app/api/pingbot/telegram/webhook-info" -Method GET
    Write-Host "✅ Webhook info retrieved!"
    Write-Host "Info: $($infoResponse | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "❌ Error getting webhook info:"
    Write-Host $_.Exception.Message
}

# Get bot info
Write-Host "`nGetting bot info..."
try {
    $botResponse = Invoke-RestMethod -Uri "https://d404-103-181-40-120.ngrok-free.app/api/pingbot/telegram/bot-info" -Method GET
    Write-Host "✅ Bot info retrieved!"
    Write-Host "Bot: $($botResponse | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "❌ Error getting bot info:"
    Write-Host $_.Exception.Message
}

Write-Host "`nSetup complete! Your Telegram bot is ready for testing."
Write-Host "You can now message your bot on Telegram to test the dynamic template service."
