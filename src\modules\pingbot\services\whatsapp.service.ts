import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface WhatsAppMessage {
  to: string;
  type: 'text' | 'template' | 'interactive';
  text?: {
    body: string;
  };
  template?: {
    name: string;
    language: {
      code: string;
    };
    components?: any[];
  };
  interactive?: {
    type: string;
    body: {
      text: string;
    };
    action: any;
  };
}

export interface WhatsAppWebhookMessage {
  from: string;
  id: string;
  timestamp: string;
  type: string;
  text?: {
    body: string;
  };
  image?: {
    id: string;
    mime_type: string;
    sha256: string;
    caption?: string;
  };
  document?: {
    id: string;
    filename: string;
    mime_type: string;
    sha256: string;
    caption?: string;
  };
  audio?: {
    id: string;
    mime_type: string;
    sha256: string;
  };
  video?: {
    id: string;
    mime_type: string;
    sha256: string;
    caption?: string;
  };
  location?: {
    latitude: number;
    longitude: number;
    name?: string;
    address?: string;
  };
  contacts?: any[];
}

@Injectable()
export class WhatsAppService {
  private readonly logger = new Logger(WhatsAppService.name);
  private readonly baseUrl: string;
  private readonly accessToken: string;
  private readonly phoneNumberId: string;

  constructor(private readonly configService: ConfigService) {
    this.baseUrl = this.configService.get('WHATSAPP_API_BASE_URL') || 'https://graph.facebook.com/v18.0';
    this.accessToken = this.configService.get('WHATSAPP_ACCESS_TOKEN');
    this.phoneNumberId = this.configService.get('WHATSAPP_PHONE_NUMBER_ID');
  }

  async sendMessage(message: WhatsAppMessage): Promise<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> {
    try {
      if (!this.accessToken || !this.phoneNumberId) {
        this.logger.warn('WhatsApp credentials not configured');
        return {
          success: false,
          error: 'WhatsApp not configured',
        };
      }

      const url = `${this.baseUrl}/${this.phoneNumberId}/messages`;
      
      const response = await axios.post(
        url,
        message,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      if (response.data && response.data.messages && response.data.messages[0]) {
        const messageId = response.data.messages[0].id;
        this.logger.debug(`WhatsApp message sent successfully: ${messageId}`);
        
        return {
          success: true,
          messageId,
        };
      }

      return {
        success: false,
        error: 'Invalid response from WhatsApp API',
      };
    } catch (error) {
      this.logger.error('Error sending WhatsApp message:', error);
      
      return {
        success: false,
        error: error.response?.data?.error?.message || error.message,
      };
    }
  }

  async sendTextMessage(to: string, text: string): Promise<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> {
    const message: WhatsAppMessage = {
      to,
      type: 'text',
      text: {
        body: text,
      },
    };

    return this.sendMessage(message);
  }

  async sendTemplateMessage(
    to: string,
    templateName: string,
    languageCode: string = 'en',
    components?: any[],
  ): Promise<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> {
    const message: WhatsAppMessage = {
      to,
      type: 'template',
      template: {
        name: templateName,
        language: {
          code: languageCode,
        },
        components,
      },
    };

    return this.sendMessage(message);
  }

  async sendInteractiveMessage(
    to: string,
    bodyText: string,
    action: any,
    type: string = 'button',
  ): Promise<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> {
    const message: WhatsAppMessage = {
      to,
      type: 'interactive',
      interactive: {
        type,
        body: {
          text: bodyText,
        },
        action,
      },
    };

    return this.sendMessage(message);
  }

  async markAsRead(messageId: string): Promise<boolean> {
    try {
      if (!this.accessToken || !this.phoneNumberId) {
        return false;
      }

      const url = `${this.baseUrl}/${this.phoneNumberId}/messages`;
      
      await axios.post(
        url,
        {
          messaging_product: 'whatsapp',
          status: 'read',
          message_id: messageId,
        },
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
          },
        },
      );

      return true;
    } catch (error) {
      this.logger.error('Error marking message as read:', error);
      return false;
    }
  }

  async downloadMedia(mediaId: string): Promise<{
    success: boolean;
    url?: string;
    mimeType?: string;
    error?: string;
  }> {
    try {
      if (!this.accessToken) {
        return {
          success: false,
          error: 'WhatsApp not configured',
        };
      }

      // First, get media URL
      const mediaUrl = `${this.baseUrl}/${mediaId}`;
      const mediaResponse = await axios.get(mediaUrl, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });

      if (!mediaResponse.data || !mediaResponse.data.url) {
        return {
          success: false,
          error: 'Invalid media response',
        };
      }

      // Download the actual media
      const downloadResponse = await axios.get(mediaResponse.data.url, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
        responseType: 'arraybuffer',
      });

      // In a real implementation, you would upload this to your storage service
      // For now, we'll just return the URL
      return {
        success: true,
        url: mediaResponse.data.url,
        mimeType: mediaResponse.data.mime_type,
      };
    } catch (error) {
      this.logger.error('Error downloading media:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  parseWebhookMessage(webhookData: any): WhatsAppWebhookMessage | null {
    try {
      if (!webhookData.entry || !webhookData.entry[0] || !webhookData.entry[0].changes) {
        return null;
      }

      const change = webhookData.entry[0].changes[0];
      if (!change.value || !change.value.messages || !change.value.messages[0]) {
        return null;
      }

      const message = change.value.messages[0];
      
      return {
        from: message.from,
        id: message.id,
        timestamp: message.timestamp,
        type: message.type,
        text: message.text,
        image: message.image,
        document: message.document,
        audio: message.audio,
        video: message.video,
        location: message.location,
        contacts: message.contacts,
      };
    } catch (error) {
      this.logger.error('Error parsing webhook message:', error);
      return null;
    }
  }

  verifyWebhook(mode: string, token: string, challenge: string): string | null {
    const verifyToken = this.configService.get('WHATSAPP_VERIFY_TOKEN');
    
    if (mode === 'subscribe' && token === verifyToken) {
      this.logger.log('WhatsApp webhook verified successfully');
      return challenge;
    }
    
    this.logger.warn('WhatsApp webhook verification failed');
    return null;
  }

  isConfigured(): boolean {
    return !!(this.accessToken && this.phoneNumberId);
  }

  getConfiguration(): {
    isConfigured: boolean;
    phoneNumberId?: string;
    baseUrl: string;
  } {
    return {
      isConfigured: this.isConfigured(),
      phoneNumberId: this.phoneNumberId,
      baseUrl: this.baseUrl,
    };
  }
}
