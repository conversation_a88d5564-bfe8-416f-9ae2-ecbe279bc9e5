const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 10000
    });
    console.log(`✅ Response: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, 2000));
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

async function testAllTemplates() {
  console.log('🧪 COMPREHENSIVE PINGBOT TEMPLATE TESTING\n');
  console.log('============================================================\n');

  console.log('1️⃣ SETUP: Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n2️⃣ ORDER STATUS TEMPLATES:');
  await sendMessage('status');
  await sendMessage('track');
  await sendMessage('tracking');
  await sendMessage('where is my order');
  await sendMessage('order status');
  await sendMessage('my order');
  
  console.log('\n3️⃣ DELIVERY TEMPLATES:');
  await sendMessage('delivery');
  await sendMessage('shipping');
  await sendMessage('when will arrive');
  await sendMessage('delivery time');
  await sendMessage('shipping cost');
  await sendMessage('delivery status');
  await sendMessage('when will my order arrive');
  await sendMessage('where is my package');
  
  console.log('\n4️⃣ PAYMENT TEMPLATES:');
  await sendMessage('payment');
  await sendMessage('pay');
  await sendMessage('refund');
  await sendMessage('money back');
  await sendMessage('payment method');
  await sendMessage('payment status');
  await sendMessage('how much did i pay');
  await sendMessage('receipt');
  await sendMessage('invoice');
  
  console.log('\n5️⃣ PRODUCT TEMPLATES:');
  await sendMessage('products');
  await sendMessage('iphone');
  await sendMessage('laptop');
  await sendMessage('smartphone');
  await sendMessage('price');
  await sendMessage('cost');
  await sendMessage('available');
  await sendMessage('in stock');
  await sendMessage('catalog');
  await sendMessage('all products');
  await sendMessage('show me products');
  await sendMessage('featured');
  await sendMessage('new arrivals');

  console.log('\n🎉 TEMPLATE TESTING COMPLETED!');
  console.log('📊 Check server logs for detailed template matching results');
}

testAllTemplates().catch(console.error);
