import { Document } from 'mongoose';

// Base interfaces
export interface BaseEntity {
  _id?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication interfaces
export interface JwtPayload {
  sub: string; // user ID
  email: string;
  storeId?: string;
  role: string;
  iat?: number;
  exp?: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface GoogleProfile {
  id: string;
  email: string;
  name: string;
  picture?: string;
}

// User interfaces
export interface UserDocument extends Document {
  email: string;
  phone?: string;
  fullName: string;
  avatarUrl?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  googleId?: string;
  passwordHash?: string;
  lastLogin?: Date;
}

// Store interfaces
// Temporarily commented out due to Document/BaseEntity conflicts
// export interface StoreDocument extends Document, BaseEntity {
//   userId: string;
//   name: string;
//   handle: string;
//   description?: string;
//   bio?: string;
//   logoUrl?: string;
//   coverImageUrl?: string;
//   whatsappNumber?: string;
//   whatsappVerified: boolean;
//   instagramHandle?: string;
//   websiteUrl?: string;
//   location?: string;
//   isActive: boolean;
//   isPublished: boolean;
//   allowReviews: boolean;
//   codEnabled: boolean;
//   minOrderAmount: number;
//   shippingFee: number;
//   freeShippingAbove?: number;
//   currentTheme: string;
//   themeConfig?: any;
//   onboardingCompleted: boolean;
//   onboardingSteps: any;
//   totalViews: number;
//   totalOrders: number;
//   totalRevenue: number;
// }

// Product interfaces
// Temporarily commented out due to Document/BaseEntity conflicts
// export interface ProductDocument extends Document, BaseEntity {
//   storeId: string;
//   categoryId?: string;
//   name: string;
//   description?: string;
//   shortDescription?: string;
//   basePrice: number;
//   discountedPrice?: number;
//   costPrice?: number;
//   sku?: string;
//   stockQuantity: number;
//   trackInventory: boolean;
//   allowBackorder: boolean;
//   weight?: number;
//   dimensions?: any;
//   tags: string[];
//   status: string;
//   isFeatured: boolean;
//   metaTitle?: string;
//   metaDescription?: string;
//   externalLink?: string;
//   instagramPostUrl?: string;
//   viewCount: number;
//   clickCount: number;
//   addToCartCount: number;
//   purchaseCount: number;
// }

// Order interfaces
// Temporarily commented out due to Document/BaseEntity conflicts
// export interface OrderDocument extends Document, BaseEntity {
//   storeId: string;
//   orderNumber: string;
//   customerName: string;
//   customerPhone: string;
//   customerEmail?: string;
//   shippingAddress: string;
//   shippingCity: string;
//   shippingState?: string;
//   shippingPincode: string;
//   shippingCountry: string;
//   subtotal: number;
//   shippingFee: number;
//   taxAmount: number;
//   discountAmount: number;
//   totalAmount: number;
//   paymentMethod?: string;
//   paymentStatus: string;
//   paymentId?: string;
//   status: string;
//   trackingNumber?: string;
//   courierPartner?: string;
//   estimatedDelivery?: Date;
//   whatsappNotified: boolean;
//   pingbotConversationId?: string;
//   customerNotes?: string;
//   adminNotes?: string;
//   confirmedAt?: Date;
//   shippedAt?: Date;
//   deliveredAt?: Date;
// }

// Analytics interfaces
export interface AnalyticsEvent {
  storeId: string;
  eventType: string;
  eventData?: any;
  sessionId?: string;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  productId?: string;
  timestamp: Date;
}

// File upload interfaces
export interface UploadedFile {
  url: string;
  thumbnailUrl?: string;
  fileSize: number;
  dimensions?: {
    width: number;
    height: number;
  };
  mimeType: string;
  originalName: string;
}

// API Response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
  errors?: any[];
  requestId?: string;
  timestamp: string;
  path: string;
  method: string;
}

// WhatsApp interfaces
export interface WhatsAppIntegration {
  storeId: string;
  phoneNumber: string;
  businessAccountId?: string;
  isVerified: boolean;
  verificationCode?: string;
  pingbotEnabled: boolean;
  autoRespondEnabled: boolean;
  forwardUnknownQueries: boolean;
  webhookUrl?: string;
  apiToken?: string;
  isActive: boolean;
  lastSync?: Date;
}

// Payment interfaces
export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  status: string;
  clientSecret?: string;
  paymentUrl?: string;
}

// Theme interfaces
export interface ThemeConfig {
  colors?: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
  };
  typography?: {
    fontFamily: string;
    headingWeight: string;
    bodyWeight: string;
  };
  layout?: {
    borderRadius: string;
    spacing: string;
  };
}
