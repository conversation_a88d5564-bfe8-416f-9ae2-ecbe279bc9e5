import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

import { StoresService } from './stores.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';
import { PaginationQuery } from '@common/interfaces';

// DTOs
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';

@ApiTags('Stores')
@Controller('stores')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new store' })
  @ApiResponse({ status: 201, description: 'Store created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 409, description: 'Store handle already exists' })
  async create(
    @CurrentUser() user: UserDocument,
    @Body() createStoreDto: CreateStoreDto,
  ) {
    const store = await this.storesService.create(user._id.toString(), createStoreDto);
    return {
      message: API_MESSAGES.STORE_CREATED,
      store,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all published stores' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ status: 200, description: 'Stores retrieved successfully' })
  async findAll(@Query() query: PaginationQuery) {
    const result = await this.storesService.findAll(query);
    return {
      message: 'Stores retrieved successfully',
      ...result,
    };
  }

  @Get('my-store')
  @ApiOperation({ summary: 'Get current user store' })
  @ApiResponse({ status: 200, description: 'Store retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async getMyStore(@CurrentUser() user: UserDocument) {
    const store = await this.storesService.findByUserId(user._id.toString());
    return {
      message: 'Store retrieved successfully',
      store,
    };
  }

  @Get('check-handle/:handle')
  @ApiOperation({ summary: 'Check if store handle is available' })
  @ApiParam({ name: 'handle', description: 'Store handle to check' })
  @ApiResponse({ status: 200, description: 'Handle availability checked' })
  async checkHandle(@Param('handle') handle: string) {
    const result = await this.storesService.checkHandleAvailability(handle);
    return {
      message: 'Handle availability checked',
      ...result,
    };
  }

  @Get(':storeId')
  @ApiOperation({ summary: 'Get store by ID' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Store retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async findById(@Param('storeId') storeId: string) {
    const store = await this.storesService.findById(storeId);
    return {
      message: 'Store retrieved successfully',
      store,
    };
  }

  @Put(':storeId')
  @ApiOperation({ summary: 'Update store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Store updated successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async update(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() updateStoreDto: UpdateStoreDto,
  ) {
    const store = await this.storesService.update(storeId, user._id.toString(), updateStoreDto);
    return {
      message: API_MESSAGES.STORE_UPDATED,
      store,
    };
  }

  @Delete(':storeId')
  @ApiOperation({ summary: 'Delete store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Store deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  @ApiResponse({ status: 404, description: 'Store not found' })
  async delete(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    await this.storesService.delete(storeId, user._id.toString());
    return {
      message: API_MESSAGES.STORE_DELETED,
    };
  }

  @Post(':storeId/upload-logo')
  @UseInterceptors(FileInterceptor('logo'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload store logo' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Logo uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file' })
  async uploadLogo(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @UploadedFile() file: Express.Multer.File,
  ) {
    // In a real implementation, upload to S3/Cloudinary
    const logoUrl = `https://example.com/logos/${storeId}.jpg`;
    
    const store = await this.storesService.uploadLogo(storeId, user._id.toString(), logoUrl);
    return {
      message: API_MESSAGES.FILE_UPLOADED,
      store,
      logoUrl,
    };
  }

  @Post(':storeId/upload-cover')
  @UseInterceptors(FileInterceptor('cover'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload store cover image' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Cover image uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file' })
  async uploadCover(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @UploadedFile() file: Express.Multer.File,
  ) {
    // In a real implementation, upload to S3/Cloudinary
    const coverImageUrl = `https://example.com/covers/${storeId}.jpg`;

    const store = await this.storesService.uploadCoverImage(storeId, user._id.toString(), coverImageUrl);
    return {
      message: API_MESSAGES.FILE_UPLOADED,
      store,
      coverImageUrl,
    };
  }

  @Get(':storeId/onboarding-status')
  @ApiOperation({ summary: 'Get store onboarding status' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Onboarding status retrieved' })
  async getOnboardingStatus(@Param('storeId') storeId: string) {
    const store = await this.storesService.findById(storeId);
    return {
      message: 'Onboarding status retrieved',
      completed: store.onboardingCompleted,
      steps: store.onboardingSteps,
    };
  }

  @Put(':storeId/onboarding-step')
  @ApiOperation({ summary: 'Update onboarding step' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Onboarding step updated' })
  async updateOnboardingStep(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { step: string; completed: boolean },
  ) {
    const store = await this.storesService.updateOnboardingStep(
      storeId,
      user._id.toString(),
      body.step,
      body.completed,
    );
    return {
      message: 'Onboarding step updated',
      onboardingSteps: store.onboardingSteps,
    };
  }

  @Post(':storeId/complete-onboarding')
  @ApiOperation({ summary: 'Complete store onboarding' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Onboarding completed successfully' })
  async completeOnboarding(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const store = await this.storesService.completeOnboarding(storeId, user._id.toString());
    return {
      message: API_MESSAGES.ONBOARDING_COMPLETED,
      store,
    };
  }
}
