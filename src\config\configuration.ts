export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  
  // Database
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/pingstore',
  },

  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
  },

  // Authentication
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
      expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    },
    refreshToken: {
      secret: process.env.REFRESH_TOKEN_SECRET || 'your-super-secret-refresh-key',
      expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    },
    bcrypt: {
      saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS, 10) || 12,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackUrl: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',
    },
  },

  // File Storage
  storage: {
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET,
    },
    cloudinary: {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME,
      apiKey: process.env.CLOUDINARY_API_KEY,
      apiSecret: process.env.CLOUDINARY_API_SECRET,
    },
  },

  // Payment Gateways
  payments: {
    razorpay: {
      keyId: process.env.RAZORPAY_KEY_ID,
      keySecret: process.env.RAZORPAY_KEY_SECRET,
      webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET,
    },
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    },
  },

  // WhatsApp/PingBot Integration
  whatsapp: {
    pingbot: {
      apiUrl: process.env.PINGBOT_API_URL || 'https://api.pingbot.com',
      apiKey: process.env.PINGBOT_API_KEY,
      webhookSecret: process.env.PINGBOT_WEBHOOK_SECRET,
      centralNumber: process.env.PINGBOT_CENTRAL_NUMBER || '+91-XXXXX-XXXXX',
    },
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER,
    },
  },

  // Email Service
  email: {
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT, 10) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      user: process.env.SMTP_USER,
      password: process.env.SMTP_PASSWORD,
    },
    sendgrid: {
      apiKey: process.env.SENDGRID_API_KEY,
      fromEmail: process.env.SENDGRID_FROM_EMAIL,
    },
  },

  // Application Settings
  app: {
    name: 'PingStore',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3001',
    apiUrl: process.env.API_URL || 'http://localhost:3000',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3001'],
  },

  // Rate Limiting
  rateLimit: {
    ttl: parseInt(process.env.RATE_LIMIT_TTL, 10) || 60, // seconds
    limit: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100, // requests per TTL
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
  },

  // Analytics
  analytics: {
    googleAnalytics: {
      trackingId: process.env.GA_TRACKING_ID,
    },
    mixpanel: {
      token: process.env.MIXPANEL_TOKEN,
    },
  },

  // Feature Flags
  features: {
    enableAnalytics: process.env.ENABLE_ANALYTICS !== 'false',
    enableWhatsApp: process.env.ENABLE_WHATSAPP !== 'false',
    enablePayments: process.env.ENABLE_PAYMENTS !== 'false',
    enableReviews: process.env.ENABLE_REVIEWS !== 'false',
  },
});
