import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Store, StoreDocument } from '@database/schemas/store.schema';
import { User, UserDocument } from '@database/schemas/user.schema';
import { ERROR_CODES } from '@common/constants';
import { PaginationQuery, PaginationResult } from '@common/interfaces';

// DTOs
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';

@Injectable()
export class StoresService {
  private readonly logger = new Logger(StoresService.name);

  constructor(
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async create(userId: string, createStoreDto: CreateStoreDto): Promise<StoreDocument> {
    const { name, handle, description, bio } = createStoreDto;

    // Check if handle already exists
    const existingStore = await this.storeModel.findOne({ handle });
    if (existingStore) {
      throw new ConflictException({
        message: 'Store handle already exists',
        code: ERROR_CODES.DUPLICATE_VALUE,
      });
    }

    // Check if user already has a store (for now, limit to one store per user)
    const userStore = await this.storeModel.findOne({ userId });
    if (userStore) {
      throw new ConflictException({
        message: 'User already has a store',
        code: ERROR_CODES.DUPLICATE_VALUE,
      });
    }

    const store = new this.storeModel({
      userId,
      name,
      handle: handle.toLowerCase(),
      description,
      bio,
    });

    await store.save();

    this.logger.log(`Store created: ${handle} by user ${userId}`);
    return store;
  }

  async findAll(query: PaginationQuery): Promise<PaginationResult<StoreDocument>> {
    const { page = 1, limit = 20, search, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    const skip = (page - 1) * limit;

    // Build search filter
    const filter: any = { isPublished: true, isActive: true };
    if (search) {
      filter.$text = { $search: search };
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [stores, total] = await Promise.all([
      this.storeModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('userId', 'fullName email')
        .exec(),
      this.storeModel.countDocuments(filter),
    ]);

    return {
      data: stores,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(storeId: string): Promise<StoreDocument> {
    const store = await this.storeModel
      .findById(storeId)
      .populate('userId', 'fullName email');

    if (!store) {
      throw new NotFoundException({
        message: 'Store not found',
        code: ERROR_CODES.STORE_NOT_FOUND,
      });
    }

    return store;
  }

  async findByHandle(handle: string): Promise<StoreDocument> {
    const store = await this.storeModel
      .findOne({ handle: handle.toLowerCase() })
      .populate('userId', 'fullName email');

    if (!store) {
      throw new NotFoundException({
        message: 'Store not found',
        code: ERROR_CODES.STORE_NOT_FOUND,
      });
    }

    return store;
  }

  async findByUserId(userId: string): Promise<StoreDocument | null> {
    return this.storeModel
      .findOne({ userId })
      .populate('userId', 'fullName email');
  }

  async update(storeId: string, userId: string, updateStoreDto: UpdateStoreDto): Promise<StoreDocument> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Check if handle already exists (excluding current store)
    if (updateStoreDto.handle) {
      const existingStore = await this.storeModel.findOne({
        handle: updateStoreDto.handle.toLowerCase(),
        _id: { $ne: storeId },
      });

      if (existingStore) {
        throw new ConflictException({
          message: 'Store handle already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    // Update store
    const updatedStore = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        ...updateStoreDto,
        ...(updateStoreDto.handle && { handle: updateStoreDto.handle.toLowerCase() }),
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('userId', 'fullName email');

    this.logger.log(`Store updated: ${store.handle} by user ${userId}`);
    return updatedStore!;
  }

  async delete(storeId: string, userId: string): Promise<void> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only delete your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    await this.storeModel.findByIdAndDelete(storeId);

    this.logger.log(`Store deleted: ${store.handle} by user ${userId}`);
  }

  async checkHandleAvailability(handle: string): Promise<{ available: boolean }> {
    const existingStore = await this.storeModel.findOne({ 
      handle: handle.toLowerCase() 
    });

    return { available: !existingStore };
  }

  async uploadLogo(storeId: string, userId: string, logoUrl: string): Promise<StoreDocument> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const updatedStore = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        logoUrl,
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('userId', 'fullName email');

    this.logger.log(`Logo uploaded for store: ${store.handle}`);
    return updatedStore!;
  }

  async uploadCoverImage(storeId: string, userId: string, coverImageUrl: string): Promise<StoreDocument> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const updatedStore = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        coverImageUrl,
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('userId', 'fullName email');

    this.logger.log(`Cover image uploaded for store: ${store.handle}`);
    return updatedStore!;
  }

  async updateOnboardingStep(
    storeId: string, 
    userId: string, 
    step: string, 
    completed: boolean
  ): Promise<StoreDocument> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const onboardingSteps = { ...store.onboardingSteps };
    onboardingSteps[step] = completed;

    // Check if all steps are completed
    const allStepsCompleted = Object.values(onboardingSteps).every(Boolean);

    const updatedStore = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        onboardingSteps,
        onboardingCompleted: allStepsCompleted,
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('userId', 'fullName email');

    this.logger.log(`Onboarding step updated for store: ${store.handle} - ${step}: ${completed}`);
    return updatedStore!;
  }

  async completeOnboarding(storeId: string, userId: string): Promise<StoreDocument> {
    const store = await this.findById(storeId);

    // Check ownership
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const updatedStore = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        onboardingCompleted: true,
        isPublished: true, // Auto-publish when onboarding is complete
        updatedAt: new Date(),
      },
      { new: true },
    ).populate('userId', 'fullName email');

    this.logger.log(`Onboarding completed for store: ${store.handle}`);
    return updatedStore!;
  }

  async incrementViews(storeId: string): Promise<void> {
    await this.storeModel.findByIdAndUpdate(storeId, {
      $inc: { totalViews: 1 },
    });
  }

  async updateAnalytics(storeId: string, orderAmount?: number): Promise<void> {
    const updateData: any = {};

    if (orderAmount) {
      updateData.$inc = {
        totalOrders: 1,
        totalRevenue: orderAmount,
      };
    }

    if (Object.keys(updateData).length > 0) {
      await this.storeModel.findByIdAndUpdate(storeId, updateData);
    }
  }
}
