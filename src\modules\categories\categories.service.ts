import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Category, CategoryDocument } from '@database/schemas/category.schema';
import { Product, ProductDocument } from '@database/schemas/product.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES } from '@common/constants';
import { PaginationResult } from '@common/interfaces';

// DTOs
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@Injectable()
export class CategoriesService {
  private readonly logger = new Logger(CategoriesService.name);

  constructor(
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    private storesService: StoresService,
  ) {}

  async create(
    storeId: string,
    userId: string,
    createCategoryDto: CreateCategoryDto,
  ): Promise<CategoryDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only create categories in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Check if category name already exists in this store
    const existingCategory = await this.categoryModel.findOne({
      storeId,
      name: { $regex: new RegExp(`^${createCategoryDto.name}$`, 'i') },
    });

    if (existingCategory) {
      throw new BadRequestException({
        message: 'Category with this name already exists',
        code: ERROR_CODES.DUPLICATE_VALUE,
      });
    }

    const category = new this.categoryModel({
      storeId,
      ...createCategoryDto,
    });

    await category.save();

    this.logger.log(`Category created: ${category.name} in store ${storeId}`);
    return category;
  }

  async findAll(
    storeId: string,
    userId: string,
    query: any,
  ): Promise<PaginationResult<CategoryDocument>> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view categories from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { storeId };

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [categories, total] = await Promise.all([
      this.categoryModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.categoryModel.countDocuments(filter),
    ]);

    // Add product count for each category
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const productCount = await this.productModel.countDocuments({
          storeId,
          categoryId: category._id,
        });
        return {
          ...category.toObject(),
          productCount,
        };
      }),
    );

    return {
      data: categoriesWithCount as any,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(
    categoryId: string,
    storeId: string,
    userId: string,
  ): Promise<CategoryDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view categories from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const category = await this.categoryModel
      .findOne({ _id: categoryId, storeId })
      .exec();

    if (!category) {
      throw new NotFoundException({
        message: 'Category not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    return category;
  }

  async update(
    categoryId: string,
    storeId: string,
    userId: string,
    updateCategoryDto: UpdateCategoryDto,
  ): Promise<CategoryDocument> {
    const category = await this.findById(categoryId, storeId, userId);

    // Check if new name conflicts with existing category
    if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
      const existingCategory = await this.categoryModel.findOne({
        storeId,
        name: { $regex: new RegExp(`^${updateCategoryDto.name}$`, 'i') },
        _id: { $ne: categoryId },
      });

      if (existingCategory) {
        throw new BadRequestException({
          message: 'Category with this name already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    const updatedCategory = await this.categoryModel
      .findByIdAndUpdate(
        categoryId,
        {
          ...updateCategoryDto,
          updatedAt: new Date(),
        },
        { new: true },
      )
      .exec();

    this.logger.log(`Category updated: ${category.name} in store ${storeId}`);
    return updatedCategory!;
  }

  async remove(categoryId: string, storeId: string, userId: string): Promise<void> {
    const category = await this.findById(categoryId, storeId, userId);

    // Check if category has products
    const productCount = await this.productModel.countDocuments({
      storeId,
      categoryId,
    });

    if (productCount > 0) {
      throw new BadRequestException({
        message: `Cannot delete category. It has ${productCount} products. Please move or delete the products first.`,
        code: ERROR_CODES.INVALID_OPERATION,
      });
    }

    await this.categoryModel.findByIdAndDelete(categoryId);

    this.logger.log(`Category deleted: ${category.name} from store ${storeId}`);
  }

  async findByStoreHandle(storeHandle: string): Promise<CategoryDocument[]> {
    // Find store by handle
    const store = await this.storeModel.findOne({
      handle: storeHandle.toLowerCase(),
      isActive: true,
      isPublished: true,
    });

    if (!store) {
      throw new NotFoundException({
        message: 'Store not found',
        code: ERROR_CODES.STORE_NOT_FOUND,
      });
    }

    return this.categoryModel
      .find({
        storeId: store._id,
        isActive: true,
      })
      .sort({ name: 1 })
      .exec();
  }

  async getStats(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view stats from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [totalCategories, activeCategories, categoriesWithProducts] = await Promise.all([
      this.categoryModel.countDocuments({ storeId }),
      this.categoryModel.countDocuments({ storeId, isActive: true }),
      this.categoryModel.aggregate([
        { $match: { storeId: store._id } },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: 'categoryId',
            as: 'products',
          },
        },
        {
          $project: {
            name: 1,
            productCount: { $size: '$products' },
          },
        },
        { $sort: { productCount: -1 } },
      ]),
    ]);

    return {
      totalCategories,
      activeCategories,
      inactiveCategories: totalCategories - activeCategories,
      categoriesWithProducts,
    };
  }
}
