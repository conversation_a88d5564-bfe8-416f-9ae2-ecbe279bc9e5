import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';
import { PRODUCT_STATUS } from '@common/constants';

export type ProductDocument = Product & Document;

@Schema({
  timestamps: true,
  collection: 'products',
})
export class Product extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Category',
    index: true
  })
  categoryId?: Types.ObjectId;

  // Basic Info
  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 200
  })
  name: string;

  @Prop({ trim: true, maxlength: 2000 })
  description?: string;

  @Prop({ trim: true, maxlength: 500 })
  shortDescription?: string;

  // Pricing
  @Prop({ 
    required: true, 
    type: Number,
    min: 0,
    max: 1000000
  })
  basePrice: number;

  @Prop({ 
    type: Number,
    min: 0,
    max: 1000000
  })
  discountedPrice?: number;

  @Prop({ 
    type: Number,
    min: 0,
    max: 1000000
  })
  costPrice?: number;

  // Inventory
  @Prop({ 
    trim: true,
    maxlength: 100
  })
  sku?: string;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 100000
  })
  stockQuantity: number;

  @Prop({ default: true })
  trackInventory: boolean;

  @Prop({ default: false })
  allowBackorder: boolean;

  // Product Details
  @Prop({ 
    type: Number,
    min: 0,
    max: 50000 // 50kg max
  })
  weight?: number; // in grams

  @Prop({ 
    type: Object,
    default: {}
  })
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };

  @Prop({ 
    type: [String],
    default: [],
    validate: {
      validator: function(tags: string[]) {
        return tags.length <= 20; // Max 20 tags
      },
      message: 'Maximum 20 tags allowed'
    }
  })
  tags: string[];

  // Status & Visibility
  @Prop({ 
    default: PRODUCT_STATUS.DRAFT,
    enum: Object.values(PRODUCT_STATUS)
  })
  status: string;

  @Prop({ default: false })
  isFeatured: boolean;

  // SEO
  @Prop({ trim: true, maxlength: 255 })
  metaTitle?: string;

  @Prop({ trim: true, maxlength: 500 })
  metaDescription?: string;

  // External Links
  @Prop({ 
    trim: true,
    match: [/^https?:\/\/.+/, 'Please enter a valid URL']
  })
  externalLink?: string;

  @Prop({ 
    trim: true,
    match: [/^https?:\/\/.+/, 'Please enter a valid Instagram URL']
  })
  instagramPostUrl?: string;

  // Analytics
  @Prop({ type: Number, default: 0, min: 0 })
  viewCount: number;

  @Prop({ type: Number, default: 0, min: 0 })
  clickCount: number;

  @Prop({ type: Number, default: 0, min: 0 })
  addToCartCount: number;

  @Prop({ type: Number, default: 0, min: 0 })
  purchaseCount: number;

  // Virtual fields
  images?: any[];
  variants?: any[];
  reviews?: any[];
  averageRating?: number;
  reviewCount?: number;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

// Indexes
ProductSchema.index({ storeId: 1 });
ProductSchema.index({ storeId: 1, status: 1 });
ProductSchema.index({ storeId: 1, categoryId: 1 });
ProductSchema.index({ storeId: 1, isFeatured: 1 });
ProductSchema.index({ storeId: 1, status: 1, isFeatured: 1 });
ProductSchema.index({ basePrice: 1 });
ProductSchema.index({ discountedPrice: 1 });
ProductSchema.index({ createdAt: -1 });
ProductSchema.index({ viewCount: -1 });
ProductSchema.index({ purchaseCount: -1 });
ProductSchema.index({ tags: 1 });

// Text search index
ProductSchema.index({ 
  name: 'text', 
  description: 'text', 
  shortDescription: 'text',
  tags: 'text'
});

// Compound indexes for common queries
ProductSchema.index({ storeId: 1, status: 1, createdAt: -1 });
ProductSchema.index({ storeId: 1, categoryId: 1, status: 1 });

// Virtual populate
ProductSchema.virtual('images', {
  ref: 'ProductImage',
  localField: '_id',
  foreignField: 'productId',
  options: { sort: { sortOrder: 1 } }
});

ProductSchema.virtual('variants', {
  ref: 'ProductVariant',
  localField: '_id',
  foreignField: 'productId',
  options: { sort: { sortOrder: 1 } }
});

ProductSchema.virtual('reviews', {
  ref: 'Review',
  localField: '_id',
  foreignField: 'productId',
});

// Pre-save middleware
ProductSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Ensure discounted price is less than base price
  if (this.discountedPrice && this.discountedPrice >= this.basePrice) {
    this.discountedPrice = undefined;
  }
  
  // Clean up tags
  if (this.isModified('tags')) {
    this.tags = this.tags
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0)
      .slice(0, 20); // Max 20 tags
  }
  
  next();
});

// Instance methods
ProductSchema.methods.getPublicData = function() {
  return {
    id: this._id,
    name: this.name,
    description: this.description,
    shortDescription: this.shortDescription,
    basePrice: this.basePrice,
    discountedPrice: this.discountedPrice,
    stockQuantity: this.trackInventory ? this.stockQuantity : null,
    weight: this.weight,
    dimensions: this.dimensions,
    tags: this.tags,
    isFeatured: this.isFeatured,
    externalLink: this.externalLink,
    instagramPostUrl: this.instagramPostUrl,
    viewCount: this.viewCount,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt,
  };
};

ProductSchema.methods.isInStock = function() {
  if (!this.trackInventory) return true;
  return this.stockQuantity > 0 || this.allowBackorder;
};

ProductSchema.methods.getEffectivePrice = function() {
  return this.discountedPrice || this.basePrice;
};

ProductSchema.methods.getDiscountPercentage = function() {
  if (!this.discountedPrice) return 0;
  return Math.round(((this.basePrice - this.discountedPrice) / this.basePrice) * 100);
};
