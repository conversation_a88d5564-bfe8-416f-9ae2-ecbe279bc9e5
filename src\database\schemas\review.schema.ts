import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export interface ReviewMethods {
  approve(): Promise<ReviewDocument>;
  reject(): Promise<ReviewDocument>;
  feature(): Promise<ReviewDocument>;
  unfeature(): Promise<ReviewDocument>;
  getPublicData(): any;
}

export type ReviewDocument = Review & Document<any, any, Review> & ReviewMethods & {
  _id: Types.ObjectId;
};

@Schema({
  timestamps: true,
  collection: 'reviews',
})
export class Review extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Customer', 
    required: true,
    index: true
  })
  customerId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Order', 
    required: true,
    index: true
  })
  orderId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Product',
    index: true
  })
  productId?: Types.ObjectId;

  // Review Content
  @Prop({ 
    required: true, 
    type: Number,
    min: 1,
    max: 5
  })
  rating: number;

  @Prop({ 
    trim: true,
    maxlength: 255
  })
  title?: string;

  @Prop({ 
    trim: true,
    maxlength: 2000
  })
  comment?: string;

  // Status
  @Prop({ default: false })
  isApproved: boolean;

  @Prop({ default: false })
  isFeatured: boolean;

  @Prop({ default: false })
  isVerifiedPurchase: boolean;

  // Store Response
  @Prop({ 
    trim: true,
    maxlength: 1000
  })
  storeResponse?: string;

  @Prop()
  respondedAt?: Date;

  // Review metadata
  @Prop({ 
    type: Object,
    default: {}
  })
  metadata?: {
    customerName?: string; // Snapshot at time of review
    productName?: string; // Snapshot at time of review
    orderNumber?: string; // Snapshot at time of review
  };

  // Moderation
  @Prop({ default: false })
  isFlagged: boolean;

  @Prop({ 
    trim: true,
    maxlength: 500
  })
  moderationNotes?: string;

  @Prop()
  moderatedAt?: Date;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'User'
  })
  moderatedBy?: Types.ObjectId;

  // Helpfulness tracking
  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  helpfulCount: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  notHelpfulCount: number;

  // Virtual fields
  customer?: any;
  product?: any;
  order?: any;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);

// Indexes
ReviewSchema.index({ storeId: 1 });
ReviewSchema.index({ storeId: 1, isApproved: 1 });
ReviewSchema.index({ storeId: 1, isFeatured: 1 });
ReviewSchema.index({ customerId: 1 });
ReviewSchema.index({ orderId: 1 });
ReviewSchema.index({ productId: 1 });
ReviewSchema.index({ rating: 1 });
ReviewSchema.index({ createdAt: -1 });
ReviewSchema.index({ helpfulCount: -1 });

// Compound indexes for common queries
ReviewSchema.index({ storeId: 1, isApproved: 1, createdAt: -1 });
ReviewSchema.index({ productId: 1, isApproved: 1, rating: -1 });
ReviewSchema.index({ storeId: 1, rating: 1, isApproved: 1 });

// Unique constraint to prevent duplicate reviews per order
ReviewSchema.index({ orderId: 1, customerId: 1 }, { unique: true });

// Text search index
ReviewSchema.index({ 
  title: 'text',
  comment: 'text'
});

// Virtual populate
ReviewSchema.virtual('customer', {
  ref: 'Customer',
  localField: 'customerId',
  foreignField: '_id',
  justOne: true,
});

ReviewSchema.virtual('product', {
  ref: 'Product',
  localField: 'productId',
  foreignField: '_id',
  justOne: true,
});

ReviewSchema.virtual('order', {
  ref: 'Order',
  localField: 'orderId',
  foreignField: '_id',
  justOne: true,
});

// Pre-save middleware
ReviewSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Set verified purchase flag
  if (this.isNew) {
    this.isVerifiedPurchase = true; // Since review is linked to an order
  }
  
  // Update responded timestamp
  if (this.isModified('storeResponse') && this.storeResponse) {
    this.respondedAt = new Date();
  }
  
  next();
});

// Post-save middleware to update product rating
ReviewSchema.post('save', async function(doc) {
  if (doc.productId && doc.isApproved) {
    await (this.constructor as any).updateProductRating(doc.productId);
  }
});

// Post-remove middleware to update product rating
ReviewSchema.post('findOneAndDelete', async function(doc) {
  if (doc && doc.productId) {
    await (this as any).model('Review').updateProductRating(doc.productId);
  }
});

// Static methods
ReviewSchema.statics.updateProductRating = async function(productId: Types.ObjectId) {
  const stats = await this.aggregate([
    { 
      $match: { 
        productId: productId, 
        isApproved: true 
      } 
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 }
      }
    }
  ]);

  const { averageRating = 0, totalReviews = 0 } = stats[0] || {};

  // Update the product with new rating
  const mongoose = require('mongoose');
  await mongoose.model('Product').findByIdAndUpdate(productId, {
    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
    reviewCount: totalReviews
  });
};

// Instance methods
ReviewSchema.methods.approve = function() {
  this.isApproved = true;
  this.moderatedAt = new Date();
  return this.save();
};

ReviewSchema.methods.reject = function() {
  this.isApproved = false;
  this.moderatedAt = new Date();
  return this.save();
};

ReviewSchema.methods.feature = function() {
  this.isFeatured = true;
  return this.save();
};

ReviewSchema.methods.unfeature = function() {
  this.isFeatured = false;
  return this.save();
};

ReviewSchema.methods.getPublicData = function() {
  return {
    id: this._id,
    rating: this.rating,
    title: this.title,
    comment: this.comment,
    customerName: this.metadata?.customerName || 'Anonymous',
    productName: this.metadata?.productName,
    storeResponse: this.storeResponse,
    respondedAt: this.respondedAt,
    isFeatured: this.isFeatured,
    isVerifiedPurchase: this.isVerifiedPurchase,
    helpfulCount: this.helpfulCount,
    createdAt: this.createdAt,
  };
};
