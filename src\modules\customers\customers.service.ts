import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Customer, CustomerDocument } from '@database/schemas/customer.schema';
import { Order, OrderDocument } from '@database/schemas/order.schema';
import { Review, ReviewDocument } from '@database/schemas/review.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES, CUSTOMER_SEGMENTS } from '@common/constants';
import { PaginationResult } from '@common/interfaces';

@Injectable()
export class CustomersService {
  private readonly logger = new Logger(CustomersService.name);

  constructor(
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    private storesService: StoresService,
  ) {}

  async findAll(
    storeId: string,
    userId: string,
    query: any,
  ): Promise<PaginationResult<CustomerDocument>> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view customers from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      segment,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { storeId };

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (segment) {
      filter.customerSegment = segment;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [customers, total] = await Promise.all([
      this.customerModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.customerModel.countDocuments(filter),
    ]);

    return {
      data: customers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(customerId: string, storeId: string, userId: string): Promise<CustomerDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view customers from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const customer = await this.customerModel
      .findOne({ _id: customerId, storeId })
      .exec();

    if (!customer) {
      throw new NotFoundException({
        message: 'Customer not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    return customer;
  }

  async getCustomerDetails(
    customerId: string,
    storeId: string,
    userId: string,
  ): Promise<any> {
    const customer = await this.findById(customerId, storeId, userId);

    // Get customer orders
    const orders = await this.orderModel
      .find({ storeId, customerPhone: customer.phone })
      .sort({ createdAt: -1 })
      .limit(10)
      .exec();

    // Get customer reviews
    const reviews = await this.reviewModel
      .find({ storeId, customerId })
      .populate('productId', 'name')
      .sort({ createdAt: -1 })
      .limit(5)
      .exec();

    // Calculate additional stats
    const stats = await this.calculateCustomerStats(storeId, customer.phone);

    return {
      customer,
      orders,
      reviews,
      stats,
    };
  }

  async updateCustomer(
    customerId: string,
    storeId: string,
    userId: string,
    updateData: any,
  ): Promise<CustomerDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update customers in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const customer = await this.findById(customerId, storeId, userId);

    const updatedCustomer = await this.customerModel.findByIdAndUpdate(
      customerId,
      {
        ...updateData,
        updatedAt: new Date(),
      },
      { new: true },
    );

    this.logger.log(`Customer updated: ${customer.name} in store ${storeId}`);
    return updatedCustomer!;
  }

  async addCustomerTags(
    customerId: string,
    storeId: string,
    userId: string,
    tags: string[],
  ): Promise<CustomerDocument> {
    const customer = await this.findById(customerId, storeId, userId);

    const updatedCustomer = await this.customerModel.findByIdAndUpdate(
      customerId,
      {
        $addToSet: { tags: { $each: tags } },
        updatedAt: new Date(),
      },
      { new: true },
    );

    this.logger.log(`Tags added to customer: ${customer.name}`);
    return updatedCustomer!;
  }

  async removeCustomerTags(
    customerId: string,
    storeId: string,
    userId: string,
    tags: string[],
  ): Promise<CustomerDocument> {
    const customer = await this.findById(customerId, storeId, userId);

    const updatedCustomer = await this.customerModel.findByIdAndUpdate(
      customerId,
      {
        $pullAll: { tags },
        updatedAt: new Date(),
      },
      { new: true },
    );

    this.logger.log(`Tags removed from customer: ${customer.name}`);
    return updatedCustomer!;
  }

  async getCustomerSegments(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view segments from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const segments = await this.customerModel.aggregate([
      { $match: { storeId: store._id } },
      {
        $group: {
          _id: '$customerSegment',
          count: { $sum: 1 },
          totalSpent: { $sum: '$totalSpent' },
          averageOrderValue: { $avg: '$averageOrderValue' },
        },
      },
      {
        $project: {
          segment: '$_id',
          count: 1,
          totalSpent: 1,
          averageOrderValue: { $round: ['$averageOrderValue', 2] },
          _id: 0,
        },
      },
    ]);

    return segments;
  }

  async getTopCustomers(
    storeId: string,
    userId: string,
    limit: number = 10,
  ): Promise<CustomerDocument[]> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view customers from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    return this.customerModel
      .find({ storeId })
      .sort({ totalSpent: -1 })
      .limit(limit)
      .exec();
  }

  async getCustomerAnalytics(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      totalCustomers,
      newCustomersThisMonth,
      averageOrderValue,
      customerLifetimeValue,
      repeatCustomers,
    ] = await Promise.all([
      this.customerModel.countDocuments({ storeId }),
      this.getNewCustomersThisMonth(storeId),
      this.getAverageOrderValue(storeId),
      this.getAverageCustomerLifetimeValue(storeId),
      this.getRepeatCustomersCount(storeId),
    ]);

    return {
      totalCustomers,
      newCustomersThisMonth,
      averageOrderValue,
      customerLifetimeValue,
      repeatCustomers,
      repeatCustomerRate: totalCustomers > 0 ? (repeatCustomers / totalCustomers) * 100 : 0,
    };
  }

  async exportCustomers(storeId: string, userId: string): Promise<any[]> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only export customers from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const customers = await this.customerModel
      .find({ storeId })
      .sort({ createdAt: -1 })
      .exec();

    return customers.map(customer => ({
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      totalOrders: customer.totalOrders,
      totalSpent: customer.totalSpent,
      averageOrderValue: customer.averageOrderValue,
      customerSegment: customer.customerSegment,
      lastOrderDate: customer.lastOrderDate,
      createdAt: customer.createdAt,
      city: customer.city,
      state: customer.state,
      country: customer.country,
      tags: customer.tags.join(', '),
    }));
  }

  // Private helper methods
  private async calculateCustomerStats(storeId: string, phone: string): Promise<any> {
    const orders = await this.orderModel.find({ storeId, customerPhone: phone });
    
    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    
    const lastOrder = orders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
    const daysSinceLastOrder = lastOrder 
      ? Math.floor((Date.now() - lastOrder.createdAt.getTime()) / (1000 * 60 * 60 * 24))
      : null;

    return {
      totalOrders,
      totalSpent,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100,
      lastOrderDate: lastOrder?.createdAt,
      daysSinceLastOrder,
    };
  }

  private async getNewCustomersThisMonth(storeId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    return this.customerModel.countDocuments({
      storeId,
      createdAt: { $gte: startOfMonth },
    });
  }

  private async getAverageOrderValue(storeId: string): Promise<number> {
    const result = await this.customerModel.aggregate([
      { $match: { storeId } },
      { $group: { _id: null, avgOrderValue: { $avg: '$averageOrderValue' } } },
    ]);

    return result[0]?.avgOrderValue || 0;
  }

  private async getAverageCustomerLifetimeValue(storeId: string): Promise<number> {
    const result = await this.customerModel.aggregate([
      { $match: { storeId } },
      { $group: { _id: null, avgLifetimeValue: { $avg: '$totalSpent' } } },
    ]);

    return result[0]?.avgLifetimeValue || 0;
  }

  private async getRepeatCustomersCount(storeId: string): Promise<number> {
    return this.customerModel.countDocuments({
      storeId,
      totalOrders: { $gt: 1 },
    });
  }
}
