import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';

import { WhatsappService } from './whatsapp.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { Public, CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { ConfigureWhatsAppDto } from './dto/configure-whatsapp.dto';

@ApiTags('WhatsApp')
@Controller('whatsapp')
export class WhatsappController {
  constructor(private readonly whatsappService: WhatsappService) {}

  // Store owner endpoints (auth required)
  @Put('stores/:storeId/configure')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Configure WhatsApp integration for store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'WhatsApp configured successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async configureWhatsApp(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() configureDto: ConfigureWhatsAppDto,
  ) {
    const integration = await this.whatsappService.configureWhatsApp(
      storeId,
      user._id.toString(),
      configureDto,
    );
    return {
      message: API_MESSAGES.WHATSAPP_CONNECTED,
      integration: {
        centralNumber: integration.phoneNumber,
        isActive: integration.isActive,
        settings: {
          pingbotEnabled: integration.pingbotEnabled,
          autoRespondEnabled: integration.autoRespondEnabled,
          orderNotificationsEnabled: integration.orderNotificationsEnabled,
          paymentNotificationsEnabled: integration.paymentNotificationsEnabled,
          shippingNotificationsEnabled: integration.shippingNotificationsEnabled,
        },
      },
    };
  }

  @Get('stores/:storeId/config')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get WhatsApp configuration for store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'WhatsApp config retrieved successfully' })
  @ApiResponse({ status: 404, description: 'WhatsApp not configured' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getWhatsAppConfig(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const integration = await this.whatsappService.getWhatsAppConfig(storeId, user._id.toString());
    return {
      message: 'WhatsApp config retrieved successfully',
      integration: {
        centralNumber: integration.phoneNumber,
        isActive: integration.isActive,
        storeOwnerPhone: integration.storeOwnerPhone,
        businessDescription: integration.businessDescription,
        settings: {
          pingbotEnabled: integration.pingbotEnabled,
          autoRespondEnabled: integration.autoRespondEnabled,
          forwardUnknownQueries: integration.forwardUnknownQueries,
          orderNotificationsEnabled: integration.orderNotificationsEnabled,
          paymentNotificationsEnabled: integration.paymentNotificationsEnabled,
          shippingNotificationsEnabled: integration.shippingNotificationsEnabled,
        },
        lastSync: integration.lastSync,
      },
    };
  }

  @Get('stores/:storeId/stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get WhatsApp statistics for store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'WhatsApp stats retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getWhatsAppStats(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const stats = await this.whatsappService.getWhatsAppStats(storeId, user._id.toString());
    return {
      message: 'WhatsApp stats retrieved successfully',
      stats,
    };
  }

  // Webhook endpoint (public - for PingBot)
  @Post('webhook')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook for PingBot messages' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async handleWebhook(@Body() payload: any) {
    await this.whatsappService.handleWebhook(payload);
    return {
      success: true,
    };
  }

  // Order notification trigger (internal use)
  @Post('orders/:orderId/notify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Send order notification via WhatsApp' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Notification sent successfully' })
  async sendOrderNotification(@Param('orderId') orderId: string) {
    await this.whatsappService.sendOrderNotification(orderId);
    return {
      message: 'Order notification sent successfully',
    };
  }

  @Post('orders/:orderId/shipping-notify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Send shipping notification via WhatsApp' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Shipping notification sent successfully' })
  async sendShippingNotification(@Param('orderId') orderId: string) {
    await this.whatsappService.sendShippingNotification(orderId);
    return {
      message: 'Shipping notification sent successfully',
    };
  }
}
