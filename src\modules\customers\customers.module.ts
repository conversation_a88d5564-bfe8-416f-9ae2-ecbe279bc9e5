import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { CustomersController } from './customers.controller';
import { CustomersService } from './customers.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { Customer, CustomerSchema } from '@database/schemas/customer.schema';
import { Order, OrderSchema } from '@database/schemas/order.schema';
import { Review, ReviewSchema } from '@database/schemas/review.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Customer.name, schema: CustomerSchema },
      { name: Order.name, schema: OrderSchema },
      { name: Review.name, schema: ReviewSchema },
      { name: Store.name, schema: StoreSchema },
    ]),
    StoresModule,
  ],
  controllers: [CustomersController],
  providers: [CustomersService],
  exports: [CustomersService],
})
export class CustomersModule {}
