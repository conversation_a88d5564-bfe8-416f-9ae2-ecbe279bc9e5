const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Sending: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 10000
    });
    console.log(`✅ Response: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, 3000));
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

async function testDebugFlow() {
  console.log('🧪 TESTING WITH DETAILED DEBUG LOGS\n');
  console.log('============================================================\n');

  console.log('1️⃣ Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n2️⃣ Testing template that should work...');
  await sendMessage('track');
  
  console.log('\n3️⃣ Testing template that should work...');
  await sendMessage('status');
  
  console.log('\n4️⃣ Testing template that should work...');
  await sendMessage('support');

  console.log('\n🎉 Debug flow completed!');
  console.log('📊 Check server logs for detailed debug information');
}

testDebugFlow().catch(console.error);
