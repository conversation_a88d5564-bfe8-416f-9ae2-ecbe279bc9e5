import {
  Injectable,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { AnalyticsEvent, AnalyticsEventDocument } from '@database/schemas/analytics-event.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';
import { Product, ProductDocument } from '@database/schemas/product.schema';
import { Order, OrderDocument } from '@database/schemas/order.schema';
import { Customer, CustomerDocument } from '@database/schemas/customer.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES, ANALYTICS_EVENTS } from '@common/constants';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    @InjectModel(AnalyticsEvent.name) private analyticsEventModel: Model<AnalyticsEventDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    private storesService: StoresService,
  ) {}

  async getOverviewStats(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      totalViews,
      totalOrders,
      totalRevenue,
      totalCustomers,
      conversionRate,
    ] = await Promise.all([
      this.getTotalViews(storeId),
      this.getTotalOrders(storeId),
      this.getTotalRevenue(storeId),
      this.getTotalCustomers(storeId),
      this.getConversionRate(storeId),
    ]);

    return {
      totalViews,
      totalOrders,
      totalRevenue,
      totalCustomers,
      conversionRate,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
    };
  }

  async getTrafficAnalytics(
    storeId: string,
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      dailyViews,
      trafficSources,
      topPages,
      deviceBreakdown,
    ] = await Promise.all([
      this.getDailyViews(storeId, startDate, endDate),
      this.getTrafficSources(storeId, startDate, endDate),
      this.getTopPages(storeId, startDate, endDate),
      this.getDeviceBreakdown(storeId, startDate, endDate),
    ]);

    return {
      dailyViews,
      trafficSources,
      topPages,
      deviceBreakdown,
    };
  }

  async getSalesAnalytics(
    storeId: string,
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      dailySales,
      topProducts,
      salesByCategory,
      orderStatusBreakdown,
    ] = await Promise.all([
      this.getDailySales(storeId, startDate, endDate),
      this.getTopProducts(storeId, startDate, endDate),
      this.getSalesByCategory(storeId, startDate, endDate),
      this.getOrderStatusBreakdown(storeId, startDate, endDate),
    ]);

    return {
      dailySales,
      topProducts,
      salesByCategory,
      orderStatusBreakdown,
    };
  }

  async getProductAnalytics(
    storeId: string,
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      topViewedProducts,
      topSellingProducts,
      productConversionRates,
      lowStockProducts,
    ] = await Promise.all([
      this.getTopViewedProducts(storeId, startDate, endDate),
      this.getTopSellingProducts(storeId, startDate, endDate),
      this.getProductConversionRates(storeId, startDate, endDate),
      this.getLowStockProducts(storeId),
    ]);

    return {
      topViewedProducts,
      topSellingProducts,
      productConversionRates,
      lowStockProducts,
    };
  }

  async getCustomerAnalytics(
    storeId: string,
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view analytics from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const [
      newCustomers,
      returningCustomers,
      customerSegments,
      topCustomers,
      customerRetention,
    ] = await Promise.all([
      this.getNewCustomers(storeId, startDate, endDate),
      this.getReturningCustomers(storeId, startDate, endDate),
      this.getCustomerSegments(storeId),
      this.getTopCustomers(storeId, startDate, endDate),
      this.getCustomerRetention(storeId, startDate, endDate),
    ]);

    return {
      newCustomers,
      returningCustomers,
      customerSegments,
      topCustomers,
      customerRetention,
    };
  }

  // Private helper methods
  private async getTotalViews(storeId: string): Promise<number> {
    return this.analyticsEventModel.countDocuments({
      storeId,
      eventType: ANALYTICS_EVENTS.STORE_VIEW,
    });
  }

  private async getTotalOrders(storeId: string): Promise<number> {
    return this.orderModel.countDocuments({ storeId });
  }

  private async getTotalRevenue(storeId: string): Promise<number> {
    const result = await this.orderModel.aggregate([
      { $match: { storeId: storeId } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } },
    ]);
    return result[0]?.total || 0;
  }

  private async getTotalCustomers(storeId: string): Promise<number> {
    return this.customerModel.countDocuments({ storeId });
  }

  private async getConversionRate(storeId: string): Promise<number> {
    const [views, orders] = await Promise.all([
      this.getTotalViews(storeId),
      this.getTotalOrders(storeId),
    ]);
    return views > 0 ? (orders / views) * 100 : 0;
  }

  private async getDailyViews(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.analyticsEventModel.aggregate([
      {
        $match: {
          storeId: storeId,
          eventType: ANALYTICS_EVENTS.STORE_VIEW,
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' },
          },
          views: { $sum: 1 },
          uniqueVisitors: { $addToSet: '$sessionId' },
        },
      },
      {
        $project: {
          date: '$_id',
          views: 1,
          uniqueVisitors: { $size: '$uniqueVisitors' },
          _id: 0,
        },
      },
      { $sort: { date: 1 } },
    ]);
  }

  private async getTrafficSources(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.analyticsEventModel.aggregate([
      {
        $match: {
          storeId: storeId,
          eventType: ANALYTICS_EVENTS.STORE_VIEW,
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: { $ifNull: ['$utmSource', 'direct'] },
          sessions: { $addToSet: '$sessionId' },
          totalViews: { $sum: 1 },
        },
      },
      {
        $project: {
          source: '$_id',
          uniqueVisitors: { $size: '$sessions' },
          totalViews: 1,
          _id: 0,
        },
      },
      { $sort: { uniqueVisitors: -1 } },
    ]);
  }

  private async getTopPages(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.analyticsEventModel.aggregate([
      {
        $match: {
          storeId: storeId,
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$eventType',
          count: { $sum: 1 },
          uniqueVisitors: { $addToSet: '$sessionId' },
        },
      },
      {
        $project: {
          page: '$_id',
          views: '$count',
          uniqueVisitors: { $size: '$uniqueVisitors' },
          _id: 0,
        },
      },
      { $sort: { views: -1 } },
    ]);
  }

  private async getDeviceBreakdown(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.analyticsEventModel.aggregate([
      {
        $match: {
          storeId: storeId,
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: { $ifNull: ['$deviceType', 'unknown'] },
          sessions: { $addToSet: '$sessionId' },
        },
      },
      {
        $project: {
          device: '$_id',
          sessions: { $size: '$sessions' },
          _id: 0,
        },
      },
      { $sort: { sessions: -1 } },
    ]);
  }

  private async getDailySales(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.orderModel.aggregate([
      {
        $match: {
          storeId: storeId,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          orders: { $sum: 1 },
          revenue: { $sum: '$totalAmount' },
        },
      },
      {
        $project: {
          date: '$_id',
          orders: 1,
          revenue: 1,
          _id: 0,
        },
      },
      { $sort: { date: 1 } },
    ]);
  }

  private async getTopProducts(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.analyticsEventModel.aggregate([
      {
        $match: {
          storeId: storeId,
          eventType: ANALYTICS_EVENTS.PRODUCT_VIEW,
          timestamp: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$productId',
          views: { $sum: 1 },
        },
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: '_id',
          as: 'product',
        },
      },
      { $unwind: '$product' },
      {
        $project: {
          productId: '$_id',
          productName: '$product.name',
          views: 1,
          _id: 0,
        },
      },
      { $sort: { views: -1 } },
      { $limit: 10 },
    ]);
  }

  private async getSalesByCategory(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    // This would require joining order items with products and categories
    // Simplified version for now
    return [];
  }

  private async getOrderStatusBreakdown(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.orderModel.aggregate([
      {
        $match: {
          storeId: storeId,
          createdAt: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: '$totalAmount' },
        },
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          totalValue: 1,
          _id: 0,
        },
      },
    ]);
  }

  private async getTopViewedProducts(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.getTopProducts(storeId, startDate, endDate);
  }

  private async getTopSellingProducts(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.productModel.aggregate([
      {
        $match: {
          storeId: storeId,
        },
      },
      {
        $sort: { purchaseCount: -1 },
      },
      {
        $limit: 10,
      },
      {
        $project: {
          productId: '$_id',
          productName: '$name',
          purchaseCount: 1,
          revenue: { $multiply: ['$purchaseCount', '$basePrice'] },
          _id: 0,
        },
      },
    ]);
  }

  private async getProductConversionRates(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.productModel.aggregate([
      {
        $match: {
          storeId: storeId,
          viewCount: { $gt: 0 },
        },
      },
      {
        $project: {
          productId: '$_id',
          productName: '$name',
          views: '$viewCount',
          purchases: '$purchaseCount',
          conversionRate: {
            $multiply: [
              { $divide: ['$purchaseCount', '$viewCount'] },
              100,
            ],
          },
          _id: 0,
        },
      },
      { $sort: { conversionRate: -1 } },
      { $limit: 10 },
    ]);
  }

  private async getLowStockProducts(storeId: string): Promise<any[]> {
    return this.productModel.find({
      storeId,
      trackInventory: true,
      stockQuantity: { $lte: 5 },
    }).select('name stockQuantity basePrice').exec();
  }

  private async getNewCustomers(storeId: string, startDate: Date, endDate: Date): Promise<number> {
    return this.customerModel.countDocuments({
      storeId,
      createdAt: { $gte: startDate, $lte: endDate },
    });
  }

  private async getReturningCustomers(storeId: string, startDate: Date, endDate: Date): Promise<number> {
    return this.customerModel.countDocuments({
      storeId,
      totalOrders: { $gt: 1 },
      lastOrderDate: { $gte: startDate, $lte: endDate },
    });
  }

  private async getCustomerSegments(storeId: string): Promise<any[]> {
    return this.customerModel.aggregate([
      { $match: { storeId: storeId } },
      {
        $group: {
          _id: '$customerSegment',
          count: { $sum: 1 },
          totalSpent: { $sum: '$totalSpent' },
        },
      },
      {
        $project: {
          segment: '$_id',
          count: 1,
          totalSpent: 1,
          _id: 0,
        },
      },
    ]);
  }

  private async getTopCustomers(storeId: string, startDate: Date, endDate: Date): Promise<any[]> {
    return this.customerModel.find({
      storeId,
      lastOrderDate: { $gte: startDate, $lte: endDate },
    })
    .sort({ totalSpent: -1 })
    .limit(10)
    .select('name phone totalSpent totalOrders')
    .exec();
  }

  private async getCustomerRetention(storeId: string, startDate: Date, endDate: Date): Promise<any> {
    const totalCustomers = await this.customerModel.countDocuments({ storeId });
    const returningCustomers = await this.getReturningCustomers(storeId, startDate, endDate);
    
    return {
      totalCustomers,
      returningCustomers,
      retentionRate: totalCustomers > 0 ? (returningCustomers / totalCustomers) * 100 : 0,
    };
  }
}
