import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { CategoriesService } from './categories.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';

@ApiTags('Categories')
@Controller('stores/:storeId/categories')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 201, description: 'Category created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error or duplicate name' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async create(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() createCategoryDto: CreateCategoryDto,
  ) {
    const category = await this.categoriesService.create(storeId, user._id.toString(), createCategoryDto);
    return {
      message: 'Category created successfully',
      category,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories for a store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findAll(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query() query: any,
  ) {
    const result = await this.categoriesService.findAll(storeId, user._id.toString(), query);
    return {
      message: 'Categories retrieved successfully',
      ...result,
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get category statistics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Category stats retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getStats(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const stats = await this.categoriesService.getStats(storeId, user._id.toString());
    return {
      message: 'Category stats retrieved successfully',
      stats,
    };
  }

  @Get(':categoryId')
  @ApiOperation({ summary: 'Get category by ID' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiResponse({ status: 200, description: 'Category retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findById(
    @Param('storeId') storeId: string,
    @Param('categoryId') categoryId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const category = await this.categoriesService.findById(categoryId, storeId, user._id.toString());
    return {
      message: 'Category retrieved successfully',
      category,
    };
  }

  @Put(':categoryId')
  @ApiOperation({ summary: 'Update category' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiResponse({ status: 200, description: 'Category updated successfully' })
  @ApiResponse({ status: 400, description: 'Validation error or duplicate name' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async update(
    @Param('storeId') storeId: string,
    @Param('categoryId') categoryId: string,
    @CurrentUser() user: UserDocument,
    @Body() updateCategoryDto: UpdateCategoryDto,
  ) {
    const category = await this.categoriesService.update(
      categoryId,
      storeId,
      user._id.toString(),
      updateCategoryDto,
    );
    return {
      message: 'Category updated successfully',
      category,
    };
  }

  @Delete(':categoryId')
  @ApiOperation({ summary: 'Delete category' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'categoryId', description: 'Category ID' })
  @ApiResponse({ status: 200, description: 'Category deleted successfully' })
  @ApiResponse({ status: 400, description: 'Cannot delete category with products' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async remove(
    @Param('storeId') storeId: string,
    @Param('categoryId') categoryId: string,
    @CurrentUser() user: UserDocument,
  ) {
    await this.categoriesService.remove(categoryId, storeId, user._id.toString());
    return {
      message: 'Category deleted successfully',
    };
  }
}
