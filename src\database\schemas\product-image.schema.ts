import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export type ProductImageDocument = ProductImage & Document;

@Schema({
  timestamps: true,
  collection: 'product_images',
})
export class ProductImage extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Product', 
    required: true,
    index: true
  })
  productId: Types.ObjectId;

  @Prop({ 
    required: true, 
    trim: true,
    match: [/^https?:\/\/.+/, 'Please enter a valid image URL']
  })
  imageUrl: string;

  @Prop({ trim: true, maxlength: 255 })
  altText?: string;

  @Prop({ default: false })
  isMain: boolean;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 100
  })
  sortOrder: number;

  // Additional image metadata
  @Prop({ 
    type: Number,
    min: 0,
    max: 50 * 1024 * 1024 // 50MB max
  })
  fileSize?: number;

  @Prop({ 
    type: Object
  })
  dimensions?: {
    width: number;
    height: number;
  };

  @Prop({ trim: true })
  mimeType?: string;

  @Prop({ trim: true })
  originalName?: string;

  // Optimized versions
  @Prop({ trim: true })
  thumbnailUrl?: string;

  @Prop({ trim: true })
  mediumUrl?: string;

  @Prop({ trim: true })
  largeUrl?: string;
}

export const ProductImageSchema = SchemaFactory.createForClass(ProductImage);

// Indexes
ProductImageSchema.index({ productId: 1 });
ProductImageSchema.index({ productId: 1, sortOrder: 1 });
ProductImageSchema.index({ productId: 1, isMain: 1 });
ProductImageSchema.index({ createdAt: -1 });

// Pre-save middleware
ProductImageSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  next();
});

// Pre-save middleware to ensure only one main image per product
ProductImageSchema.pre('save', async function(next) {
  if (this.isMain && this.isModified('isMain')) {
    // Set all other images of this product to not main
    await (this.constructor as any).updateMany(
      {
        productId: this.productId,
        _id: { $ne: this._id }
      },
      { isMain: false }
    );
  }
  next();
});

// Post-remove middleware to set another image as main if main image is deleted
ProductImageSchema.post('findOneAndDelete', async function(doc) {
  if (doc && doc.isMain) {
    // Find another image to set as main
    const nextImage = await this.model.findOne({ 
      productId: doc.productId 
    }).sort({ sortOrder: 1 });
    
    if (nextImage) {
      nextImage.isMain = true;
      await nextImage.save();
    }
  }
});
