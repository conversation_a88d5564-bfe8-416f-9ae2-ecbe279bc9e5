const axios = require('axios');

const NGROK_URL = 'https://381b-103-70-36-156.ngrok-free.app';
const headers = {
  'Content-Type': 'application/json',
  'ngrok-skip-browser-warning': 'true'
};

async function testWhatsAppIntegration() {
  console.log('🧪 Testing WhatsApp Integration...\n');

  try {
    // Test 1: Webhook Verification
    console.log('1. Testing Webhook Verification...');
    const verifyResponse = await axios.get(`${NGROK_URL}/api/pingbot/whatsapp/webhook`, {
      params: {
        'hub.mode': 'subscribe',
        'hub.verify_token': 'pingstore_webhook_verify_token_2024',
        'hub.challenge': 'test123'
      },
      headers: {
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log('✅ Webhook verification:', verifyResponse.data);

    // Test 2: PingBot Test
    console.log('\n2. Testing PingBot AI...');
    const botResponse = await axios.post(`${NGROK_URL}/api/pingbot/test`, {
      message: 'Hello, I need help with my order'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log('✅ PingBot response:', botResponse.data.data.data.response);

    // Test 3: WhatsApp Configuration
    console.log('\n3. Testing WhatsApp Configuration...');
    const configResponse = await axios.get(`${NGROK_URL}/api/pingbot/whatsapp/config`, {
      headers: {
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log('✅ WhatsApp config:', configResponse.data);

    // Test 4: Simulate WhatsApp Message
    console.log('\n4. Testing Simulated WhatsApp Message...');
    const simulatedMessage = {
      object: 'whatsapp_business_account',
      entry: [{
        id: 'test-entry-id',
        changes: [{
          value: {
            messaging_product: 'whatsapp',
            metadata: {
              display_phone_number: '+**********',
              phone_number_id: 'test-phone-id'
            },
            messages: [{
              from: '+**********',
              id: 'test-message-id',
              timestamp: Date.now().toString(),
              text: {
                body: 'Hello, I need help with my order #12345'
              },
              type: 'text'
            }]
          },
          field: 'messages'
        }]
      }]
    };

    const webhookResponse = await axios.post(`${NGROK_URL}/api/pingbot/whatsapp/webhook`, simulatedMessage, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    console.log('✅ Webhook message processed:', webhookResponse.status === 200 ? 'Success' : 'Failed');

    console.log('\n🎉 All tests passed! WhatsApp integration is ready!');
    console.log('\n📋 Next Steps:');
    console.log('1. Get WhatsApp Business API credentials from Meta');
    console.log('2. Update WHATSAPP_ACCESS_TOKEN and WHATSAPP_PHONE_NUMBER_ID in .env');
    console.log('3. Configure webhook in Meta Developer Console:');
    console.log(`   Webhook URL: ${NGROK_URL}/api/pingbot/whatsapp/webhook`);
    console.log('   Verify Token: pingstore_webhook_verify_token_2024');
    console.log('4. Test with real WhatsApp messages!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testWhatsAppIntegration();
