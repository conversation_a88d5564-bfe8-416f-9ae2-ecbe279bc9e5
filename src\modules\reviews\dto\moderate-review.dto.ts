import { IsString, IsEnum, IsOptional, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ReviewModerationAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  PENDING = 'pending',
}

export class ModerateReviewDto {
  @ApiProperty({
    description: 'Moderation action',
    enum: ReviewModerationAction,
    example: ReviewModerationAction.APPROVE,
  })
  @IsEnum(ReviewModerationAction)
  action: ReviewModerationAction;

  @ApiPropertyOptional({
    description: 'Moderation notes (optional)',
    example: 'Approved after verification',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Moderation notes must not exceed 500 characters' })
  moderationNotes?: string;
}
