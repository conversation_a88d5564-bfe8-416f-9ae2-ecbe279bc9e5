const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_CUSTOMER_PHONE = 'telegram:7520184516';
const TEST_ORDER_NUMBER = 'TG-2024-001';

async function testBotFlow() {
    console.log('🧪 Testing Bot Flow...\n');

    try {
        // Step 1: Test initial "hi" message (should ask for order number)
        console.log('1️⃣ Testing initial "hi" message...');
        const hiResponse = await sendMessage('hi');
        console.log('✅ Hi Response:', hiResponse.message ? hiResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Step 2: Test order number (should connect to store)
        console.log('2️⃣ Testing order number connection...');
        const orderResponse = await sendMessage(TEST_ORDER_NUMBER);
        console.log('✅ Order Response:', orderResponse.message ? orderResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Step 3: Test "track" (should use connected order)
        console.log('3️⃣ Testing "track" with connected order...');
        const trackResponse = await sendMessage('track');
        console.log('✅ Track Response:', trackResponse.message ? trackResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Wait a bit to see logs
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Step 4: Test "status" (should use connected order)
        console.log('4️⃣ Testing "status" with connected order...');
        const statusResponse = await sendMessage('status');
        console.log('✅ Status Response:', statusResponse.message ? statusResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Step 5: Test "delivery" (should use connected order)
        console.log('5️⃣ Testing "delivery" with connected order...');
        const deliveryResponse = await sendMessage('delivery');
        console.log('✅ Delivery Response:', deliveryResponse.message ? deliveryResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Step 6: Test "payment" (should use connected order)
        console.log('6️⃣ Testing "payment" with connected order...');
        const paymentResponse = await sendMessage('payment');
        console.log('✅ Payment Response:', paymentResponse.message ? paymentResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        // Step 7: Test "products" (should work in store context)
        console.log('7️⃣ Testing "products" in store context...');
        const productsResponse = await sendMessage('products');
        console.log('✅ Products Response:', productsResponse.message ? productsResponse.message.substring(0, 100) + '...' : 'No message in response', '\n');

        console.log('🎉 All tests completed!');

        // Check if track/status are working properly
        if (trackResponse.message && trackResponse.message.includes("I don't see any recent orders")) {
            console.log('❌ ISSUE FOUND: Track is not finding the connected order!');
            console.log('🔧 Need to fix the template service to use the order from memory');
        } else {
            console.log('✅ Track is working correctly with connected order');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response:', error.response.data);
        }
    }
}

async function sendMessage(text) {
    const payload = {
        message: {
            message_id: Date.now(),
            from: {
                id: 7520184516,
                is_bot: false,
                first_name: "Test",
                username: "testuser"
            },
            chat: {
                id: 7520184516,
                first_name: "Test",
                username: "testuser",
                type: "private"
            },
            date: Math.floor(Date.now() / 1000),
            text: text
        }
    };

    console.log(`📤 Sending: "${text}"`);
    
    const response = await axios.post(`${BASE_URL}/api/pingbot/telegram/webhook`, payload, {
        headers: {
            'Content-Type': 'application/json'
        }
    });

    console.log(`📥 Received: ${response.status}`);
    console.log(`📋 Response data:`, JSON.stringify(response.data, null, 2));
    return response.data;
}

// Run the test
testBotFlow();
