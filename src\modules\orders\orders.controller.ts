import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { OrdersService } from './orders.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { Public, CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { CreateOrderDto } from './dto/create-order.dto';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  // Public order creation (no auth required for customers)
  @Public()
  @Post()
  @ApiOperation({ summary: 'Create a new order (public)' })
  @ApiResponse({ status: 201, description: 'Order created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 404, description: 'Store or product not found' })
  async create(@Body() createOrderDto: CreateOrderDto) {
    const result = await this.ordersService.create(createOrderDto);
    return {
      message: API_MESSAGES.ORDER_CREATED,
      ...result,
    };
  }

  // Store owner endpoints (auth required)
  @Get('store/:storeId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get all orders for a store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'paymentStatus', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ status: 200, description: 'Orders retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findAllByStore(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query() query: any,
  ) {
    const result = await this.ordersService.findAll(storeId, user._id.toString(), query);
    return {
      message: 'Orders retrieved successfully',
      ...result,
    };
  }

  @Get(':orderId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async findById(@Param('orderId') orderId: string) {
    const order = await this.ordersService.findById(orderId);
    return {
      message: 'Order retrieved successfully',
      order,
    };
  }

  @Put(':orderId/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update order status' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Order status updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid status transition' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async updateStatus(
    @Param('orderId') orderId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: {
      storeId: string;
      status: string;
      adminNotes?: string;
    },
  ) {
    const order = await this.ordersService.updateStatus(
      orderId,
      body.storeId,
      user._id.toString(),
      body.status,
      body.adminNotes,
    );
    return {
      message: API_MESSAGES.ORDER_UPDATED,
      order,
    };
  }

  @Put(':orderId/tracking')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update order tracking information' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Order tracking updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async updateTracking(
    @Param('orderId') orderId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: {
      storeId: string;
      trackingNumber?: string;
      courierPartner?: string;
      estimatedDelivery?: Date;
    },
  ) {
    const order = await this.ordersService.updateTracking(
      orderId,
      body.storeId,
      user._id.toString(),
      {
        trackingNumber: body.trackingNumber,
        courierPartner: body.courierPartner,
        estimatedDelivery: body.estimatedDelivery,
      },
    );
    return {
      message: 'Order tracking updated successfully',
      order,
    };
  }

  @Put(':orderId/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Cancel order' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Order cancelled successfully' })
  @ApiResponse({ status: 400, description: 'Order cannot be cancelled' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async cancelOrder(
    @Param('orderId') orderId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: {
      storeId: string;
      reason?: string;
    },
  ) {
    const order = await this.ordersService.cancelOrder(
      orderId,
      body.storeId,
      user._id.toString(),
      body.reason,
    );
    return {
      message: API_MESSAGES.ORDER_CANCELLED,
      order,
    };
  }
}
