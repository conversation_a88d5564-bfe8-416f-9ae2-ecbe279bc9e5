import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUrl, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'Category name',
    example: 'Electronics',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2, { message: 'Category name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Category name must not exceed 100 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Category description',
    example: 'Electronic gadgets and accessories',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Category image URL',
    example: 'https://example.com/category-image.jpg',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please enter a valid image URL' })
  imageUrl?: string;

  @ApiPropertyOptional({
    description: 'Is category active',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
