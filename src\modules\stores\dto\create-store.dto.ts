import { IsString, IsOptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateStoreDto {
  @ApiProperty({
    description: 'Store name',
    example: 'My Awesome Store',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2, { message: 'Store name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Store name must not exceed 100 characters' })
  name: string;

  @ApiProperty({
    description: 'Store handle (URL slug)',
    example: 'my-awesome-store',
    minLength: 3,
    maxLength: 50,
  })
  @IsString()
  @MinLength(3, { message: 'Store handle must be at least 3 characters long' })
  @MaxLength(50, { message: 'Store handle must not exceed 50 characters' })
  @Matches(/^[a-z0-9-]+$/, {
    message: 'Store handle can only contain lowercase letters, numbers, and hyphens',
  })
  handle: string;

  @ApiPropertyOptional({
    description: 'Store description',
    example: 'A store selling amazing products',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Store bio',
    example: 'Welcome to our store!',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Bio must not exceed 200 characters' })
  bio?: string;
}
