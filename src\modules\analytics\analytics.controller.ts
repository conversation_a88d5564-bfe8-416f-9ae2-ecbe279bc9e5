import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';

@ApiTags('Analytics')
@Controller('stores/:storeId/analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('overview')
  @ApiOperation({ summary: 'Get store overview analytics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Overview analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getOverview(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const stats = await this.analyticsService.getOverviewStats(storeId, user._id.toString());
    return {
      message: 'Overview analytics retrieved successfully',
      stats,
    };
  }

  @Get('traffic')
  @ApiOperation({ summary: 'Get traffic analytics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiResponse({ status: 200, description: 'Traffic analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getTrafficAnalytics(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const end = endDate ? new Date(endDate) : new Date();

    const analytics = await this.analyticsService.getTrafficAnalytics(storeId, user._id.toString(), start, end);
    return {
      message: 'Traffic analytics retrieved successfully',
      analytics,
      dateRange: { startDate: start, endDate: end },
    };
  }

  @Get('sales')
  @ApiOperation({ summary: 'Get sales analytics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiResponse({ status: 200, description: 'Sales analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getSalesAnalytics(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const end = endDate ? new Date(endDate) : new Date();

    const analytics = await this.analyticsService.getSalesAnalytics(storeId, user._id.toString(), start, end);
    return {
      message: 'Sales analytics retrieved successfully',
      analytics,
      dateRange: { startDate: start, endDate: end },
    };
  }

  @Get('products')
  @ApiOperation({ summary: 'Get product analytics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiResponse({ status: 200, description: 'Product analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getProductAnalytics(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const end = endDate ? new Date(endDate) : new Date();

    const analytics = await this.analyticsService.getProductAnalytics(storeId, user._id.toString(), start, end);
    return {
      message: 'Product analytics retrieved successfully',
      analytics,
      dateRange: { startDate: start, endDate: end },
    };
  }

  @Get('customers')
  @ApiOperation({ summary: 'Get customer analytics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date (ISO string)' })
  @ApiResponse({ status: 200, description: 'Customer analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getCustomerAnalytics(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const end = endDate ? new Date(endDate) : new Date();

    const analytics = await this.analyticsService.getCustomerAnalytics(storeId, user._id.toString(), start, end);
    return {
      message: 'Customer analytics retrieved successfully',
      analytics,
      dateRange: { startDate: start, endDate: end },
    };
  }
}
