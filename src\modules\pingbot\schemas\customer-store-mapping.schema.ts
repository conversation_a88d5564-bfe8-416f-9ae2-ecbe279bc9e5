import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CustomerStoreMappingDocument = CustomerStoreMappingModel & Document;

@Schema({ timestamps: true })
export class CustomerStoreMappingModel {
  @Prop({ required: true, index: true })
  customerPhone: string;

  @Prop({ required: true })
  storeId: string;

  @Prop({ required: true })
  storeName: string;

  @Prop({ 
    required: true, 
    enum: ['order_id', 'order_history', 'manual_selection', 'conversation_history'] 
  })
  source: string;

  @Prop({ default: Date.now })
  lastInteraction: Date;

  @Prop({ default: 1 })
  interactionCount: number;

  @Prop({ type: Object, default: {} })
  metadata: {
    orderId?: string;
    platform?: string;
    userId?: string;
  };

  @Prop({ default: Date.now, expires: 86400 }) // 24 hours TTL
  expiresAt: Date;
}

export const CustomerStoreMappingSchema = SchemaFactory.createForClass(CustomerStoreMappingModel);
