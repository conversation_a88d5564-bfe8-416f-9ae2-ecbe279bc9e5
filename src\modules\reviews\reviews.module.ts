import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { ReviewsController } from './reviews.controller';
import { ReviewsService } from './reviews.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { Review, ReviewSchema } from '@database/schemas/review.schema';
import { Product, ProductSchema } from '@database/schemas/product.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';
import { Customer, CustomerSchema } from '@database/schemas/customer.schema';
import { Order, OrderSchema } from '@database/schemas/order.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Review.name, schema: ReviewSchema },
      { name: Product.name, schema: ProductSchema },
      { name: Store.name, schema: StoreSchema },
      { name: Customer.name, schema: CustomerSchema },
      { name: Order.name, schema: OrderSchema },
    ]),
    StoresModule,
  ],
  controllers: [ReviewsController],
  providers: [ReviewsService],
  exports: [ReviewsService],
})
export class ReviewsModule {}
