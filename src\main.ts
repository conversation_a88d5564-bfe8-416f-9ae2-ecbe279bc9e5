import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>Pipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { WinstonModule } from 'nest-winston';
import * as helmet from 'helmet';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from '@common/filters/global-exception.filter';
import { ResponseInterceptor } from '@common/interceptors/response.interceptor';
import { LoggingInterceptor } from '@common/interceptors/logging.interceptor';
import { createWinstonConfig } from '@config/winston.config';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    // Create Winston logger instance
    const winstonLogger = WinstonModule.createLogger(createWinstonConfig());

    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: winstonLogger,
      cors: true,
    });

    // Get configuration service
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3000);
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');

    // Security middleware
    app.use(helmet.default({
      contentSecurityPolicy: nodeEnv === 'production' ? undefined : false,
      crossOriginEmbedderPolicy: false,
    }));

    // Compression middleware
    app.use(compression());

    // Cookie parser
    app.use(cookieParser());

    // Global prefix for all routes
    app.setGlobalPrefix('api');

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // Global exception filter
    app.useGlobalFilters(new GlobalExceptionFilter());

    // Global interceptors
    app.useGlobalInterceptors(
      new LoggingInterceptor(),
      new ResponseInterceptor(),
    );

    // CORS configuration
    app.enableCors({
      origin: [
        configService.get<string>('FRONTEND_URL'),
        'http://localhost:3000',
        'http://localhost:3001',
        'https://pingstore.com',
        'https://*.pingstore.com',
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Version',
        'X-Store-ID',
      ],
    });

    // Swagger documentation (only in development)
    if (nodeEnv === 'development') {
      const config = new DocumentBuilder()
        .setTitle('PingStore API')
        .setDescription('Store-in-bio platform with WhatsApp integration')
        .setVersion('1.0')
        .addBearerAuth(
          {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
          },
          'JWT-auth',
        )
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Users', 'User management')
        .addTag('Stores', 'Store management')
        .addTag('Products', 'Product management')
        .addTag('Orders', 'Order management')
        .addTag('Public', 'Public storefront APIs')
        .addTag('Payments', 'Payment processing')
        .addTag('WhatsApp', 'WhatsApp integration')
        .addTag('Analytics', 'Analytics and reporting')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });

      logger.log(`📚 Swagger documentation available at http://localhost:${port}/api/docs`);
    }

    // Start the server
    await app.listen(port);

    logger.log(`🚀 PingStore API is running on http://localhost:${port}`);
    logger.log(`🌍 Environment: ${nodeEnv}`);
    logger.log(`📊 Health check: http://localhost:${port}/api/health`);

  } catch (error) {
    logger.error('❌ Failed to start the application', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  const logger = new Logger('UnhandledRejection');
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  const logger = new Logger('UncaughtException');
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  const logger = new Logger('SIGTERM');
  logger.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

bootstrap();
