import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { Model } from 'mongoose';
import { firstValueFrom } from 'rxjs';

import { WhatsAppIntegration, WhatsAppIntegrationDocument } from '@database/schemas/whatsapp-integration.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';
import { Order, OrderDocument } from '@database/schemas/order.schema';
import { Customer, CustomerDocument } from '@database/schemas/customer.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES, API_MESSAGES } from '@common/constants';

// DTOs
import { ConfigureWhatsAppDto } from './dto/configure-whatsapp.dto';

@Injectable()
export class WhatsappService {
  private readonly logger = new Logger(WhatsappService.name);
  private readonly pingbotApiUrl: string;
  private readonly pingbotApiKey: string;
  private readonly centralWhatsAppNumber: string;

  constructor(
    @InjectModel(WhatsAppIntegration.name) private whatsappModel: Model<WhatsAppIntegrationDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    private storesService: StoresService,
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.pingbotApiUrl = this.configService.get<string>('whatsapp.pingbot.apiUrl');
    this.pingbotApiKey = this.configService.get<string>('whatsapp.pingbot.apiKey');
    this.centralWhatsAppNumber = this.configService.get<string>('whatsapp.pingbot.centralNumber') || '+91-XXXXX-XXXXX';
  }

  async configureWhatsApp(
    storeId: string,
    userId: string,
    configDto: ConfigureWhatsAppDto,
  ): Promise<WhatsAppIntegrationDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only configure WhatsApp for your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Find or create WhatsApp integration
    let integration = await this.whatsappModel.findOne({ storeId });

    if (integration) {
      // Update existing integration
      Object.assign(integration, configDto);
      integration.updatedAt = new Date();
      await integration.save();
    } else {
      // Create new integration
      integration = new this.whatsappModel({
        storeId,
        phoneNumber: this.centralWhatsAppNumber, // Always use central number
        isVerified: true, // Central number is pre-verified
        ...configDto,
        isActive: true,
      });
      await integration.save();
    }

    // Register store with PingBot
    await this.registerStoreWithPingBot(store, integration);

    // Update store WhatsApp info
    await this.storeModel.findByIdAndUpdate(storeId, {
      whatsappNumber: this.centralWhatsAppNumber,
      whatsappVerified: true,
    });

    this.logger.log(`WhatsApp configured for store: ${store.name}`);
    return integration;
  }

  async getWhatsAppConfig(storeId: string, userId: string): Promise<WhatsAppIntegrationDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view WhatsApp config for your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const integration = await this.whatsappModel.findOne({ storeId });

    if (!integration) {
      throw new NotFoundException({
        message: 'WhatsApp integration not configured',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    return integration;
  }

  async sendOrderNotification(orderId: string): Promise<void> {
    const order = await this.orderModel
      .findById(orderId)
      .populate('storeId')
      .exec();

    if (!order) {
      this.logger.error(`Order not found: ${orderId}`);
      return;
    }

    const integration = await this.whatsappModel.findOne({ 
      storeId: order.storeId._id,
      isActive: true,
    });

    const store = order.storeId as any;
    if (!integration || !integration.orderNotificationsEnabled) {
      this.logger.debug(`Order notifications disabled for store: ${store.name}`);
      return;
    }

    // Send notification to customer via PingBot
    await this.sendMessageViaPingBot({
      to: order.customerPhone,
      storeId: store._id.toString(),
      storeName: store.name,
      messageType: 'order_confirmation',
      data: {
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount,
        customerName: order.customerName,
        trackingUrl: `${this.configService.get('app.frontendUrl')}/track/${orderId}`,
      },
    });

    // Notify store owner if configured
    if (integration.storeOwnerPhone) {
      await this.sendMessageViaPingBot({
        to: integration.storeOwnerPhone,
        storeId: store._id.toString(),
        storeName: store.name,
        messageType: 'new_order_alert',
        data: {
          orderNumber: order.orderNumber,
          totalAmount: order.totalAmount,
          customerName: order.customerName,
          customerPhone: order.customerPhone,
        },
      });
    }

    this.logger.log(`Order notification sent for: ${order.orderNumber}`);
  }

  async sendShippingNotification(orderId: string): Promise<void> {
    const order = await this.orderModel
      .findById(orderId)
      .populate('storeId')
      .exec();

    if (!order) {
      this.logger.error(`Order not found: ${orderId}`);
      return;
    }

    const integration = await this.whatsappModel.findOne({ 
      storeId: order.storeId._id,
      isActive: true,
    });

    const store = order.storeId as any;
    if (!integration || !integration.shippingNotificationsEnabled) {
      this.logger.debug(`Shipping notifications disabled for store: ${store.name}`);
      return;
    }

    await this.sendMessageViaPingBot({
      to: order.customerPhone,
      storeId: store._id.toString(),
      storeName: store.name,
      messageType: 'order_shipped',
      data: {
        orderNumber: order.orderNumber,
        trackingNumber: order.trackingNumber,
        courierPartner: order.courierPartner,
        estimatedDelivery: order.estimatedDelivery,
        trackingUrl: `${this.configService.get('app.frontendUrl')}/track/${orderId}`,
      },
    });

    this.logger.log(`Shipping notification sent for: ${order.orderNumber}`);
  }

  async handleWebhook(payload: any): Promise<void> {
    // Handle incoming messages from PingBot
    const { messageType, storeId, customerPhone, message, conversationId } = payload;

    switch (messageType) {
      case 'customer_message':
        await this.handleCustomerMessage(storeId, customerPhone, message, conversationId);
        break;
      case 'store_owner_reply':
        await this.handleStoreOwnerReply(storeId, customerPhone, message, conversationId);
        break;
      default:
        this.logger.warn(`Unknown webhook message type: ${messageType}`);
    }
  }

  // Private helper methods
  private async registerStoreWithPingBot(store: StoreDocument, integration: WhatsAppIntegrationDocument): Promise<void> {
    if (!this.pingbotApiUrl || !this.pingbotApiKey) {
      this.logger.warn('PingBot API not configured');
      return;
    }

    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.pingbotApiUrl}/stores/register`,
          {
            storeId: store._id.toString(),
            storeName: store.name,
            storeHandle: store.handle,
            businessDescription: integration.businessDescription,
            storeOwnerPhone: integration.storeOwnerPhone,
            settings: {
              pingbotEnabled: integration.pingbotEnabled,
              autoRespondEnabled: integration.autoRespondEnabled,
              forwardUnknownQueries: integration.forwardUnknownQueries,
            },
          },
          {
            headers: {
              'Authorization': `Bearer ${this.pingbotApiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.log(`Store registered with PingBot: ${store.name}`);
    } catch (error) {
      this.logger.error(`Failed to register store with PingBot: ${error.message}`);
    }
  }

  private async sendMessageViaPingBot(messageData: any): Promise<void> {
    if (!this.pingbotApiUrl || !this.pingbotApiKey) {
      this.logger.warn('PingBot API not configured');
      return;
    }

    try {
      await firstValueFrom(
        this.httpService.post(
          `${this.pingbotApiUrl}/messages/send`,
          messageData,
          {
            headers: {
              'Authorization': `Bearer ${this.pingbotApiKey}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      this.logger.debug(`Message sent via PingBot to: ${messageData.to}`);
    } catch (error) {
      this.logger.error(`Failed to send message via PingBot: ${error.message}`);
    }
  }

  private async handleCustomerMessage(
    storeId: string,
    customerPhone: string,
    message: string,
    conversationId: string,
  ): Promise<void> {
    // Log customer message for analytics
    this.logger.log(`Customer message from ${customerPhone} for store ${storeId}: ${message}`);

    // Update conversation ID in relevant orders
    await this.orderModel.updateMany(
      { storeId, customerPhone },
      { pingbotConversationId: conversationId },
    );
  }

  private async handleStoreOwnerReply(
    storeId: string,
    customerPhone: string,
    message: string,
    conversationId: string,
  ): Promise<void> {
    // Log store owner reply
    this.logger.log(`Store owner reply for store ${storeId} to ${customerPhone}: ${message}`);
  }

  async getWhatsAppStats(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view WhatsApp stats for your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const integration = await this.whatsappModel.findOne({ storeId });

    if (!integration) {
      return {
        isConfigured: false,
        centralNumber: this.centralWhatsAppNumber,
      };
    }

    // Get message stats from PingBot (if available)
    let messageStats = {
      totalMessages: 0,
      customerMessages: 0,
      storeOwnerReplies: 0,
      avgResponseTime: 0,
    };

    try {
      if (this.pingbotApiUrl && this.pingbotApiKey) {
        const response = await firstValueFrom(
          this.httpService.get(
            `${this.pingbotApiUrl}/stores/${storeId}/stats`,
            {
              headers: {
                'Authorization': `Bearer ${this.pingbotApiKey}`,
              },
            },
          ),
        );
        messageStats = response.data;
      }
    } catch (error) {
      this.logger.warn(`Failed to fetch PingBot stats: ${error.message}`);
    }

    return {
      isConfigured: true,
      centralNumber: this.centralWhatsAppNumber,
      isActive: integration.isActive,
      settings: {
        pingbotEnabled: integration.pingbotEnabled,
        autoRespondEnabled: integration.autoRespondEnabled,
        orderNotificationsEnabled: integration.orderNotificationsEnabled,
        paymentNotificationsEnabled: integration.paymentNotificationsEnabled,
        shippingNotificationsEnabled: integration.shippingNotificationsEnabled,
      },
      stats: messageStats,
      lastSync: integration.lastSync,
    };
  }
}
