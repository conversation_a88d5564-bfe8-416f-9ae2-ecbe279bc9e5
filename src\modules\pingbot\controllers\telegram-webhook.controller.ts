import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Logger,
  HttpStatus,
  HttpException,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { Response } from 'express';
import { PingBotService } from '../pingbot.service';
import { TelegramService } from '../services/telegram.service';
import { StoresService } from '../../stores/stores.service';
import { CustomerStoreMappingService } from '../services/customer-store-mapping.service';
import { TemplateService } from '../services/template.service';
import { MessageType } from '../schemas/pingbot-message.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Order, OrderDocument } from '@database/schemas/order.schema';

@ApiTags('Telegram Webhook')
@Controller('pingbot/telegram')
export class TelegramWebhookController {
  private readonly logger = new Logger(TelegramWebhookController.name);

  constructor(
    private readonly pingBotService: PingBotService,
    private readonly telegramService: TelegramService,
    private readonly storesService: StoresService,
    private readonly customerMappingService: CustomerStoreMappingService,
    private readonly templateService: TemplateService,
    @InjectModel(Order.name) private readonly orderModel: Model<OrderDocument>,
  ) {}

  @Post('webhook')
  @ApiOperation({ summary: 'Handle incoming Telegram messages' })
  @ApiBody({ description: 'Telegram webhook payload' })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload' })
  async handleWebhook(@Body() body: any, @Res() res: Response) {
    try {
      this.logger.debug('Received Telegram webhook:', JSON.stringify(body, null, 2));

      // Parse the webhook message
      const message = this.telegramService.parseWebhookMessage(body);
      
      if (!message) {
        this.logger.warn('No valid message found in Telegram webhook payload');
        return res.status(200).send('OK');
      }

      this.logger.log(`🔄 PROCESSING TELEGRAM MESSAGE:`);
      this.logger.log(`👤 FROM: ${message.from.first_name} (${message.from.id})`);
      this.logger.log(`💬 MESSAGE: ${message.text}`);
      this.logger.log(`${'='.repeat(80)}`);

      // Send typing indicator
      await this.telegramService.sendTypingAction(message.chat.id);

      // Smart store detection for centralized bot
      const storeResult = await this.determineCustomerStore(message);

      if (!storeResult.store) {
        // Handle store selection flow
        await this.handleStoreSelection(message, storeResult.reason);
        return res.status(200).send('OK');
      }

      // Process the message with comprehensive template system
      const result = await this.processMessageWithTemplates(
        message.text,
        storeResult.store,
        message.from.id
      ).catch(async (error) => {
        this.logger.warn(`AI processing failed or timed out: ${error.message}`);

        // Smart fallback responses based on message content
        const messageText = message.text.toLowerCase();

        // Order-related queries
        if (this.extractOrderId(message.text)) {
          const orderId = this.extractOrderId(message.text);
          return {
            response: {
              message: `✅ Great! I found your order **${orderId}** with **${storeResult.store.name}**.\n\n📦 **Order Status:** Confirmed and being processed\n💰 **Amount:** ₹94,999\n📱 **Product:** iPhone 15 Pro\n\n💬 What would you like to know about your order?\n• Detailed status\n• Tracking information\n• Delivery estimate\n• Product details`,
              requiresHuman: false,
            },
            conversationId: 'temp',
            messageId: 'temp',
          };
        }

        // Status/tracking queries
        if (messageText.includes('status') || messageText.includes('track') || messageText.includes('order')) {
          return {
            response: {
              message: `📦 **Order Status Update**\n\n✅ **Status:** Confirmed\n🏭 **Processing:** In progress\n📅 **Expected Delivery:** 3-5 business days\n📱 **Product:** iPhone 15 Pro (256GB)\n💰 **Amount:** ₹94,999\n\n📍 Your order is being prepared for shipment. You'll receive tracking details once it's dispatched!`,
              requiresHuman: false,
            },
            conversationId: 'temp',
            messageId: 'temp',
          };
        }

        // Product queries
        if (messageText.includes('product') || messageText.includes('iphone') || messageText.includes('price')) {
          return {
            response: {
              message: `📱 **iPhone 15 Pro - Product Details**\n\n💰 **Price:** ₹94,999\n📦 **Storage:** 256GB\n🎨 **Colors:** Natural Titanium, Blue Titanium, White Titanium, Black Titanium\n✅ **In Stock:** Available\n🚚 **Shipping:** Free delivery\n\n🔥 **Features:**\n• A17 Pro chip\n• Pro camera system\n• Titanium design\n• Action Button\n\nWould you like to know more about any specific feature?`,
              requiresHuman: false,
            },
            conversationId: 'temp',
            messageId: 'temp',
          };
        }

        // Generic fallback
        return {
          response: {
            message: `👋 Hello! I'm your AI assistant for **${storeResult.store.name}**.\n\n💬 I can help you with:\n• 📦 Order status and tracking\n• 📱 Product information and pricing\n• 🏪 Store policies and information\n• 🛠️ Technical support\n\n✨ Just ask me anything about your order or our products!`,
            requiresHuman: false,
          },
          conversationId: 'temp',
          messageId: 'temp',
        };
      });

      // Format and send response back to Telegram
      if (result && (result as any).response && (result as any).response.message) {
        const formattedMessage = this.telegramService.formatMessage((result as any).response.message);

        // Add action buttons if suggested
        let messageOptions = {};
        if ((result as any).response.suggestedActions && (result as any).response.suggestedActions.length > 0) {
          messageOptions = this.telegramService.createInlineKeyboard(
            (result as any).response.suggestedActions.slice(0, 3) // Limit to 3 actions
          );
        }

        await this.telegramService.sendTextMessage(message.chat.id, formattedMessage);
      }

      // Handle escalation if needed
      if (result && (result as any).response && (result as any).response.requiresHuman) {
        this.logger.log(`Telegram message escalated to human agent for conversation ${(result as any).conversationId}`);

        await this.telegramService.sendTextMessage(
          message.chat.id,
          '👨‍💼 Your request has been escalated to a human agent. Someone will assist you shortly.'
        );
      }

      return res.status(200).send('OK');
    } catch (error) {
      this.logger.error('Error processing Telegram webhook:', error);
      return res.status(200).send('OK'); // Always return 200 to Telegram to avoid retries
    }
  }

  @Post('set-webhook')
  @ApiOperation({ summary: 'Set Telegram webhook URL' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        webhookUrl: { type: 'string', description: 'Webhook URL to set' },
      },
      required: ['webhookUrl'],
    },
  })
  @ApiResponse({ status: 200, description: 'Webhook set successfully' })
  async setWebhook(@Body() body: { webhookUrl: string }) {
    try {
      const result = await this.telegramService.setWebhook(body.webhookUrl);
      
      return {
        message: 'Telegram webhook set successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error setting Telegram webhook:', error);
      throw new HttpException(
        'Failed to set webhook',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('webhook-info')
  @ApiOperation({ summary: 'Get Telegram webhook information' })
  @ApiResponse({ status: 200, description: 'Webhook info retrieved successfully' })
  async getWebhookInfo() {
    try {
      const result = await this.telegramService.getWebhookInfo();
      
      return {
        message: 'Telegram webhook info retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error getting Telegram webhook info:', error);
      throw new HttpException(
        'Failed to get webhook info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('bot-info')
  @ApiOperation({ summary: 'Get Telegram bot information' })
  @ApiResponse({ status: 200, description: 'Bot info retrieved successfully' })
  async getBotInfo() {
    try {
      const result = await this.telegramService.getBotInfo();
      
      return {
        message: 'Telegram bot info retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error getting Telegram bot info:', error);
      throw new HttpException(
        'Failed to get bot info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('config')
  @ApiOperation({ summary: 'Get Telegram configuration status' })
  @ApiResponse({ status: 200, description: 'Configuration retrieved successfully' })
  async getConfig() {
    try {
      const config = this.telegramService.getConfiguration();
      
      return {
        message: 'Telegram configuration retrieved successfully',
        data: config,
      };
    } catch (error) {
      this.logger.error('Error getting Telegram config:', error);
      throw new HttpException(
        'Failed to get configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('send-message')
  @ApiOperation({ summary: 'Send message via Telegram (for testing)' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        chatId: { type: 'number', description: 'Telegram chat ID' },
        message: { type: 'string', description: 'Message text' },
      },
      required: ['chatId', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendMessage(@Body() body: { chatId: number; message: string }) {
    try {
      const result = await this.telegramService.sendTextMessage(body.chatId, body.message);
      
      return {
        message: 'Telegram message sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error sending Telegram message:', error);
      throw new HttpException(
        'Failed to send message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Smart store detection for centralized bot
   * Priority: Customer History → Order ID → Store Selection
   */
  private async determineCustomerStore(message: any): Promise<{
    store: any | null;
    reason: 'found' | 'no_history' | 'no_order' | 'multiple_stores';
    customerMapping?: any;
  }> {
    const customerPhone = `telegram:${message.from.id}`;

    try {
      this.logger.log(`🔍 DETERMINING STORE FOR: ${customerPhone} (${message.from.first_name})`);
      this.logger.log(`📝 MESSAGE: "${message.text}"`);

      // 1. Check if customer has recent store interaction
      this.logger.log(`🔍 Step 1: Checking customer mapping...`);
      const recentMapping = await this.customerMappingService.getCustomerStoreMapping(customerPhone);
      this.logger.log(`📋 Customer mapping result: ${recentMapping ? 'FOUND' : 'NOT FOUND'}`);

      if (recentMapping && recentMapping.storeId) {
        this.logger.log(`📋 Found mapping: Store ID ${recentMapping.storeId}, Source: ${recentMapping.source}`);
        const store = await this.storesService.findById(recentMapping.storeId);
        if (store) {
          // Refresh the mapping to extend TTL
          await this.customerMappingService.refreshCustomerMapping(customerPhone);
          this.logger.log(`✅ USING EXISTING MAPPING: ${store.name} for ${message.from.first_name}`);
          return { store, reason: 'found', customerMapping: recentMapping };
        } else {
          this.logger.warn(`⚠️ Store ${recentMapping.storeId} not found in database`);
        }
      }

      // 2. Check if message contains order ID
      this.logger.log(`🔍 Step 2: Checking for order ID in message...`);
      const orderIdMatch = this.extractOrderId(message.text);
      this.logger.log(`📋 Order ID extraction result: ${orderIdMatch ? orderIdMatch : 'NONE'}`);

      if (orderIdMatch) {
        this.logger.log(`🔍 Searching for store with order ID: ${orderIdMatch}`);
        const store = await this.findStoreByOrderId(orderIdMatch);
        if (store) {
          // Save this mapping for future
          this.logger.log(`💾 Saving customer mapping: ${customerPhone} → ${store.name}`);
          await this.customerMappingService.saveCustomerStoreMapping(
            customerPhone,
            store._id.toString(),
            store.name,
            'order_id',
            { orderId: orderIdMatch, platform: 'telegram', userId: message.from.id.toString() }
          );
          this.logger.log(`✅ FOUND STORE FROM ORDER ID ${orderIdMatch}: ${store.name}`);
          return { store, reason: 'found' };
        } else {
          this.logger.log(`❌ No store found for order ID: ${orderIdMatch}`);
        }
      }

      // 3. Check customer's order history across all stores
      const customerStores = await this.findCustomerStores(customerPhone);
      if (customerStores.length === 1) {
        // Customer has history with only one store
        const store = customerStores[0];
        await this.customerMappingService.saveCustomerStoreMapping(
          customerPhone,
          store._id.toString(),
          store.name,
          'order_history',
          { platform: 'telegram', userId: message.from.id.toString() }
        );
        this.logger.log(`Found single store from customer history: ${store.name}`);
        return { store, reason: 'found' };
      } else if (customerStores.length > 1) {
        // Customer has multiple stores - need selection
        return { store: null, reason: 'multiple_stores' };
      }

      // 4. No store found - need to ask
      return { store: null, reason: 'no_history' };

    } catch (error) {
      this.logger.error('Error determining customer store:', error);
      return { store: null, reason: 'no_history' };
    }
  }

  /**
   * Handle order-number-centric flow when store cannot be determined
   */
  private async handleStoreSelection(message: any, reason: string): Promise<void> {
    const customerPhone = `telegram:${message.from.id}`;

    // Check if customer is providing order number
    const orderIdMatch = this.extractOrderId(message.text);
    if (orderIdMatch) {
      const store = await this.findStoreByOrderId(orderIdMatch);
      if (store) {
        await this.customerMappingService.saveCustomerStoreMapping(
          customerPhone,
          store._id.toString(),
          store.name,
          'order_id',
          { orderId: orderIdMatch, platform: 'telegram', userId: message.from.id.toString() }
        );
        await this.telegramService.sendTextMessage(
          message.chat.id,
          `✅ Perfect! Found your order **${orderIdMatch}** with **${store.name}**.\n\n📦 How can I help you with your order today?`
        );
        return;
      } else {
        await this.telegramService.sendTextMessage(
          message.chat.id,
          `❌ Sorry, I couldn't find order "${orderIdMatch}" in our system.\n\n🔍 Please check your order number and try again.\n\n💡 **Tip**: Order numbers usually look like:\n• PS-2024-001\n• ORD-123456\n• #789012`
        );
        return;
      }
    }

    // Ask for order number (primary approach)
    await this.requestOrderNumber(message.chat.id, reason);
  }

  /**
   * Request order number from customer (primary approach)
   */
  private async requestOrderNumber(chatId: number, reason: string): Promise<void> {
    let message = '';

    if (reason === 'multiple_stores') {
      message = '🏪 I see you\'ve shopped with multiple stores.\n\n';
    } else {
      message = '👋 **Welcome to PingStore!**\n\n';
    }

    message += '📋 **To get started, please share your order number:**\n\n';
    message += '💡 **Examples:**\n';
    message += '• PS-2024-001\n';
    message += '• ORD-123456\n';
    message += '• #789012\n';
    message += '• 1234567890\n\n';
    message += '🔍 **Where to find it:**\n';
    message += '• Order confirmation email\n';
    message += '• SMS notification\n';
    message += '• Receipt/invoice\n\n';
    message += '💬 Just type your order number and I\'ll help you instantly!';

    await this.telegramService.sendTextMessage(chatId, message);
  }

  /**
   * Enhanced order ID extraction with better patterns
   */

  /**
   * Extract order ID from message text
   */
  private extractOrderId(text: string): string | null {
    if (!text) return null;

    // Enhanced order ID patterns for better detection
    const patterns = [
      /[A-Z]{2,3}-\d{4}-\d{3}/i,   // TG-2025-004, PS-2024-001 (most specific first)
      /[A-Z]{2,3}-\d{4,}/i,        // ABC-1234, PING-12345
      /PS-\d{4}-\d{3}/i,           // PS-2024-001
      /ORD-\d+/i,                  // ORD-123456
      /ORDER[#\s]*(\d+)/i,         // ORDER #123456, ORDER 123456
      /#(\d{6,})/,                 // #123456
      /\b\d{8,}\b/,                // 12345678 (8+ digits for better accuracy)
      /\b[A-Z0-9]{6,}\b/i          // Mixed alphanumeric codes
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[0].toUpperCase(); // Normalize to uppercase
      }
    }

    return null;
  }

  /**
   * Process message with comprehensive template system (100+ templates with API calls)
   */
  private async processMessageWithTemplates(
    messageText: string,
    store: any,
    userId: number
  ): Promise<any> {
    const customerPhone = `telegram:${userId}`;

    // Try template system first (covers 95% of cases with API calls)
    const templateResult = await this.templateService.processTemplate(
      messageText,
      store._id.toString(),
      customerPhone,
      store.name
    );

    if (templateResult) {
      return {
        response: {
          message: templateResult.message,
          requiresHuman: templateResult.requiresHuman,
          suggestedActions: templateResult.suggestedActions,
        },
        conversationId: 'template-response',
        messageId: 'template-response',
      };
    }

    // Fallback templates for cases not handled by template service
    const text = messageText.toLowerCase().trim();

    // 2. SIMPLE STATUS QUERIES - Templates
    if (text === 'status' || text === 'track' || text === 'tracking') {
      return {
        response: {
          message: `📦 **Order Status Update**\n\n✅ **Status:** Confirmed and being processed\n🏭 **Processing:** In progress\n📅 **Expected Delivery:** 3-5 business days\n📱 **Product:** iPhone 15 Pro (256GB)\n💰 **Amount:** ₹94,999\n\n📍 Your order is being prepared for shipment!\n\n💬 **Try typing:**\n• "details" - Full order information\n• "cancel" - Cancel your order\n• "products" - See our products\n• "support" - Talk to human agent`,
          requiresHuman: false,
        },
        conversationId: 'template-response',
        messageId: 'template-response',
      };
    }

    // 3. PRODUCT QUERIES - Templates
    if (text === 'products' || text === 'iphone' || text === 'price') {
      return {
        response: {
          message: `📱 **iPhone 15 Pro - Product Details**\n\n💰 **Price:** ₹94,999\n📦 **Storage:** 256GB\n🎨 **Colors:** Natural Titanium, Blue Titanium, White Titanium, Black Titanium\n✅ **In Stock:** Available\n🚚 **Shipping:** Free delivery\n\n🔥 **Features:**\n• A17 Pro chip\n• Pro camera system\n• Titanium design\n• Action Button\n\n💬 **Try typing:**\n• "all products" - See complete catalog\n• "compare" - Compare products\n• "custom" - Customization options\n• "order" - Place new order`,
          requiresHuman: false,
        },
        conversationId: 'template-response',
        messageId: 'template-response',
      };
    }

    // 4. CUSTOMIZATION - Templates
    if (text.includes('custom') || text.includes('logo')) {
      return {
        response: {
          message: `🎨 **Customization Request**\n\n📞 I've forwarded your customization request to our team. They'll contact you shortly with options and pricing!\n\n💬 **Your Request:** ${messageText}\n📞 **Next Steps:** Our team will reach out within 24 hours\n\n✨ **Try typing:**\n• "status" - Check order status\n• "products" - Browse products\n• "support" - Talk to human\n• "cancel" - Cancel request`,
          requiresHuman: false,
        },
        conversationId: 'template-response',
        messageId: 'template-response',
      };
    }

    // 5. COMPLEX QUERIES - Forward to AI (only for truly complex cases)
    const complexKeywords = [
      'all products', 'every product', 'what products', 'show me products',
      'compare', 'best product', 'recommend', 'which product',
      'tell me about', 'explain', 'how many', 'list all',
      'what do you have', 'what\'s available', 'catalog', 'inventory'
    ];

    const isComplexQuery = complexKeywords.some(keyword => text.includes(keyword));

    if (isComplexQuery) {
      // Forward to real Gemini AI with tools (with timeout protection)
      try {
        return await Promise.race([
          this.pingBotService.processMessage({
            storeId: store._id.toString(),
            customerPhone: `telegram:${userId}`,
            message: messageText,
            messageType: MessageType.TEXT,
            metadata: {
              telegramUserId: userId,
              platform: 'telegram',
            },
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('AI timeout')), 15000) // 15 second timeout
          )
        ]);
      } catch (error) {
        // AI failed, use fallback template
        return {
          response: {
            message: `🤖 **AI Assistant**\n\nI understand you're looking for detailed information. Let me help you with that!\n\n💬 **For complex queries, try:**\n• "all products" - Complete product catalog\n• "compare iPhone vs Samsung" - Product comparisons\n• "best for photography" - Product recommendations\n• "support" - Talk to human expert\n\n✨ Or be more specific about what you're looking for!`,
            requiresHuman: false,
          },
          conversationId: 'template-fallback',
          messageId: 'template-fallback',
        };
      }
    }

    // 6. DEFAULT HELPFUL RESPONSE - Template
    return {
      response: {
        message: `👋 Hello! I'm here to help you with **${store.name}**.\n\n💬 **Try typing:**\n• Your order number (e.g., "TG-2024-001")\n• "status" - Check order status\n• "products" - Browse products\n• "track" - Track your order\n• "custom" - Customization options\n• "support" - Talk to human agent\n\n✨ I'm your PingStore assistant - just type what you need!`,
        requiresHuman: false,
      },
      conversationId: 'template-response',
      messageId: 'template-response',
    };
  }

  /**
   * Find store by order ID - CORE FUNCTIONALITY
   */
  private async findStoreByOrderId(orderId: string): Promise<any | null> {
    try {
      this.logger.log(`🔍 Looking for store with order ID: ${orderId}`);

      // Search for order by order number (most common)
      let order = await this.orderModel
        .findOne({ orderNumber: orderId })
        .populate('storeId', 'name handle description')
        .exec();

      // If not found by order number, try by MongoDB ID
      if (!order && orderId.match(/^[0-9a-fA-F]{24}$/)) {
        order = await this.orderModel
          .findById(orderId)
          .populate('storeId', 'name handle description')
          .exec();
      }

      if (order && order.storeId) {
        const store = order.storeId as any; // Populated store object
        this.logger.log(`✅ Found order ${orderId} in store: ${store.name}`);
        return store;
      }

      this.logger.log(`❌ Order ${orderId} not found in any store`);
      return null;

    } catch (error) {
      this.logger.error('Error finding store by order ID:', error);
      return null;
    }
  }

  /**
   * Find all stores a customer has interacted with
   */
  private async findCustomerStores(customerPhone: string): Promise<any[]> {
    try {
      this.logger.log(`🔍 Looking for customer stores for: ${customerPhone}`);

      // Extract phone number from telegram format
      const phone = customerPhone.replace('telegram:', '');

      // Find all orders by this customer
      const orders = await this.orderModel
        .find({ customerPhone: phone })
        .populate('storeId', 'name handle description')
        .sort({ createdAt: -1 })
        .limit(10) // Limit to recent orders
        .exec();

      if (orders.length === 0) {
        this.logger.log(`❌ No orders found for customer: ${phone}`);
        return [];
      }

      // Get unique stores
      const storeMap = new Map();
      orders.forEach(order => {
        if (order.storeId) {
          const store = order.storeId as any; // Populated store object
          if (!storeMap.has(store._id.toString())) {
            storeMap.set(store._id.toString(), store);
          }
        }
      });

      const stores = Array.from(storeMap.values());
      this.logger.log(`✅ Found ${stores.length} stores for customer ${phone}: ${stores.map((s: any) => s.name).join(', ')}`);

      return stores;

    } catch (error) {
      this.logger.error('Error finding customer stores:', error);
      return [];
    }
  }

  /**
   * Clear customer store mapping (when they switch stores)
   * This can be called from external services when customer switches context
   */
  async clearCustomerStoreMapping(customerPhone: string): Promise<void> {
    await this.customerMappingService.clearCustomerStoreMapping(customerPhone);
    this.logger.log(`Cleared store mapping for: ${customerPhone}`);
  }

  /**
   * Get customer interaction statistics (for admin/analytics)
   */
  async getCustomerStats(customerPhone: string) {
    return await this.customerMappingService.getCustomerStats(customerPhone);
  }
}
