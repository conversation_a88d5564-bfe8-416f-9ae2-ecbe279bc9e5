const axios = require('axios');

async function testAIDirect() {
  console.log('🤖 Testing AI Agent Directly...');
  
  try {
    const response = await axios.post('http://localhost:3000/api/pingbot/process-message', {
      storeId: '6853cb95c94210a5b61955fa', // TechGadgets Pro
      customerPhone: 'telegram:7520184516',
      message: 'What is the status of my order TEC-2025-004?',
      messageType: 'TEXT',
      metadata: {
        platform: 'telegram',
        telegramUserId: 7520184516
      }
    });
    
    console.log('✅ AI Response:', response.data);
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log('Response:', error.response.data);
    }
  }
}

testAIDirect();
