import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { MongoError } from 'mongodb';
import { Error as MongooseError } from 'mongoose';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let code = 'INTERNAL_SERVER_ERROR';
    let errors: any[] = [];

    // Handle different types of exceptions
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || exception.message;
        errors = (exceptionResponse as any).errors || [];
        code = (exceptionResponse as any).code || 'HTTP_EXCEPTION';
      } else {
        message = exceptionResponse as string;
      }
    } else if (exception instanceof MongoError) {
      // Handle MongoDB errors
      status = HttpStatus.BAD_REQUEST;
      
      if (exception.code === 11000) {
        // Duplicate key error
        const field = Object.keys((exception as any).keyPattern)[0];
        message = `${field} already exists`;
        code = 'DUPLICATE_VALUE';
      } else {
        message = 'Database error';
        code = 'DATABASE_ERROR';
      }
    } else if (exception instanceof MongooseError.ValidationError) {
      // Handle Mongoose validation errors
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation failed';
      code = 'VALIDATION_ERROR';
      
      errors = Object.values(exception.errors).map(err => ({
        field: err.path,
        message: err.message,
      }));
    } else if (exception instanceof MongooseError.CastError) {
      // Handle Mongoose cast errors
      status = HttpStatus.BAD_REQUEST;
      message = `Invalid ${exception.path}: ${exception.value}`;
      code = 'INVALID_FORMAT';
    } else if (exception instanceof Error) {
      message = exception.message;
      code = 'APPLICATION_ERROR';
    }

    // Generate request ID for tracking
    const requestId = this.generateRequestId();

    // Log the error
    this.logger.error(
      `${request.method} ${request.url} - ${status} - ${message}`,
      {
        requestId,
        method: request.method,
        url: request.url,
        userAgent: request.get('User-Agent'),
        ip: request.ip,
        exception: exception instanceof Error ? exception.stack : exception,
      },
    );

    // Send error response
    response.status(status).json({
      success: false,
      message,
      code,
      errors,
      requestId,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method,
    });
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }
}
