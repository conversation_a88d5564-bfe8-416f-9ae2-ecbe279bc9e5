import {
  Controller,
  Get,
  Put,
  Post,
  Delete,
  Body,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

import { UsersService } from './users.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@ApiTags('Users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('profile')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getProfile(@CurrentUser() user: UserDocument) {
    return {
      message: 'Profile retrieved successfully',
      user,
    };
  }

  @Put('profile')
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 409, description: 'Email or phone already exists' })
  async updateProfile(
    @CurrentUser() user: UserDocument,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    const updatedUser = await this.usersService.updateProfile(user._id.toString(), updateProfileDto);
    return {
      message: API_MESSAGES.PROFILE_UPDATED,
      user: updatedUser,
    };
  }

  @Post('change-password')
  @ApiOperation({ summary: 'Change user password' })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 409, description: 'Current password is incorrect' })
  async changePassword(
    @CurrentUser() user: UserDocument,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    await this.usersService.changePassword(user._id.toString(), changePasswordDto);
    return {
      message: 'Password changed successfully',
    };
  }

  @Post('upload-avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload user avatar' })
  @ApiResponse({ status: 200, description: 'Avatar uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file' })
  async uploadAvatar(
    @CurrentUser() user: UserDocument,
    @UploadedFile() file: Express.Multer.File,
  ) {
    // In a real implementation, you would upload the file to S3/Cloudinary
    // and get the URL. For now, we'll use a placeholder.
    const avatarUrl = `https://example.com/avatars/${user._id}.jpg`;
    
    const updatedUser = await this.usersService.uploadAvatar(user._id.toString(), avatarUrl);
    return {
      message: API_MESSAGES.FILE_UPLOADED,
      user: updatedUser,
      avatarUrl,
    };
  }

  @Post('verify-email')
  @ApiOperation({ summary: 'Verify user email' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  async verifyEmail(@CurrentUser() user: UserDocument) {
    const updatedUser = await this.usersService.verifyEmail(user._id.toString());
    return {
      message: 'Email verified successfully',
      user: updatedUser,
    };
  }

  @Post('verify-phone')
  @ApiOperation({ summary: 'Verify user phone' })
  @ApiResponse({ status: 200, description: 'Phone verified successfully' })
  async verifyPhone(@CurrentUser() user: UserDocument) {
    const updatedUser = await this.usersService.verifyPhone(user._id.toString());
    return {
      message: 'Phone verified successfully',
      user: updatedUser,
    };
  }

  @Delete('account')
  @ApiOperation({ summary: 'Delete user account' })
  @ApiResponse({ status: 200, description: 'Account deleted successfully' })
  async deleteAccount(@CurrentUser() user: UserDocument) {
    await this.usersService.deleteUser(user._id.toString());
    return {
      message: API_MESSAGES.USER_DELETED,
    };
  }
}
