import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Order, OrderDocument } from '@database/schemas/order.schema';
import { OrderItem, OrderItemDocument } from '@database/schemas/order-item.schema';
import { Customer, CustomerDocument } from '@database/schemas/customer.schema';
import { Product, ProductDocument } from '@database/schemas/product.schema';
import { ProductVariant, ProductVariantDocument } from '@database/schemas/product-variant.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';

import { StoresService } from '@modules/stores/stores.service';
import { 
  ERROR_CODES, 
  ORDER_STATUS, 
  PAYMENT_STATUS, 
  PAYMENT_METHODS,
  PRODUCT_STATUS 
} from '@common/constants';
import { PaginationResult } from '@common/interfaces';

// DTOs
import { CreateOrderDto } from './dto/create-order.dto';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(OrderItem.name) private orderItemModel: Model<OrderItemDocument>,
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(ProductVariant.name) private productVariantModel: Model<ProductVariantDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    private storesService: StoresService,
  ) {}

  async create(createOrderDto: CreateOrderDto): Promise<{ order: OrderDocument; paymentIntent?: any }> {
    const { storeHandle, customer, shippingAddress, items, paymentMethod, customerNotes } = createOrderDto;

    // Get store by handle
    const store = await this.storeModel.findOne({ 
      handle: storeHandle.toLowerCase(),
      isActive: true,
      isPublished: true,
    });

    if (!store) {
      throw new NotFoundException({
        message: 'Store not found',
        code: ERROR_CODES.STORE_NOT_FOUND,
      });
    }

    // Validate items and calculate totals
    const { validatedItems, subtotal } = await this.validateOrderItems(store._id.toString(), items);

    // Calculate shipping and totals
    const shippingFee = this.calculateShippingFee(store, subtotal);
    const taxAmount = 0; // Implement tax calculation if needed
    const discountAmount = 0; // Implement discount calculation if needed
    const totalAmount = subtotal + shippingFee + taxAmount - discountAmount;

    // Check minimum order amount
    if (totalAmount < store.minOrderAmount) {
      throw new BadRequestException({
        message: `Minimum order amount is ₹${store.minOrderAmount}`,
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Validate payment method
    if (paymentMethod === PAYMENT_METHODS.COD && !store.codEnabled) {
      throw new BadRequestException({
        message: 'Cash on Delivery is not available for this store',
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Create or update customer
    const customerDoc = await this.createOrUpdateCustomer(store._id.toString(), customer, shippingAddress);

    // Generate order number
    const orderNumber = this.generateOrderNumber();

    // Create order
    const order = new this.orderModel({
      storeId: store._id,
      orderNumber,
      customerName: customer.name,
      customerPhone: customer.phone,
      customerEmail: customer.email,
      shippingAddress: shippingAddress.address,
      shippingCity: shippingAddress.city,
      shippingState: shippingAddress.state,
      shippingPincode: shippingAddress.pincode,
      shippingCountry: shippingAddress.country || 'India',
      subtotal,
      shippingFee,
      taxAmount,
      discountAmount,
      totalAmount,
      paymentMethod,
      paymentStatus: paymentMethod === PAYMENT_METHODS.COD ? PAYMENT_STATUS.PENDING : PAYMENT_STATUS.PENDING,
      status: ORDER_STATUS.CONFIRMED,
      customerNotes,
      confirmedAt: new Date(),
    });

    await order.save();

    // Create order items
    const orderItems = await this.createOrderItems(order._id.toString(), validatedItems);

    // Update product stock
    await this.updateProductStock(validatedItems);

    // Update customer analytics
    await this.updateCustomerAnalytics(customerDoc, totalAmount);

    // Update store analytics
    await this.storesService.updateAnalytics(store._id.toString(), totalAmount);

    this.logger.log(`Order created: ${orderNumber} for store ${storeHandle}`);

    // Create payment intent if not COD
    let paymentIntent;
    if (paymentMethod !== PAYMENT_METHODS.COD) {
      paymentIntent = await this.createPaymentIntent(order, paymentMethod);
    }

    return {
      order: await this.findById(order._id.toString()),
      paymentIntent,
    };
  }

  async findAll(
    storeId: string,
    userId: string,
    query: any,
  ): Promise<PaginationResult<OrderDocument>> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view orders from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      status,
      paymentStatus,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { storeId };

    if (search) {
      filter.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { customerName: { $regex: search, $options: 'i' } },
        { customerPhone: { $regex: search, $options: 'i' } },
        { customerEmail: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      filter.status = status;
    }

    if (paymentStatus) {
      filter.paymentStatus = paymentStatus;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [orders, total] = await Promise.all([
      this.orderModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('items')
        .exec(),
      this.orderModel.countDocuments(filter),
    ]);

    return {
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(orderId: string): Promise<OrderDocument> {
    const order = await this.orderModel
      .findById(orderId)
      .populate('items')
      .populate('storeId', 'name handle')
      .exec();

    if (!order) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    return order;
  }

  async findByIdAndStore(orderId: string, storeId: string): Promise<OrderDocument> {
    const order = await this.orderModel
      .findOne({ _id: orderId, storeId })
      .populate('items')
      .exec();

    if (!order) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    return order;
  }

  async updateStatus(
    orderId: string,
    storeId: string,
    userId: string,
    status: string,
    adminNotes?: string,
  ): Promise<OrderDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update orders in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const order = await this.findByIdAndStore(orderId, storeId);

    // Validate status transition
    if (!this.isValidStatusTransition(order.status, status)) {
      throw new BadRequestException({
        message: `Cannot change order status from ${order.status} to ${status}`,
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    // Set status-specific timestamps
    const now = new Date();
    switch (status) {
      case ORDER_STATUS.SHIPPED:
        updateData.shippedAt = now;
        break;
      case ORDER_STATUS.DELIVERED:
        updateData.deliveredAt = now;
        break;
    }

    const updatedOrder = await this.orderModel
      .findByIdAndUpdate(orderId, updateData, { new: true })
      .populate('items')
      .exec();

    this.logger.log(`Order status updated: ${order.orderNumber} to ${status}`);
    return updatedOrder!;
  }

  async updateTracking(
    orderId: string,
    storeId: string,
    userId: string,
    trackingData: {
      trackingNumber?: string;
      courierPartner?: string;
      estimatedDelivery?: Date;
    },
  ): Promise<OrderDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update orders in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const order = await this.findByIdAndStore(orderId, storeId);

    const updatedOrder = await this.orderModel
      .findByIdAndUpdate(
        orderId,
        {
          ...trackingData,
          updatedAt: new Date(),
        },
        { new: true },
      )
      .populate('items')
      .exec();

    this.logger.log(`Order tracking updated: ${order.orderNumber}`);
    return updatedOrder!;
  }

  async cancelOrder(
    orderId: string,
    storeId: string,
    userId: string,
    reason?: string,
  ): Promise<OrderDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only cancel orders in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const order = await this.findByIdAndStore(orderId, storeId);

    if (!order.canBeCancelled()) {
      throw new BadRequestException({
        message: 'Order cannot be cancelled at this stage',
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Restore product stock
    const orderItems = await this.orderItemModel.find({ orderId });
    await this.restoreProductStock(orderItems);

    const updatedOrder = await this.orderModel
      .findByIdAndUpdate(
        orderId,
        {
          status: ORDER_STATUS.CANCELLED,
          adminNotes: reason || 'Order cancelled',
          updatedAt: new Date(),
        },
        { new: true },
      )
      .populate('items')
      .exec();

    this.logger.log(`Order cancelled: ${order.orderNumber}`);
    return updatedOrder!;
  }

  // Private helper methods
  private async validateOrderItems(storeId: string, items: any[]): Promise<{
    validatedItems: any[];
    subtotal: number;
  }> {
    const validatedItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await this.productModel.findOne({
        _id: item.productId,
        storeId,
        status: PRODUCT_STATUS.ACTIVE,
      });

      if (!product) {
        throw new BadRequestException({
          message: `Product not found: ${item.productId}`,
          code: ERROR_CODES.PRODUCT_NOT_FOUND,
        });
      }

      let variant = null;
      let effectivePrice = product.discountedPrice || product.basePrice;
      let variantName = null;

      if (item.variantId) {
        variant = await this.productVariantModel.findOne({
          _id: item.variantId,
          productId: item.productId,
          isActive: true,
        });

        if (!variant) {
          throw new BadRequestException({
            message: `Product variant not found: ${item.variantId}`,
            code: ERROR_CODES.VALIDATION_ERROR,
          });
        }

        effectivePrice = variant.discountedPrice || variant.price || effectivePrice;
        variantName = variant.name;
      }

      // Check stock
      const availableStock = variant ? variant.stockQuantity : product.stockQuantity;
      if (product.trackInventory && availableStock < item.quantity) {
        throw new BadRequestException({
          message: `Insufficient stock for ${product.name}`,
          code: ERROR_CODES.INSUFFICIENT_STOCK,
        });
      }

      const totalPrice = effectivePrice * item.quantity;
      subtotal += totalPrice;

      validatedItems.push({
        productId: item.productId,
        variantId: item.variantId,
        productName: product.name,
        variantName,
        sku: variant?.sku || product.sku,
        unitPrice: product.basePrice,
        discountedPrice: effectivePrice !== product.basePrice ? effectivePrice : null,
        quantity: item.quantity,
        totalPrice,
        weight: variant?.weight || product.weight,
        productAttributes: variant?.attributes || {},
        productImageUrl: product.images?.[0]?.imageUrl,
        productSnapshot: {
          description: product.description,
          tags: product.tags,
        },
      });
    }

    return { validatedItems, subtotal };
  }

  private calculateShippingFee(store: StoreDocument, subtotal: number): number {
    if (store.freeShippingAbove && subtotal >= store.freeShippingAbove) {
      return 0;
    }
    return store.shippingFee || 0;
  }

  private async createOrUpdateCustomer(
    storeId: string,
    customerData: any,
    shippingAddress: any,
  ): Promise<CustomerDocument> {
    let customer = await this.customerModel.findOne({
      storeId,
      phone: customerData.phone,
    });

    if (customer) {
      // Update existing customer
      customer.name = customerData.name;
      customer.email = customerData.email || customer.email;
      customer.address = shippingAddress.address;
      customer.city = shippingAddress.city;
      customer.state = shippingAddress.state;
      customer.pincode = shippingAddress.pincode;
      customer.country = shippingAddress.country || 'India';
      await customer.save();
    } else {
      // Create new customer
      customer = new this.customerModel({
        storeId,
        name: customerData.name,
        phone: customerData.phone,
        email: customerData.email,
        address: shippingAddress.address,
        city: shippingAddress.city,
        state: shippingAddress.state,
        pincode: shippingAddress.pincode,
        country: shippingAddress.country || 'India',
      });
      await customer.save();
    }

    return customer;
  }

  private async createOrderItems(orderId: string, items: any[]): Promise<OrderItemDocument[]> {
    const orderItems = items.map(item => ({
      orderId,
      ...item,
    }));

    return this.orderItemModel.insertMany(orderItems) as any;
  }

  private async updateProductStock(items: any[]): Promise<void> {
    const stockUpdates = items.map(async (item) => {
      if (item.variantId) {
        await this.productVariantModel.findByIdAndUpdate(item.variantId, {
          $inc: { stockQuantity: -item.quantity },
        });
      } else {
        await this.productModel.findByIdAndUpdate(item.productId, {
          $inc: { stockQuantity: -item.quantity },
        });
      }
    });

    await Promise.all(stockUpdates);
  }

  private async restoreProductStock(orderItems: OrderItemDocument[]): Promise<void> {
    const stockUpdates = orderItems.map(async (item) => {
      if (item.variantId) {
        await this.productVariantModel.findByIdAndUpdate(item.variantId, {
          $inc: { stockQuantity: item.quantity },
        });
      } else {
        await this.productModel.findByIdAndUpdate(item.productId, {
          $inc: { stockQuantity: item.quantity },
        });
      }
    });

    await Promise.all(stockUpdates);
  }

  private async updateCustomerAnalytics(customer: CustomerDocument, orderAmount: number): Promise<void> {
    customer.addOrder(orderAmount);
    await customer.save();
  }

  private generateOrderNumber(): string {
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `ORD-${timestamp}-${random}`;
  }

  private isValidStatusTransition(currentStatus: string, newStatus: string): boolean {
    const validTransitions: Record<string, string[]> = {
      [ORDER_STATUS.CONFIRMED]: [ORDER_STATUS.PROCESSING, ORDER_STATUS.CANCELLED],
      [ORDER_STATUS.PROCESSING]: [ORDER_STATUS.SHIPPED, ORDER_STATUS.CANCELLED],
      [ORDER_STATUS.SHIPPED]: [ORDER_STATUS.DELIVERED],
      [ORDER_STATUS.DELIVERED]: [],
      [ORDER_STATUS.CANCELLED]: [],
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  private async createPaymentIntent(order: OrderDocument, paymentMethod: string): Promise<any> {
    // Implement payment gateway integration here
    // This is a placeholder for Razorpay/Stripe integration
    
    switch (paymentMethod) {
      case PAYMENT_METHODS.UPI:
        return {
          id: `pi_${Date.now()}`,
          amount: order.totalAmount,
          currency: 'INR',
          paymentMethod: 'upi',
          status: 'requires_payment_method',
          paymentUrl: `upi://pay?pa=merchant@upi&pn=Store&am=${order.totalAmount}&tr=${order.orderNumber}`,
        };
      
      case PAYMENT_METHODS.CARD:
        return {
          id: `pi_${Date.now()}`,
          amount: order.totalAmount,
          currency: 'INR',
          paymentMethod: 'card',
          status: 'requires_payment_method',
          clientSecret: `pi_${Date.now()}_secret_${Math.random().toString(36)}`,
        };
      
      default:
        return null;
    }
  }
}
