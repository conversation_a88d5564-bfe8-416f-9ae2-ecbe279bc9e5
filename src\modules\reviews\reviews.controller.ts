import {
  Controller,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { ReviewsService } from './reviews.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';

@ApiTags('Reviews')
@Controller('stores/:storeId/reviews')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all reviews for a store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'rating', required: false, type: Number })
  @ApiQuery({ name: 'isApproved', required: false, type: Boolean })
  @ApiQuery({ name: 'productId', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ status: 200, description: 'Reviews retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findAll(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query() query: any,
  ) {
    const result = await this.reviewsService.findAll(storeId, user._id.toString(), query);
    return {
      message: 'Reviews retrieved successfully',
      ...result,
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get review statistics' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Review stats retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getStats(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const stats = await this.reviewsService.getReviewStats(storeId, user._id.toString());
    return {
      message: 'Review stats retrieved successfully',
      stats,
    };
  }

  @Get(':reviewId')
  @ApiOperation({ summary: 'Get review by ID' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'reviewId', description: 'Review ID' })
  @ApiResponse({ status: 200, description: 'Review retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findById(
    @Param('storeId') storeId: string,
    @Param('reviewId') reviewId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const review = await this.reviewsService.findById(reviewId, storeId, user._id.toString());
    return {
      message: 'Review retrieved successfully',
      review,
    };
  }

  @Put(':reviewId/moderate')
  @ApiOperation({ summary: 'Moderate review (approve/reject)' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'reviewId', description: 'Review ID' })
  @ApiResponse({ status: 200, description: 'Review moderated successfully' })
  @ApiResponse({ status: 404, description: 'Review not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async moderateReview(
    @Param('storeId') storeId: string,
    @Param('reviewId') reviewId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { 
      action: 'approve' | 'reject'; 
      moderationNotes?: string; 
    },
  ) {
    const review = await this.reviewsService.moderateReview(
      reviewId,
      storeId,
      user._id.toString(),
      body.action,
      body.moderationNotes,
    );
    return {
      message: `Review ${body.action}d successfully`,
      review,
    };
  }
}
