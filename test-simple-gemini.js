const { ChatGoogleGenerativeAI } = require('@langchain/google-genai');

async function testSimpleGemini() {
  console.log('🤖 Testing LangChain Gemini integration...');
  
  try {
    const llm = new ChatGoogleGenerativeAI({
      apiKey: 'AIzaSyDO-ow5P4OPg2uGA_VSxFIOiY4BouXKOC0',
      modelName: 'gemini-1.5-flash',
      temperature: 0.1,
      maxOutputTokens: 100,
    });
    
    console.log('✅ Gemini LLM initialized');
    
    const response = await Promise.race([
      llm.invoke('Say hello in one word'),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout after 10 seconds')), 10000)
      )
    ]);
    
    console.log('✅ Gemini Response:', response.content);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.message.includes('Timeout')) {
      console.log('⏰ GEMINI IS TOO SLOW - This is why we get timeouts!');
    }
  }
}

testSimpleGemini();
