import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ToolsService } from './tools.service';
import { OrdersService } from '../../orders/orders.service';
import { ProductsService } from '../../products/products.service';
import { StoresService } from '../../stores/stores.service';
import { CustomersService } from '../../customers/customers.service';
import { Order, OrderDocument } from '@database/schemas/order.schema';

export interface TemplateResponse {
  message: string;
  requiresHuman: boolean;
  suggestedActions?: string[];
  apiCall?: string;
  apiParams?: any;
}

@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);

  constructor(
    private readonly toolsService: ToolsService,
    private readonly ordersService: OrdersService,
    private readonly productsService: ProductsService,
    private readonly storesService: StoresService,
    private readonly customersService: CustomersService,
    @InjectModel(Order.name) private readonly orderModel: Model<OrderDocument>,
  ) {}

  async processTemplate(
    messageText: string,
    storeId: string,
    customerPhone: string,
    storeName: string
  ): Promise<TemplateResponse | null> {
    const text = messageText.toLowerCase().trim();
    this.logger.log(`🔍 Template Service Processing: "${text}" for customer ${customerPhone} in store ${storeId}`);

    // ORDER NUMBER PATTERNS (Templates 1-20)
    const orderNumber = this.extractOrderId(messageText);
    if (orderNumber) {
      this.logger.log(`📦 Found order number pattern: ${orderNumber}`);
      return await this.handleOrderNumberTemplate(orderNumber, storeId, customerPhone);
    }

    // STATUS QUERIES (Templates 21-30)
    if (this.matchesPattern(text, ['status', 'track', 'tracking', 'where is my order', 'order status'])) {
      this.logger.log(`📊 Handling status query: ${text}`);
      return await this.handleStatusTemplate(storeId, customerPhone);
    }

    // PRODUCT QUERIES (Templates 31-50)
    if (this.matchesPattern(text, ['products', 'iphone', 'price', 'cost', 'available', 'in stock', 'catalog', 'all products'])) {
      return await this.handleProductTemplate(text, storeId);
    }

    // CATEGORY QUERIES (Templates 51-60)
    if (this.matchesPattern(text, ['categories', 'category', 'browse', 'types', 'sections'])) {
      return await this.handleCategoryTemplate(storeId);
    }

    // VARIANT QUERIES (Templates 61-70)
    if (this.matchesPattern(text, ['variants', 'colors', 'sizes', 'options', 'models'])) {
      return await this.handleVariantTemplate(text, storeId);
    }

    // DELIVERY QUERIES (Templates 71-80)
    if (this.matchesPattern(text, ['delivery', 'shipping', 'when will arrive', 'delivery time', 'shipping cost', 'track', 'tracking'])) {
      return await this.handleDeliveryTemplate(storeId, customerPhone);
    }

    // PAYMENT QUERIES (Templates 81-90)
    if (this.matchesPattern(text, ['payment', 'pay', 'refund', 'money back', 'payment method', 'payment status'])) {
      return await this.handlePaymentTemplate(storeId, customerPhone);
    }

    // GREETING QUERIES (Templates 91-100)
    if (this.matchesPattern(text, ['hi', 'hello', 'hey', 'good morning', 'good evening', 'start'])) {
      return this.handleGreetingTemplate(storeName);
    }

    // RETURN/EXCHANGE QUERIES (Templates 101-110)
    if (this.matchesPattern(text, ['return', 'exchange', 'replace', 'wrong item', 'damaged'])) {
      return this.handleReturnTemplate();
    }

    return null; // No template match, forward to AI
  }

  private async handleOrderNumberTemplate(orderNumber: string, storeId: string, customerPhone: string): Promise<TemplateResponse> {
    try {
      // Search for order by order number - first try in current store, then globally
      let order = null;
      let foundStore = null;

      try {
        // Try to find order in the current store first using public methods
        const storeOrders = await this.searchOrdersInStore(storeId, orderNumber);
        if (storeOrders.length > 0) {
          order = storeOrders[0];
          foundStore = await this.storesService.findById(storeId);
        }
      } catch (storeError) {
        this.logger.warn(`Error searching in specific store ${storeId}:`, storeError.message);
      }

      // If not found in specific store, search across all stores
      if (!order) {
        try {
          const globalOrders = await this.searchOrdersGlobally(orderNumber);
          if (globalOrders.length > 0) {
            order = globalOrders[0];
            foundStore = order.storeId;
          }
        } catch (crossStoreError) {
          this.logger.warn(`Error in cross-store search:`, crossStoreError.message);
        }
      }

      if (order && foundStore) {
        // Format order details dynamically from real data
        const orderMessage = this.formatOrderDetails(order);

        return {
          message: `📦 **Order ${orderNumber} Status**\n\n${orderMessage}\n\n💬 **Try typing:**\n• "track" - Detailed tracking\n• "cancel" - Cancel order\n• "modify" - Change order\n• "support" - Talk to human`,
          requiresHuman: false,
          apiCall: 'order_lookup',
          suggestedActions: ['track', 'cancel', 'modify', 'support']
        };
      }

      // Order not found anywhere
      return {
        message: `❌ **Order ${orderNumber} Not Found**\n\nI couldn't find this order in our system. Please check:\n• Order number spelling\n• Order confirmation email\n• SMS notification\n\n💬 **Try typing:**\n• Your correct order number\n• "support" - Talk to human agent\n• "orders" - See your order history`,
        requiresHuman: false,
        suggestedActions: ['support', 'orders', 'help']
      };

    } catch (error) {
      this.logger.error(`Error in handleOrderNumberTemplate for ${orderNumber}:`, error);
      return {
        message: `❌ **Error Looking Up Order ${orderNumber}**\n\nI'm having trouble accessing order information right now. Please try again or contact support.\n\n💬 **Try typing:**\n• "support" - Talk to human agent\n• "help" - Get assistance`,
        requiresHuman: false,
        suggestedActions: ['support', 'help']
      };
    }
  }

  private formatOrderDetails(order: any): string {
    const status = order.status || 'Unknown';
    const customerName = order.customerName || 'Customer';
    const totalAmount = order.totalAmount || 0;
    const createdAt = order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Unknown';
    const storeName = order.storeId?.name || 'Store';

    let statusEmoji = '📦';
    switch (status.toLowerCase()) {
      case 'confirmed': statusEmoji = '✅'; break;
      case 'shipped': statusEmoji = '🚚'; break;
      case 'delivered': statusEmoji = '📬'; break;
      case 'cancelled': statusEmoji = '❌'; break;
    }

    return `${statusEmoji} **Status:** ${status}\n🏪 **Store:** ${storeName}\n👤 **Customer:** ${customerName}\n💰 **Amount:** ₹${totalAmount}\n📅 **Order Date:** ${createdAt}\n📱 **Order Number:** ${order.orderNumber}`;
  }

  private async handleStatusTemplate(storeId: string, customerPhone: string): Promise<TemplateResponse> {
    try {
      this.logger.log(`📊 Getting customer orders for ${customerPhone} in store ${storeId}`);
      // Get customer's recent orders from the store using helper method
      const recentOrders = await this.getCustomerOrdersInStore(storeId, customerPhone, 3);
      this.logger.log(`📊 Found ${recentOrders.length} orders for customer`);

      if (recentOrders.length > 0) {
        const latestOrder = recentOrders[0];
        this.logger.log(`📦 Latest order: ${latestOrder.orderNumber} - Status: ${latestOrder.status}`);
        const orderMessage = this.formatOrderDetails(latestOrder);

        return {
          message: `📦 **Your Latest Order Status**\n\n${orderMessage}\n\n💬 **Try typing:**\n• Your order number for specific details\n• "track" - Detailed tracking\n• "products" - Browse products\n• "support" - Talk to human`,
          requiresHuman: false,
          apiCall: 'customer_orders_lookup',
          suggestedActions: [latestOrder.orderNumber, 'track', 'products', 'support']
        };
      }

      // No orders found, show generic status
      return {
        message: `📦 **Order Status**\n\nI don't see any recent orders for you in this store. \n\n💬 **Try typing:**\n• Your order number (e.g., "ORD-12345")\n• "products" - Browse products\n• "support" - Talk to human\n• "help" - Get assistance`,
        requiresHuman: false,
        suggestedActions: ['ORD-12345', 'products', 'support', 'help']
      };
    } catch (error) {
      this.logger.warn(`Error in handleStatusTemplate:`, error.message);
      return {
        message: `📦 **Order Status**\n\n💬 **To check your order status:**\n• Share your order number\n• Check your email confirmation\n• Contact support for assistance\n\n💬 **Try typing:**\n• Your order number\n• "support" - Talk to human\n• "help" - Get assistance`,
        requiresHuman: false,
        suggestedActions: ['support', 'help']
      };
    }
  }

  private async handleProductTemplate(text: string, storeId: string): Promise<TemplateResponse> {
    try {
      // Enhanced product search with better query handling
      const searchQuery = this.extractProductSearchTerms(text);

      // Search for products using the actual ProductsService
      const products = await this.productsService.findAll(storeId, {
        search: searchQuery,
        limit: 5,
        status: 'active'
      });

      if (products.data && products.data.length > 0) {
        const productMessages = products.data.map(product => {
          const price = product.discountedPrice || product.basePrice;
          const originalPrice = product.basePrice;
          const inStock = product.stockQuantity > 0;
          const stockText = inStock ? `✅ **In Stock** (${product.stockQuantity} available)` : '❌ **Out of Stock**';
          const priceText = product.discountedPrice && product.discountedPrice < originalPrice
            ? `💰 **Price:** ₹${price} ~~₹${originalPrice}~~ (${Math.round((1 - price/originalPrice) * 100)}% off)`
            : `💰 **Price:** ₹${price}`;

          return `📱 **${product.name}**\n${priceText}\n${stockText}\n📝 ${product.description?.substring(0, 120)}...${product.isFeatured ? '\n⭐ **Featured Product**' : ''}`;
        }).join('\n\n');

        const firstProduct = products.data[0];
        return {
          message: `📱 **Product Search Results** (${products.pagination.total} found)\n\n${productMessages}\n\n💬 **Try typing:**\n• "all products" - Complete catalog\n• "${firstProduct.name}" - Product details\n• "categories" - Browse by category\n• "variants" - See product variants`,
          requiresHuman: false,
          apiCall: 'product_search',
          suggestedActions: ['all products', firstProduct.name, 'categories', 'variants']
        };
      }

      // No products found, show store's featured products
      const featuredProducts = await this.productsService.findAll(storeId, {
        isFeatured: true,
        limit: 4,
        status: 'active'
      });

      if (featuredProducts.data && featuredProducts.data.length > 0) {
        const featuredMessages = featuredProducts.data.map(product => {
          const price = product.discountedPrice || product.basePrice;
          const inStock = product.stockQuantity > 0;
          return `⭐ **${product.name}**\n💰 **Price:** ₹${price}\n${inStock ? '✅ In Stock' : '❌ Out of Stock'}\n📝 ${product.description?.substring(0, 100)}...`;
        }).join('\n\n');

        return {
          message: `⭐ **Featured Products**\n\n${featuredMessages}\n\n💬 **Try typing:**\n• "all products" - Complete catalog\n• Product name for details\n• "categories" - Browse by category\n• "search [product name]" - Search specific products`,
          requiresHuman: false,
          apiCall: 'featured_products',
          suggestedActions: ['all products', featuredProducts.data[0].name, 'categories', 'search']
        };
      }

      // Fallback if no products found
      return {
        message: `📱 **Products**\n\nI couldn't find specific products matching "${text}", but we have a great selection available!\n\n💬 **Try typing:**\n• "all products" - See complete catalog\n• "categories" - Browse by category\n• "featured" - See featured items\n• "support" - Talk to human`,
        requiresHuman: false,
        suggestedActions: ['all products', 'categories', 'featured', 'support']
      };
    } catch (error) {
      this.logger.warn(`Error in handleProductTemplate:`, error.message);
      return {
        message: `📱 **Products**\n\nI'm having trouble accessing product information right now.\n\n💬 **Try typing:**\n• "support" - Talk to human agent\n• "help" - Get assistance\n• Your order number for order info`,
        requiresHuman: false,
        suggestedActions: ['support', 'help']
      };
    }
  }

  private async handleDeliveryTemplate(storeId: string, customerPhone: string): Promise<TemplateResponse> {
    try {
      // Get customer's recent orders to provide delivery information
      const recentOrders = await this.getCustomerOrdersInStore(storeId, customerPhone, 3);
      const store = await this.storesService.findById(storeId);

      if (recentOrders.length > 0) {
        const latestOrder = recentOrders[0];
        const deliveryInfo = this.formatDeliveryInfo(latestOrder, store);

        return {
          message: `🚚 **Delivery Information**\n\n${deliveryInfo}\n\n💬 **Try typing:**\n• "track ${latestOrder.orderNumber}" - Track this order\n• "address" - Update delivery address\n• "delivery time" - Estimated delivery\n• "shipping cost" - Delivery charges`,
          requiresHuman: false,
          apiCall: 'delivery_info',
          suggestedActions: [`track ${latestOrder.orderNumber}`, 'address', 'delivery time', 'shipping cost']
        };
      }

      // No orders found, show general delivery info from store
      const generalDeliveryInfo = this.formatGeneralDeliveryInfo(store);
      return {
        message: `� **Delivery Information**\n\n${generalDeliveryInfo}\n\n💬 **Try typing:**\n• Your order number for tracking\n• "shipping cost" - Delivery charges\n• "delivery areas" - Coverage areas\n• "products" - Browse products`,
        requiresHuman: false,
        apiCall: 'general_delivery_info',
        suggestedActions: ['shipping cost', 'delivery areas', 'products', 'support']
      };
    } catch (error) {
      this.logger.warn(`Error in handleDeliveryTemplate:`, error.message);
      return {
        message: `� **Delivery Information**\n\nI'm having trouble accessing delivery information right now.\n\n💬 **Try typing:**\n• Your order number for tracking\n• "support" - Talk to human agent`,
        requiresHuman: false,
        suggestedActions: ['support', 'help']
      };
    }
  }

  private async handlePaymentTemplate(storeId: string, customerPhone: string): Promise<TemplateResponse> {
    try {
      // Get customer's recent orders to provide payment information
      const recentOrders = await this.getCustomerOrdersInStore(storeId, customerPhone, 3);
      const store = await this.storesService.findById(storeId);

      if (recentOrders.length > 0) {
        const latestOrder = recentOrders[0];
        const paymentInfo = this.formatPaymentInfo(latestOrder, store);

        return {
          message: `💳 **Payment Information**\n\n${paymentInfo}\n\n💬 **Try typing:**\n• "refund ${latestOrder.orderNumber}" - Request refund\n• "payment status" - Check payment\n• "payment methods" - Available options\n• "receipt" - Get payment receipt`,
          requiresHuman: false,
          apiCall: 'payment_info',
          suggestedActions: [`refund ${latestOrder.orderNumber}`, 'payment status', 'payment methods', 'receipt']
        };
      }

      // No orders found, show general payment info from store
      const generalPaymentInfo = this.formatGeneralPaymentInfo(store);
      return {
        message: `💳 **Payment Information**\n\n${generalPaymentInfo}\n\n💬 **Try typing:**\n• "payment methods" - Available options\n• "cod" - Cash on delivery info\n• "refund policy" - Refund information\n• "products" - Browse products`,
        requiresHuman: false,
        apiCall: 'general_payment_info',
        suggestedActions: ['payment methods', 'cod', 'refund policy', 'products']
      };
    } catch (error) {
      this.logger.warn(`Error in handlePaymentTemplate:`, error.message);
      return {
        message: `💳 **Payment Information**\n\nI'm having trouble accessing payment information right now.\n\n💬 **Try typing:**\n• Your order number for payment status\n• "support" - Talk to human agent`,
        requiresHuman: false,
        suggestedActions: ['support', 'help']
      };
    }
  }

  private handleGreetingTemplate(storeName: string): TemplateResponse {
    return {
      message: `👋 **Welcome to ${storeName}!**\n\nI'm your AI assistant, here to help you with orders, products, and support.\n\n💬 **Try typing:**\n• Your order number (e.g., "TG-2024-001")\n• "products" - Browse our products\n• "status" - Check order status\n• "support" - Talk to human agent\n• "help" - See all options\n\n✨ Just type what you need and I'll assist you instantly!`,
      requiresHuman: false,
      suggestedActions: ['TG-2024-001', 'products', 'status', 'support', 'help']
    };
  }

  private handleReturnTemplate(): TemplateResponse {
    return {
      message: `🔄 **Returns & Exchanges**\n\n✅ **Return Policy:** 30-day return window\n📦 **Condition:** Original packaging required\n💰 **Refund:** 7-14 business days\n🚚 **Pickup:** Free return pickup\n\n📋 **Process:**\n1. Provide order number\n2. Select return reason\n3. Schedule pickup\n4. Get refund confirmation\n\n💬 **Try typing:**\n• Your order number\n• "return process" - Detailed steps\n• "exchange" - Exchange product\n• "support" - Talk to human`,
      requiresHuman: false,
      suggestedActions: ['TG-2024-001', 'return process', 'exchange', 'support']
    };
  }

  private matchesPattern(text: string, patterns: string[]): boolean {
    return patterns.some(pattern => text.includes(pattern));
  }

  private extractOrderId(text: string): string | null {
    if (!text) return null;

    const patterns = [
      /[A-Z]{2,3}-\d{4}-\d{3}/i,   // TG-2025-004, PS-2024-001
      /[A-Z]{2,3}-\d{4,}/i,        // ABC-1234, PING-12345
      /ORD-\d+/i,                  // ORD-123456
      /ORDER[#\s]*(\d+)/i,         // ORDER #123456
      /#(\d{6,})/,                 // #123456
      /\b\d{8,}\b/,                // 12345678
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return null;
  }

  // Helper methods for dynamic order search
  private async searchOrdersInStore(storeId: string, orderNumber: string): Promise<OrderDocument[]> {
    try {
      return await this.orderModel
        .find({
          storeId,
          orderNumber: { $regex: orderNumber, $options: 'i' }
        })
        .populate('storeId', 'name handle')
        .limit(1)
        .exec();
    } catch (error) {
      this.logger.error(`Error searching orders in store ${storeId}:`, error);
      return [];
    }
  }

  private async searchOrdersGlobally(orderNumber: string): Promise<OrderDocument[]> {
    try {
      return await this.orderModel
        .find({
          orderNumber: { $regex: orderNumber, $options: 'i' }
        })
        .populate('storeId', 'name handle')
        .limit(1)
        .exec();
    } catch (error) {
      this.logger.error(`Error searching orders globally:`, error);
      return [];
    }
  }

  private async getCustomerOrdersInStore(storeId: string, customerPhone: string, limit: number = 3): Promise<OrderDocument[]> {
    try {
      const cleanPhone = customerPhone.replace('telegram:', '');
      this.logger.log(`🔍 Searching for orders: storeId=${storeId}, customerPhone=${customerPhone}, cleanPhone=${cleanPhone}`);

      // Try multiple search strategies to find customer orders
      let orders = [];

      // Strategy 1: Direct phone match
      orders = await this.orderModel
        .find({
          storeId,
          customerPhone: { $regex: cleanPhone, $options: 'i' }
        })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate('storeId', 'name handle')
        .exec();

      this.logger.log(`📊 Strategy 1 (direct phone match): Found ${orders.length} orders`);

      // Strategy 2: If no orders found, try searching by any field that might contain the user ID
      if (orders.length === 0) {
        orders = await this.orderModel
          .find({
            storeId,
            $or: [
              { customerPhone: { $regex: cleanPhone, $options: 'i' } },
              { customerEmail: { $regex: cleanPhone, $options: 'i' } },
              { metadata: { $regex: cleanPhone, $options: 'i' } },
              { notes: { $regex: cleanPhone, $options: 'i' } }
            ]
          })
          .sort({ createdAt: -1 })
          .limit(limit)
          .populate('storeId', 'name handle')
          .exec();

        this.logger.log(`📊 Strategy 2 (multi-field search): Found ${orders.length} orders`);
      }

      // Strategy 3: If still no orders, get recent orders from this store (for demo purposes)
      if (orders.length === 0) {
        this.logger.warn(`No orders found for customer ${cleanPhone} in store ${storeId}, getting recent store orders for demo`);
        orders = await this.orderModel
          .find({ storeId })
          .sort({ createdAt: -1 })
          .limit(limit)
          .populate('storeId', 'name handle')
          .exec();

        this.logger.log(`📊 Strategy 3 (recent store orders): Found ${orders.length} orders`);

        // Log the phone numbers in these orders to understand the format
        if (orders.length > 0) {
          orders.forEach((order, index) => {
            this.logger.log(`📱 Order ${index + 1}: ${order.orderNumber} - customerPhone: "${order.customerPhone}"`);
          });
        }
      }

      return orders;
    } catch (error) {
      this.logger.error(`Error getting customer orders:`, error);
      return [];
    }
  }

  // New template handlers
  private async handleCategoryTemplate(storeId: string): Promise<TemplateResponse> {
    try {
      // Get all active products for the store to analyze categories
      const products = await this.productsService.findAll(storeId, {
        limit: 50,
        status: 'active'
      });

      if (products.data && products.data.length > 0) {
        // Create a simple category overview based on product names and descriptions
        const categoryKeywords = {
          'Electronics': ['iphone', 'phone', 'mobile', 'laptop', 'computer', 'tablet', 'electronics'],
          'Fashion': ['shirt', 'dress', 'clothing', 'fashion', 'wear', 'apparel'],
          'Accessories': ['case', 'cover', 'accessory', 'accessories', 'charger', 'cable'],
          'Home & Living': ['home', 'furniture', 'decor', 'kitchen', 'living'],
          'Sports': ['sports', 'fitness', 'gym', 'exercise', 'outdoor']
        };

        const detectedCategories = new Map();

        products.data.forEach(product => {
          const productText = `${product.name} ${product.description || ''}`.toLowerCase();
          let categorized = false;

          for (const [category, keywords] of Object.entries(categoryKeywords)) {
            if (keywords.some(keyword => productText.includes(keyword))) {
              if (!detectedCategories.has(category)) {
                detectedCategories.set(category, []);
              }
              detectedCategories.get(category).push(product);
              categorized = true;
              break;
            }
          }

          if (!categorized) {
            if (!detectedCategories.has('Other Products')) {
              detectedCategories.set('Other Products', []);
            }
            detectedCategories.get('Other Products').push(product);
          }
        });

        const categoryMessages = Array.from(detectedCategories.entries())
          .slice(0, 5) // Limit to 5 categories
          .map(([categoryName, categoryProducts]) => {
            const productCount = categoryProducts.length;
            const sampleProduct = categoryProducts[0];
            const priceRange = this.calculatePriceRange(categoryProducts);
            return `📂 **${categoryName}** (${productCount} products)\n💰 Price range: ${priceRange}\n📱 Example: ${sampleProduct.name}`;
          }).join('\n\n');

        const firstCategory = Array.from(detectedCategories.keys())[0];
        return {
          message: `📂 **Product Categories**\n\n${categoryMessages}\n\n💬 **Try typing:**\n• Category name to browse\n• "all products" - See everything\n• "featured" - Featured products\n• "search [product]" - Search specific items`,
          requiresHuman: false,
          apiCall: 'categories_lookup',
          suggestedActions: [firstCategory, 'all products', 'featured', 'search']
        };
      }

      return {
        message: `📂 **Product Categories**\n\nWe're organizing our product categories. In the meantime:\n\n💬 **Try typing:**\n• "all products" - See complete catalog\n• "featured" - Featured products\n• "search [product]" - Search specific items\n• "support" - Talk to human`,
        requiresHuman: false,
        suggestedActions: ['all products', 'featured', 'search', 'support']
      };
    } catch (error) {
      this.logger.warn(`Error in handleCategoryTemplate:`, error.message);
      return {
        message: `📂 **Categories**\n\nI'm having trouble loading categories right now.\n\n💬 **Try typing:**\n• "all products" - See all products\n• "support" - Talk to human agent`,
        requiresHuman: false,
        suggestedActions: ['all products', 'support']
      };
    }
  }

  private async handleVariantTemplate(text: string, storeId: string): Promise<TemplateResponse> {
    try {
      // Search for products that might have variants
      const products = await this.productsService.findAll(storeId, {
        search: text,
        limit: 5,
        status: 'active'
      });

      if (products.data && products.data.length > 0) {
        const productWithVariants = products.data.find(product =>
          product.variants && product.variants.length > 0
        );

        if (productWithVariants) {
          const variantMessages = productWithVariants.variants
            .slice(0, 5)
            .map(variant => {
              const price = variant.price || productWithVariants.basePrice;
              const inStock = variant.stockQuantity > 0;
              return `🔸 **${variant.name}**\n💰 Price: ₹${price}\n${inStock ? `✅ In Stock (${variant.stockQuantity})` : '❌ Out of Stock'}`;
            }).join('\n\n');

          return {
            message: `🎨 **Product Variants - ${productWithVariants.name}**\n\n${variantMessages}\n\n💬 **Try typing:**\n• Variant name for details\n• "order [variant]" - Place order\n• "compare variants" - Compare options\n• "support" - Talk to human`,
            requiresHuman: false,
            apiCall: 'variants_lookup',
            suggestedActions: [productWithVariants.variants[0].name, 'order', 'compare variants', 'support']
          };
        }
      }

      return {
        message: `🎨 **Product Variants**\n\nI couldn't find specific variants for "${text}". Most of our products have multiple options available!\n\n💬 **Try typing:**\n• Specific product name\n• "products" - Browse all products\n• "colors" - See color options\n• "sizes" - See size options`,
        requiresHuman: false,
        suggestedActions: ['products', 'colors', 'sizes', 'support']
      };
    } catch (error) {
      this.logger.warn(`Error in handleVariantTemplate:`, error.message);
      return {
        message: `🎨 **Variants**\n\nI'm having trouble loading variant information.\n\n💬 **Try typing:**\n• "products" - Browse products\n• "support" - Talk to human agent`,
        requiresHuman: false,
        suggestedActions: ['products', 'support']
      };
    }
  }

  // Helper methods
  private extractProductSearchTerms(text: string): string {
    // Remove common words and extract meaningful search terms
    const commonWords = ['products', 'show', 'me', 'all', 'available', 'in', 'stock', 'price', 'cost'];
    const words = text.toLowerCase().split(' ').filter(word =>
      word.length > 2 && !commonWords.includes(word)
    );
    return words.join(' ') || text;
  }

  private calculatePriceRange(products: any[]): string {
    if (!products || products.length === 0) return 'N/A';

    const prices = products.map(p => p.discountedPrice || p.basePrice).filter(p => p > 0);
    if (prices.length === 0) return 'N/A';

    const min = Math.min(...prices);
    const max = Math.max(...prices);

    return min === max ? `₹${min}` : `₹${min} - ₹${max}`;
  }

  // Delivery and Payment formatting methods
  private formatDeliveryInfo(order: any, store: any): string {
    const status = order.status || 'Unknown';
    const trackingNumber = order.trackingNumber || 'Not assigned yet';
    const estimatedDelivery = order.estimatedDelivery
      ? new Date(order.estimatedDelivery).toLocaleDateString()
      : 'To be confirmed';
    const shippingAddress = `${order.shippingAddress}, ${order.shippingCity}, ${order.shippingPincode}`;

    let statusEmoji = '📦';
    let statusMessage = '';

    switch (status.toLowerCase()) {
      case 'confirmed':
        statusEmoji = '✅';
        statusMessage = 'Order confirmed and being prepared';
        break;
      case 'processing':
        statusEmoji = '🏭';
        statusMessage = 'Order is being processed';
        break;
      case 'shipped':
        statusEmoji = '🚚';
        statusMessage = 'Order has been shipped';
        break;
      case 'delivered':
        statusEmoji = '📬';
        statusMessage = 'Order delivered successfully';
        break;
      default:
        statusMessage = `Order status: ${status}`;
    }

    return `${statusEmoji} **${statusMessage}**\n📦 **Order:** ${order.orderNumber}\n🚚 **Tracking:** ${trackingNumber}\n📅 **Expected Delivery:** ${estimatedDelivery}\n📍 **Address:** ${shippingAddress}\n💰 **Shipping Fee:** ₹${order.shippingFee || store.shippingFee || 0}`;
  }

  private formatGeneralDeliveryInfo(store: any): string {
    const shippingFee = store.shippingFee || 0;
    const freeShippingAbove = store.freeShippingAbove || 500;
    const minOrderAmount = store.minOrderAmount || 0;

    return `📅 **Standard Delivery:** 3-5 business days\n⚡ **Express Delivery:** 1-2 business days (+₹200)\n💰 **Shipping Fee:** ₹${shippingFee}\n🆓 **Free Shipping:** On orders above ₹${freeShippingAbove}\n📦 **Minimum Order:** ₹${minOrderAmount}\n📍 **Coverage:** Pan India delivery`;
  }

  private formatPaymentInfo(order: any, store: any): string {
    const paymentMethod = order.paymentMethod || 'Not specified';
    const paymentStatus = order.paymentStatus || 'Unknown';
    const totalAmount = order.totalAmount || 0;
    const paymentId = order.paymentId || 'Not available';

    let statusEmoji = '💳';
    let statusMessage = '';

    switch (paymentStatus.toLowerCase()) {
      case 'pending':
        statusEmoji = '⏳';
        statusMessage = 'Payment is pending';
        break;
      case 'completed':
      case 'paid':
        statusEmoji = '✅';
        statusMessage = 'Payment completed successfully';
        break;
      case 'failed':
        statusEmoji = '❌';
        statusMessage = 'Payment failed';
        break;
      case 'refunded':
        statusEmoji = '🔄';
        statusMessage = 'Payment has been refunded';
        break;
      default:
        statusMessage = `Payment status: ${paymentStatus}`;
    }

    return `${statusEmoji} **${statusMessage}**\n📦 **Order:** ${order.orderNumber}\n💰 **Amount:** ₹${totalAmount}\n💳 **Method:** ${paymentMethod}\n🆔 **Payment ID:** ${paymentId}\n📅 **Date:** ${new Date(order.createdAt).toLocaleDateString()}`;
  }

  private formatGeneralPaymentInfo(store: any): string {
    const codEnabled = store.codEnabled ? '✅ Available' : '❌ Not available';
    const minOrderAmount = store.minOrderAmount || 0;

    return `✅ **Accepted Methods:**\n• Credit/Debit Cards\n• UPI (GPay, PhonePe, Paytm)\n• Net Banking\n• Wallets (Paytm, Amazon Pay)\n\n💰 **Cash on Delivery:** ${codEnabled}\n📦 **Minimum Order:** ₹${minOrderAmount}\n🔒 **Security:** 256-bit SSL encryption\n💸 **Refund Policy:** 7-14 business days`;
  }
}
