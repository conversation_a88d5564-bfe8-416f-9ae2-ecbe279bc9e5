import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export interface WhatsAppIntegrationMethods {
  generateVerificationCode(): void;
  verifyCode(code: string): boolean;
  isBusinessHoursActive(): boolean;
  incrementMessageCount(): Promise<WhatsAppIntegrationDocument>;
  recordError(error: string, code?: string): Promise<WhatsAppIntegrationDocument>;
  getPublicData(): any;
}

export type WhatsAppIntegrationDocument = WhatsAppIntegration & Document<any, any, WhatsAppIntegration> & WhatsAppIntegrationMethods & {
  _id: Types.ObjectId;
};

@Schema({
  timestamps: true,
  collection: 'whatsapp_integrations',
})
export class WhatsAppIntegration extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    unique: true,
    index: true
  })
  storeId: Types.ObjectId;

  // WhatsApp Details
  @Prop({ 
    required: true,
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid WhatsApp number']
  })
  phoneNumber: string;

  @Prop({ 
    trim: true,
    maxlength: 255
  })
  businessAccountId?: string;

  @Prop({ default: false })
  isVerified: boolean;

  @Prop({ 
    trim: true,
    maxlength: 10
  })
  verificationCode?: string;

  @Prop()
  verificationCodeExpiry?: Date;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  displayName?: string;

  @Prop({
    trim: true,
    maxlength: 500
  })
  businessDescription?: string;

  @Prop({
    trim: true,
    maxlength: 20
  })
  storeOwnerPhone?: string;

  // PingBot Configuration
  @Prop({ default: true })
  pingbotEnabled: boolean;

  @Prop({ default: true })
  autoRespondEnabled: boolean;

  @Prop({ default: true })
  forwardUnknownQueries: boolean;

  @Prop({ default: true })
  orderNotificationsEnabled: boolean;

  @Prop({ default: true })
  paymentNotificationsEnabled: boolean;

  @Prop({ default: true })
  shippingNotificationsEnabled: boolean;

  // API Configuration
  @Prop({ 
    trim: true,
    maxlength: 500
  })
  webhookUrl?: string;

  @Prop({ 
    trim: true,
    maxlength: 255,
    select: false // Don't include in queries by default
  })
  apiToken?: string;

  @Prop({ 
    trim: true,
    maxlength: 255,
    select: false
  })
  webhookSecret?: string;

  // Business Hours
  @Prop({ 
    type: Object,
    default: {
      enabled: false,
      timezone: 'Asia/Kolkata',
      schedule: {
        monday: { open: '09:00', close: '18:00', enabled: true },
        tuesday: { open: '09:00', close: '18:00', enabled: true },
        wednesday: { open: '09:00', close: '18:00', enabled: true },
        thursday: { open: '09:00', close: '18:00', enabled: true },
        friday: { open: '09:00', close: '18:00', enabled: true },
        saturday: { open: '09:00', close: '18:00', enabled: true },
        sunday: { open: '09:00', close: '18:00', enabled: false }
      }
    }
  })
  businessHours?: {
    enabled: boolean;
    timezone: string;
    schedule: Record<string, {
      open: string;
      close: string;
      enabled: boolean;
    }>;
  };

  // Auto-response Messages
  @Prop({ 
    type: Object,
    default: {
      welcome: 'Hello! Welcome to our store. How can I help you today?',
      orderStatus: 'Please share your order number and I\'ll check the status for you.',
      businessHours: 'Thank you for contacting us. We\'re currently closed but will respond during business hours.',
      fallback: 'I didn\'t understand that. Please contact our support team for assistance.'
    }
  })
  autoResponseMessages?: {
    welcome?: string;
    orderStatus?: string;
    businessHours?: string;
    fallback?: string;
  };

  // Status
  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  lastSync?: Date;

  @Prop()
  lastMessageAt?: Date;

  // Statistics
  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  totalMessages: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  totalConversations: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  resolvedQueries: number;

  // Error tracking
  @Prop({ 
    type: Object,
    default: {}
  })
  lastError?: {
    message: string;
    timestamp: Date;
    code?: string;
  };

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  errorCount: number;

  // Virtual fields
  store?: any;
}

export const WhatsAppIntegrationSchema = SchemaFactory.createForClass(WhatsAppIntegration);

// Indexes
WhatsAppIntegrationSchema.index({ storeId: 1 });
WhatsAppIntegrationSchema.index({ phoneNumber: 1 });
WhatsAppIntegrationSchema.index({ isVerified: 1 });
WhatsAppIntegrationSchema.index({ isActive: 1 });
WhatsAppIntegrationSchema.index({ createdAt: -1 });

// Virtual populate
WhatsAppIntegrationSchema.virtual('store', {
  ref: 'Store',
  localField: 'storeId',
  foreignField: '_id',
  justOne: true,
});

// Pre-save middleware
WhatsAppIntegrationSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Generate verification code if phone number is modified
  if (this.isModified('phoneNumber') && !this.isVerified) {
    (this as any).generateVerificationCode();
  }
  
  next();
});

// Instance methods
WhatsAppIntegrationSchema.methods.generateVerificationCode = function() {
  this.verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
  this.verificationCodeExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  this.isVerified = false;
};

WhatsAppIntegrationSchema.methods.verifyCode = function(code: string) {
  if (!this.verificationCode || !this.verificationCodeExpiry) {
    return false;
  }
  
  if (Date.now() > this.verificationCodeExpiry.getTime()) {
    return false; // Code expired
  }
  
  if (this.verificationCode !== code) {
    return false; // Invalid code
  }
  
  this.isVerified = true;
  this.verificationCode = undefined;
  this.verificationCodeExpiry = undefined;
  return true;
};

WhatsAppIntegrationSchema.methods.isBusinessHoursActive = function() {
  if (!this.businessHours?.enabled) {
    return true; // Always active if business hours not enabled
  }
  
  const now = new Date();
  const dayName = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const daySchedule = this.businessHours.schedule[dayName];
  
  if (!daySchedule?.enabled) {
    return false;
  }
  
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
  return currentTime >= daySchedule.open && currentTime <= daySchedule.close;
};

WhatsAppIntegrationSchema.methods.incrementMessageCount = function() {
  this.totalMessages += 1;
  this.lastMessageAt = new Date();
  return this.save();
};

WhatsAppIntegrationSchema.methods.recordError = function(error: string, code?: string) {
  this.lastError = {
    message: error,
    timestamp: new Date(),
    code
  };
  this.errorCount += 1;
  return this.save();
};

WhatsAppIntegrationSchema.methods.getPublicData = function() {
  return {
    id: this._id,
    phoneNumber: this.phoneNumber,
    isVerified: this.isVerified,
    isActive: this.isActive,
    pingbotEnabled: this.pingbotEnabled,
    businessHours: this.businessHours,
    lastSync: this.lastSync,
  };
};
