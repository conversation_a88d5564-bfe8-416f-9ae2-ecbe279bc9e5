import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { AgentExecutor, createReactAgent } from 'langchain/agents';
import { ChatPromptTemplate, MessagesPlaceholder } from '@langchain/core/prompts';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { DynamicTool } from '@langchain/core/tools';
import { ToolsService } from '../services/tools.service';
import { KnowledgeBaseService } from '../services/knowledge-base.service';
import { ConversationService } from '../services/conversation.service';
import { pull } from 'langchain/hub';

export interface PingBotContext {
  storeId: string;
  storeName: string;
  customerPhone: string;
  customerName?: string;
  orderId?: string;
  conversationId: string;
  messageHistory: BaseMessage[];
  metadata?: Record<string, any>;
}

export interface PingBotResponse {
  message: string;
  intent?: string;
  confidence?: number;
  requiresHuman?: boolean;
  suggestedActions?: string[];
  metadata?: Record<string, any>;
}

@Injectable()
export class PingBotAgent {
  private readonly logger = new Logger(PingBotAgent.name);
  private llm: ChatGoogleGenerativeAI;
  private agent: AgentExecutor;
  private tools: DynamicTool[];

  constructor(
    private readonly configService: ConfigService,
    private readonly toolsService: ToolsService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly conversationService: ConversationService,
  ) {
    this.initializeAgent();
  }

  private async initializeAgent() {
    try {
      // Initialize Gemini Flash (much faster than Pro)
      this.llm = new ChatGoogleGenerativeAI({
        apiKey: this.configService.get('GEMINI_API_KEY'),
        modelName: 'gemini-1.5-flash',
        temperature: 0.1,
        maxOutputTokens: 200,
      });

      // Get available tools
      this.tools = await this.toolsService.getAllTools();

      // Create a custom prompt for ReAct agent
      const prompt = ChatPromptTemplate.fromTemplate(`
${this.getSystemPrompt()}

TOOLS:
You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}`);

      // Create the ReAct agent
      const agent = await createReactAgent({
        llm: this.llm,
        tools: this.tools,
        prompt,
      });

      // Create agent executor with better error handling
      this.agent = new AgentExecutor({
        agent,
        tools: this.tools,
        verbose: true,
        maxIterations: 2,
        returnIntermediateSteps: true,
        handleParsingErrors: (error) => {
          console.log('Parsing error, using fallback:', error);
          return "I apologize, but I'm having trouble processing your request. Let me forward this to our team for assistance.";
        },
      });

      this.logger.log('PingBot Agent initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize PingBot Agent:', error);
      throw error;
    }
  }

  private getSystemPrompt(): string {
    return `You are PingBot, an AI customer service agent.

RULES:
1. Customer mentions order number → Use order_lookup tool
2. Customer asks about products → Use product_search tool
3. Customer wants customization → Use forward_to_seller tool
4. Customer has complaints → Use escalate_to_human tool

ALWAYS use tools. Be helpful and provide detailed information from tool results.`;
  }

  async processMessage(
    message: string,
    context: PingBotContext,
  ): Promise<PingBotResponse> {
    try {
      this.logger.debug(`Processing message for store ${context.storeId}: ${message}`);

      // Enhance context with store and order information
      const enhancedContext = await this.enhanceContext(context);

      // Prepare the input for the agent
      const input = {
        input: `Store: ${context.storeName}\nCustomer Message: ${message}`,
      };

      // Execute the agent
      const result = await this.agent.invoke(input);

      // Analyze the response for intent and confidence
      const analysis = await this.analyzeResponse(message, result.output);

      // Determine if human intervention is needed
      const requiresHuman = this.shouldEscalateToHuman(message, result.output, analysis);

      return {
        message: result.output,
        intent: analysis.intent,
        confidence: analysis.confidence,
        requiresHuman,
        suggestedActions: analysis.suggestedActions,
        metadata: {
          intermediateSteps: result.intermediateSteps,
          toolsUsed: this.extractToolsUsed(result.intermediateSteps),
          processingTime: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error('Error processing message:', error);
      
      // Fallback response
      return {
        message: "I apologize, but I'm experiencing technical difficulties. Let me connect you with a human agent who can assist you better.",
        requiresHuman: true,
        metadata: { error: error.message },
      };
    }
  }

  private async enhanceContext(context: PingBotContext): Promise<Record<string, any>> {
    const enhanced: Record<string, any> = {
      storeId: context.storeId,
      storeName: context.storeName,
      customerPhone: context.customerPhone,
      customerName: context.customerName,
    };

    // Add order information if available
    if (context.orderId) {
      try {
        const orderInfo = await this.toolsService.getOrderInfo(context.orderId);
        enhanced.currentOrder = orderInfo;
      } catch (error) {
        this.logger.warn(`Could not fetch order info for ${context.orderId}:`, error);
      }
    }

    // Add store-specific knowledge
    try {
      const storeKnowledge = await this.knowledgeBaseService.getStoreKnowledge(context.storeId);
      enhanced.storeKnowledge = storeKnowledge;
    } catch (error) {
      this.logger.warn(`Could not fetch store knowledge for ${context.storeId}:`, error);
    }

    return enhanced;
  }

  private async analyzeResponse(input: string, output: string): Promise<{
    intent: string;
    confidence: number;
    suggestedActions: string[];
  }> {
    // Simple intent classification - can be enhanced with more sophisticated NLP
    const intents = {
      order_inquiry: ['order', 'track', 'status', 'delivery', 'shipping'],
      product_question: ['product', 'item', 'price', 'available', 'stock'],
      complaint: ['problem', 'issue', 'wrong', 'damaged', 'complaint'],
      return_refund: ['return', 'refund', 'exchange', 'cancel'],
      general_info: ['hours', 'location', 'contact', 'policy'],
    };

    let detectedIntent = 'general';
    let maxScore = 0;

    for (const [intent, keywords] of Object.entries(intents)) {
      const score = keywords.reduce((acc, keyword) => {
        return acc + (input.toLowerCase().includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > maxScore) {
        maxScore = score;
        detectedIntent = intent;
      }
    }

    const confidence = Math.min(maxScore / 3, 1); // Normalize confidence

    return {
      intent: detectedIntent,
      confidence,
      suggestedActions: this.getSuggestedActions(detectedIntent),
    };
  }

  private getSuggestedActions(intent: string): string[] {
    const actionMap = {
      order_inquiry: ['Check order status', 'Provide tracking info', 'Update delivery estimate'],
      product_question: ['Show product details', 'Check availability', 'Suggest alternatives'],
      complaint: ['Acknowledge issue', 'Gather details', 'Offer solution'],
      return_refund: ['Explain return policy', 'Initiate return process', 'Process refund'],
      general_info: ['Provide store info', 'Share contact details', 'Explain policies'],
    };

    return actionMap[intent] || ['Provide general assistance'];
  }

  private shouldEscalateToHuman(input: string, output: string, analysis: any): boolean {
    // Escalation criteria
    const escalationKeywords = [
      'speak to human', 'talk to person', 'manager', 'supervisor',
      'not satisfied', 'angry', 'frustrated', 'legal action',
      'complaint', 'dispute', 'unacceptable'
    ];

    const hasEscalationKeyword = escalationKeywords.some(keyword => 
      input.toLowerCase().includes(keyword)
    );

    const lowConfidence = analysis.confidence < 0.3;
    const containsApology = output.toLowerCase().includes('sorry') || 
                           output.toLowerCase().includes('apologize');

    return hasEscalationKeyword || (lowConfidence && containsApology);
  }

  private extractToolsUsed(intermediateSteps: any[]): string[] {
    if (!intermediateSteps) return [];
    
    return intermediateSteps
      .map(step => step.action?.tool)
      .filter(tool => tool)
      .filter((tool, index, arr) => arr.indexOf(tool) === index); // Remove duplicates
  }

  async getAgentStatus(): Promise<{
    isInitialized: boolean;
    toolsCount: number;
    modelName: string;
  }> {
    return {
      isInitialized: !!this.agent,
      toolsCount: this.tools?.length || 0,
      modelName: this.llm?.modelName || 'unknown',
    };
  }
}
