import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { StoresController } from './stores.controller';
import { StoresService } from './stores.service';

// Schemas
import { Store, StoreSchema } from '@database/schemas/store.schema';
import { User, UserSchema } from '@database/schemas/user.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Store.name, schema: StoreSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  controllers: [StoresController],
  providers: [StoresService],
  exports: [StoresService],
})
export class StoresModule {}
