const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

// Test specific templates that were failing before
const testMessages = [
  'hi',
  'TG-2024-001',
  'support',
  'help',
  'contact',
  'hours',
  'location',
  'cancel',
  'modify',
  'laptop',
  'smartphone',
  'where is my package',
  'I need help'
];

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Sending: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload);
    console.log(`📥 Received: ${response.status}`);
    
    // Small delay between messages
    await new Promise(resolve => setTimeout(resolve, 2000));
  } catch (error) {
    console.error(`❌ Error sending "${text}":`, error.message);
  }
}

async function testSpecificTemplates() {
  console.log('🧪 TESTING SPECIFIC TEMPLATES THAT WERE FAILING\n');
  console.log('============================================================\n');

  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`${i + 1}️⃣ Testing "${message}"...`);
    await sendMessage(message);
    console.log('');
  }

  console.log('🎉 All specific template tests completed!');
  console.log('📊 Check server logs for detailed template responses');
}

testSpecificTemplates().catch(console.error);
