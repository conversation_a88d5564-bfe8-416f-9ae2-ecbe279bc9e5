import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UserDocument } from '@database/schemas/user.schema';
import { PingBotService, ProcessMessageDto } from './pingbot.service';
import { ConversationStatus } from './schemas/conversation.schema';
import { MessageType } from './schemas/pingbot-message.schema';

class ProcessMessageBodyDto {
  storeId: string;
  customerPhone: string;
  customerName?: string;
  message: string;
  orderId?: string;
  whatsappMessageId?: string;
  messageType?: MessageType;
  metadata?: Record<string, any>;
}

class EscalateConversationDto {
  reason: string;
  assignedTo?: string;
}

class AddFeedbackDto {
  rating: number;
  feedback?: string;
}

@ApiTags('PingBot AI')
@Controller('pingbot')
export class PingBotController {
  private readonly logger = new Logger(PingBotController.name);

  constructor(private readonly pingBotService: PingBotService) {}

  @Post('process-message')
  @ApiOperation({ summary: 'Process incoming customer message with PingBot AI' })
  @ApiBody({ type: ProcessMessageBodyDto })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid message data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async processMessage(@Body() dto: ProcessMessageBodyDto) {
    try {
      this.logger.debug(`Processing message from ${dto.customerPhone} for store ${dto.storeId}`);
      
      const result = await this.pingBotService.processMessage(dto);
      
      return {
        message: 'Message processed successfully',
        data: {
          response: result.response.message,
          intent: result.response.intent,
          confidence: result.response.confidence,
          requiresHuman: result.response.requiresHuman,
          suggestedActions: result.response.suggestedActions,
          conversationId: result.conversationId,
          messageId: result.messageId,
        },
      };
    } catch (error) {
      this.logger.error('Error processing message:', error);
      throw new HttpException(
        'Failed to process message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('conversations/:conversationId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get conversation details and messages' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiResponse({ status: 200, description: 'Conversation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async getConversation(
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: UserDocument,
  ) {
    try {
      const result = await this.pingBotService.getConversation(conversationId);
      
      return {
        message: 'Conversation retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error getting conversation:', error);
      throw new HttpException(
        error.message || 'Failed to get conversation',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Get('stores/:storeId/conversations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get conversations for a store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'status', required: false, enum: ConversationStatus })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  async getStoreConversations(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('status') status?: ConversationStatus,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
  ) {
    try {
      const result = await this.pingBotService.getStoreConversations(
        storeId,
        status,
        page,
        limit,
      );
      
      return {
        message: 'Conversations retrieved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error getting store conversations:', error);
      throw new HttpException(
        'Failed to get conversations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('conversations/:conversationId/escalate')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Escalate conversation to human agent' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiBody({ type: EscalateConversationDto })
  @ApiResponse({ status: 200, description: 'Conversation escalated successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async escalateConversation(
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: UserDocument,
    @Body() dto: EscalateConversationDto,
  ) {
    try {
      const result = await this.pingBotService.escalateConversation(
        conversationId,
        dto.reason,
        dto.assignedTo,
      );
      
      if (!result) {
        throw new HttpException('Conversation not found', HttpStatus.NOT_FOUND);
      }
      
      return {
        message: 'Conversation escalated successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error escalating conversation:', error);
      throw new HttpException(
        error.message || 'Failed to escalate conversation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('conversations/:conversationId/resolve')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Mark conversation as resolved' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiResponse({ status: 200, description: 'Conversation resolved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async resolveConversation(
    @Param('conversationId') conversationId: string,
    @CurrentUser() user: UserDocument,
  ) {
    try {
      const result = await this.pingBotService.resolveConversation(conversationId);
      
      if (!result) {
        throw new HttpException('Conversation not found', HttpStatus.NOT_FOUND);
      }
      
      return {
        message: 'Conversation resolved successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error resolving conversation:', error);
      throw new HttpException(
        error.message || 'Failed to resolve conversation',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('conversations/:conversationId/feedback')
  @ApiOperation({ summary: 'Add customer feedback to conversation' })
  @ApiParam({ name: 'conversationId', description: 'Conversation ID' })
  @ApiBody({ type: AddFeedbackDto })
  @ApiResponse({ status: 200, description: 'Feedback added successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  async addFeedback(
    @Param('conversationId') conversationId: string,
    @Body() dto: AddFeedbackDto,
  ) {
    try {
      const result = await this.pingBotService.addConversationFeedback(
        conversationId,
        dto.rating,
        dto.feedback,
      );
      
      if (!result) {
        throw new HttpException('Conversation not found', HttpStatus.NOT_FOUND);
      }
      
      return {
        message: 'Feedback added successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error adding feedback:', error);
      throw new HttpException(
        error.message || 'Failed to add feedback',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get PingBot statistics' })
  @ApiQuery({ name: 'storeId', required: false, type: String })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStats(
    @CurrentUser() user: UserDocument,
    @Query('storeId') storeId?: string,
  ) {
    try {
      const stats = await this.pingBotService.getStats(storeId);
      
      return {
        message: 'Statistics retrieved successfully',
        data: stats,
      };
    } catch (error) {
      this.logger.error('Error getting stats:', error);
      throw new HttpException(
        'Failed to get statistics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get PingBot agent status' })
  @ApiResponse({ status: 200, description: 'Agent status retrieved successfully' })
  async getAgentStatus(@CurrentUser() user: UserDocument) {
    try {
      const status = await this.pingBotService.getAgentStatus();
      
      return {
        message: 'Agent status retrieved successfully',
        data: status,
      };
    } catch (error) {
      this.logger.error('Error getting agent status:', error);
      throw new HttpException(
        'Failed to get agent status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('process-unprocessed')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Process unprocessed messages (admin only)' })
  @ApiResponse({ status: 200, description: 'Unprocessed messages processed successfully' })
  async processUnprocessedMessages(@CurrentUser() user: UserDocument) {
    try {
      const processedCount = await this.pingBotService.processUnprocessedMessages();

      return {
        message: 'Unprocessed messages processed successfully',
        data: {
          processedCount,
        },
      };
    } catch (error) {
      this.logger.error('Error processing unprocessed messages:', error);
      throw new HttpException(
        'Failed to process unprocessed messages',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('test')
  @ApiOperation({ summary: 'Test PingBot AI with a sample message' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Test message' },
        storeId: { type: 'string', description: 'Store ID (optional)' },
        customerPhone: { type: 'string', description: 'Customer phone (optional)' },
      },
      required: ['message'],
    },
  })
  @ApiResponse({ status: 200, description: 'Test message processed successfully' })
  async testPingBot(@Body() body: { message: string; storeId?: string; customerPhone?: string }) {
    try {
      this.logger.debug(`Testing PingBot with message: ${body.message}`);

      // For testing, we'll simulate a response without hitting the database
      if (!body.storeId) {
        // Return a simulated response for testing
        return {
          message: 'Test message processed successfully (simulated)',
          data: {
            response: `Thank you for your message: "${body.message}". This is a simulated response from PingBot AI powered by Gemini. The AI agent is configured and ready to handle real conversations!`,
            intent: 'general_inquiry',
            confidence: 0.85,
            requiresHuman: false,
            suggestedActions: ['provide_more_details', 'check_order_status'],
            conversationId: 'test-conversation-' + Date.now(),
            messageId: 'test-message-' + Date.now(),
            aiProvider: 'gemini',
            status: 'ready'
          },
        };
      }

      // Use provided store ID for real testing
      const result = await this.pingBotService.processMessage({
        storeId: body.storeId,
        customerPhone: body.customerPhone || '+**********',
        message: body.message,
        messageType: MessageType.TEXT,
        metadata: {
          isTest: true,
          timestamp: new Date().toISOString(),
        },
      });

      return {
        message: 'Test message processed successfully',
        data: {
          response: result.response.message,
          intent: result.response.intent,
          confidence: result.response.confidence,
          requiresHuman: result.response.requiresHuman,
          suggestedActions: result.response.suggestedActions,
          conversationId: result.conversationId,
          messageId: result.messageId,
        },
      };
    } catch (error) {
      this.logger.error('Error testing PingBot:', error);
      throw new HttpException(
        error.message || 'Failed to test PingBot',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
