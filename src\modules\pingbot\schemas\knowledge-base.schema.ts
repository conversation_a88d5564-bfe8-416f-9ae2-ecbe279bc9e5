import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type KnowledgeBaseDocument = KnowledgeBase & Document;

export enum KnowledgeType {
  STORE_INFO = 'store_info',
  PRODUCT_INFO = 'product_info',
  POLICY = 'policy',
  FAQ = 'faq',
  PROCEDURE = 'procedure',
  TEMPLATE = 'template',
  GENERAL = 'general',
}

export enum KnowledgeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

@Schema({
  collection: 'pingbot_knowledge_base',
  timestamps: true,
})
export class KnowledgeBase {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store',
    index: true 
  })
  storeId?: Types.ObjectId;

  @Prop({ 
    required: true,
    trim: true,
    index: true 
  })
  title: string;

  @Prop({ 
    required: true 
  })
  content: string;

  @Prop({ 
    type: String,
    enum: KnowledgeType,
    required: true,
    index: true 
  })
  type: KnowledgeType;

  @Prop({ 
    type: String,
    enum: KnowledgeStatus,
    default: KnowledgeStatus.ACTIVE,
    index: true 
  })
  status: KnowledgeStatus;

  @Prop({ 
    type: [String],
    default: [],
    index: true 
  })
  tags: string[];

  @Prop({ 
    type: [String],
    default: [] 
  })
  keywords: string[];

  @Prop({ 
    trim: true 
  })
  category?: string;

  @Prop({ 
    trim: true 
  })
  subcategory?: string;

  @Prop({ 
    type: Object,
    default: {} 
  })
  metadata: Record<string, any>;

  @Prop({ 
    type: [Number],
    default: [] 
  })
  embedding?: number[];

  @Prop({ 
    default: 0 
  })
  priority: number;

  @Prop({ 
    default: 0 
  })
  usageCount: number;

  @Prop({ 
    type: Date 
  })
  lastUsedAt?: Date;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'User' 
  })
  createdBy?: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'User' 
  })
  updatedBy?: Types.ObjectId;

  @Prop({ 
    default: true 
  })
  isGlobal: boolean;

  @Prop({ 
    type: [String],
    default: [] 
  })
  relatedQuestions: string[];

  @Prop({ 
    type: Object,
    default: {} 
  })
  conditions: Record<string, any>;

  @Prop({ 
    type: Date 
  })
  expiresAt?: Date;

  @Prop({ 
    min: 0,
    max: 1 
  })
  confidence?: number;
}

export const KnowledgeBaseSchema = SchemaFactory.createForClass(KnowledgeBase);

// Indexes for performance and search
KnowledgeBaseSchema.index({ storeId: 1, type: 1, status: 1 });
KnowledgeBaseSchema.index({ tags: 1 });
KnowledgeBaseSchema.index({ keywords: 1 });
KnowledgeBaseSchema.index({ category: 1, subcategory: 1 });
KnowledgeBaseSchema.index({ priority: -1, usageCount: -1 });
KnowledgeBaseSchema.index({ isGlobal: 1, status: 1 });
KnowledgeBaseSchema.index({ expiresAt: 1 });

// Text search index
KnowledgeBaseSchema.index({ 
  title: 'text', 
  content: 'text', 
  keywords: 'text',
  tags: 'text'
});

// Methods
KnowledgeBaseSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsedAt = new Date();
  return this.save();
};

KnowledgeBaseSchema.methods.updateEmbedding = function(embedding: number[]) {
  this.embedding = embedding;
  return this.save();
};

KnowledgeBaseSchema.methods.isExpired = function() {
  return this.expiresAt && this.expiresAt < new Date();
};

KnowledgeBaseSchema.methods.isApplicableToStore = function(storeId: string) {
  return this.isGlobal || (this.storeId && this.storeId.toString() === storeId);
};
