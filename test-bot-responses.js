const axios = require('axios');

async function testBotFlow() {
  const baseUrl = 'http://localhost:3000/api/pingbot/telegram/webhook';
  
  console.log('🤖 TESTING TELEGRAM BOT FLOW');
  console.log('=' .repeat(50));
  
  // Test 1: Send "hi" - should ask for order number
  console.log('\n1️⃣ TESTING: "hi" message');
  const hiMessage = {
    update_id: 123456789,
    message: {
      message_id: 1,
      from: { id: *********, first_name: 'Test User', username: 'testuser' },
      chat: { id: *********, type: 'private' },
      text: 'hi',
      date: Math.floor(Date.now() / 1000)
    }
  };
  
  try {
    const response1 = await axios.post(baseUrl, hiMessage);
    console.log('✅ Response:', response1.status);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 2: Send real order number
  console.log('\n2️⃣ TESTING: Real order number "TG-2024-001"');
  const orderMessage = {
    update_id: 123456790,
    message: {
      message_id: 2,
      from: { id: *********, first_name: 'Test User', username: 'testuser' },
      chat: { id: *********, type: 'private' },
      text: 'TG-2024-001',
      date: Math.floor(Date.now() / 1000)
    }
  };
  
  try {
    const response2 = await axios.post(baseUrl, orderMessage);
    console.log('✅ Response:', response2.status);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 3: Ask about order status
  console.log('\n3️⃣ TESTING: "What is my order status?"');
  const statusMessage = {
    update_id: 123456791,
    message: {
      message_id: 3,
      from: { id: *********, first_name: 'Test User', username: 'testuser' },
      chat: { id: *********, type: 'private' },
      text: 'What is my order status?',
      date: Math.floor(Date.now() / 1000)
    }
  };
  
  try {
    const response3 = await axios.post(baseUrl, statusMessage);
    console.log('✅ Response:', response3.status);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 4: Complex query that should forward to seller
  console.log('\n4️⃣ TESTING: Complex customization request');
  const complexMessage = {
    update_id: *********,
    message: {
      message_id: 4,
      from: { id: *********, first_name: 'Test User', username: 'testuser' },
      chat: { id: *********, type: 'private' },
      text: 'Can you customize the iPhone with my company logo and change the color to purple?',
      date: Math.floor(Date.now() / 1000)
    }
  };
  
  try {
    const response4 = await axios.post(baseUrl, complexMessage);
    console.log('✅ Response:', response4.status);
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  
  console.log('\n🎉 All tests completed! Check the server logs above to see bot responses.');
}

testBotFlow().catch(console.error);
