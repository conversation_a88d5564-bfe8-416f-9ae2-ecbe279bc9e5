import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export type ProductVariantDocument = ProductVariant & Document;

@Schema({
  timestamps: true,
  collection: 'product_variants',
})
export class ProductVariant extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Product', 
    required: true,
    index: true
  })
  productId: Types.ObjectId;

  @Prop({ 
    required: true, 
    trim: true,
    minlength: 1,
    maxlength: 255
  })
  name: string; // e.g., "Size: Large", "Color: Red"

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  sku?: string;

  @Prop({ 
    type: Number,
    min: 0,
    max: 1000000
  })
  price?: number; // Override product price if set

  @Prop({ 
    type: Number,
    min: 0,
    max: 1000000
  })
  discountedPrice?: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 100000
  })
  stockQuantity: number;

  @Prop({ 
    type: Number,
    min: 0,
    max: 50000 // 50kg max
  })
  weight?: number; // in grams

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 100
  })
  sortOrder: number;

  // Variant attributes (flexible structure)
  @Prop({ 
    type: Object,
    default: {}
  })
  attributes?: Record<string, any>; // e.g., { size: "L", color: "red" }

  // Additional variant metadata
  @Prop({ trim: true })
  imageUrl?: string; // Specific image for this variant

  @Prop({ trim: true, maxlength: 500 })
  description?: string;
}

export const ProductVariantSchema = SchemaFactory.createForClass(ProductVariant);

// Indexes
ProductVariantSchema.index({ productId: 1 });
ProductVariantSchema.index({ productId: 1, isActive: 1 });
ProductVariantSchema.index({ productId: 1, sortOrder: 1 });
ProductVariantSchema.index({ sku: 1 });
ProductVariantSchema.index({ createdAt: -1 });

// Compound unique index to prevent duplicate SKUs
ProductVariantSchema.index({ sku: 1 }, {
  unique: true,
  sparse: true,
  partialFilterExpression: { sku: { $exists: true, $ne: null, $nin: ["", null] } }
});

// Pre-save middleware
ProductVariantSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Ensure discounted price is less than price
  if (this.discountedPrice && this.price && this.discountedPrice >= this.price) {
    this.discountedPrice = undefined;
  }
  
  next();
});

// Instance methods
ProductVariantSchema.methods.getEffectivePrice = function() {
  return this.discountedPrice || this.price;
};

ProductVariantSchema.methods.isInStock = function() {
  return this.stockQuantity > 0;
};

ProductVariantSchema.methods.getDiscountPercentage = function() {
  if (!this.discountedPrice || !this.price) return 0;
  return Math.round(((this.price - this.discountedPrice) / this.price) * 100);
};
