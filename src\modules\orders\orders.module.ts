import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';
import { ProductsModule } from '@modules/products/products.module';

// Schemas
import { Order, OrderSchema } from '@database/schemas/order.schema';
import { OrderItem, OrderItemSchema } from '@database/schemas/order-item.schema';
import { Customer, CustomerSchema } from '@database/schemas/customer.schema';
import { Product, ProductSchema } from '@database/schemas/product.schema';
import { ProductVariant, ProductVariantSchema } from '@database/schemas/product-variant.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Order.name, schema: OrderSchema },
      { name: OrderItem.name, schema: OrderItemSchema },
      { name: Customer.name, schema: CustomerSchema },
      { name: Product.name, schema: ProductSchema },
      { name: ProductVariant.name, schema: ProductVariantSchema },
      { name: Store.name, schema: StoreSchema },
    ]),
    StoresModule,
    ProductsModule,
  ],
  controllers: [OrdersController],
  providers: [OrdersService],
  exports: [OrdersService],
})
export class OrdersModule {}
