import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';
import { CUSTOMER_SEGMENTS } from '@common/constants';

export interface CustomerMethods {
  updateCustomerSegment(): void;
  addOrder(orderAmount: number): void;
  getLifetimeValue(): number;
  getDaysSinceLastOrder(): number;
}

export type CustomerDocument = Customer & Document<any, any, Customer> & CustomerMethods & {
  _id: Types.ObjectId;
};

@Schema({
  timestamps: true,
  collection: 'customers',
})
export class Customer extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  // Customer Info
  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  name: string;

  @Prop({ 
    required: true, 
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number']
  })
  phone: string;

  @Prop({ 
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  })
  email?: string;

  // Address
  @Prop({ 
    trim: true,
    maxlength: 500
  })
  address?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  city?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  state?: string;

  @Prop({ 
    trim: true,
    maxlength: 20
  })
  pincode?: string;

  @Prop({ 
    trim: true,
    default: 'India',
    maxlength: 100
  })
  country: string;

  // Preferences
  @Prop({ default: true })
  whatsappOptIn: boolean;

  @Prop({ default: true })
  emailOptIn: boolean;

  // Analytics
  @Prop({ 
    type: Number, 
    default: 0,
    min: 0
  })
  totalOrders: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 10000000
  })
  totalSpent: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 1000000
  })
  averageOrderValue: number;

  @Prop()
  lastOrderDate?: Date;

  // Segmentation
  @Prop({ 
    default: CUSTOMER_SEGMENTS.NEW,
    enum: Object.values(CUSTOMER_SEGMENTS)
  })
  customerSegment: string;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Category'
  })
  favoriteCategoryId?: Types.ObjectId;

  // Additional customer metadata
  @Prop({ 
    type: Object,
    default: {}
  })
  metadata?: Record<string, any>;

  @Prop({ 
    type: [String],
    default: []
  })
  tags: string[];

  // Birthday for marketing
  @Prop()
  birthday?: Date;

  // Acquisition source
  @Prop({ 
    trim: true,
    maxlength: 100
  })
  acquisitionSource?: string; // e.g., 'instagram', 'whatsapp', 'referral'

  // Virtual fields
  orders?: any[];
  reviews?: any[];
}

export const CustomerSchema = SchemaFactory.createForClass(Customer);

// Indexes
CustomerSchema.index({ storeId: 1 });
CustomerSchema.index({ storeId: 1, phone: 1 }, { unique: true });
CustomerSchema.index({ storeId: 1, email: 1 });
CustomerSchema.index({ storeId: 1, customerSegment: 1 });
CustomerSchema.index({ totalSpent: -1 });
CustomerSchema.index({ totalOrders: -1 });
CustomerSchema.index({ lastOrderDate: -1 });
CustomerSchema.index({ createdAt: -1 });

// Text search index
CustomerSchema.index({ 
  name: 'text',
  phone: 'text',
  email: 'text'
});

// Compound indexes for common queries
CustomerSchema.index({ storeId: 1, createdAt: -1 });
CustomerSchema.index({ storeId: 1, totalSpent: -1 });
CustomerSchema.index({ storeId: 1, customerSegment: 1, totalSpent: -1 });

// Virtual populate
CustomerSchema.virtual('orders', {
  ref: 'Order',
  localField: 'phone',
  foreignField: 'customerPhone',
  match: function() {
    return { storeId: this.storeId };
  }
});

CustomerSchema.virtual('reviews', {
  ref: 'Review',
  localField: '_id',
  foreignField: 'customerId',
});

// Pre-save middleware
CustomerSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Calculate average order value
  if (this.totalOrders > 0) {
    this.averageOrderValue = this.totalSpent / this.totalOrders;
  }
  
  // Update customer segment based on spending and order history
  (this as any).updateCustomerSegment();
  
  next();
});

// Instance methods
CustomerSchema.methods.updateCustomerSegment = function() {
  const daysSinceLastOrder = this.lastOrderDate 
    ? Math.floor((Date.now() - this.lastOrderDate.getTime()) / (1000 * 60 * 60 * 24))
    : Infinity;
  
  if (daysSinceLastOrder > 180) {
    this.customerSegment = CUSTOMER_SEGMENTS.INACTIVE;
  } else if (this.totalSpent >= 10000 || this.totalOrders >= 10) {
    this.customerSegment = CUSTOMER_SEGMENTS.VIP;
  } else if (this.totalOrders >= 2) {
    this.customerSegment = CUSTOMER_SEGMENTS.REGULAR;
  } else {
    this.customerSegment = CUSTOMER_SEGMENTS.NEW;
  }
};

CustomerSchema.methods.addOrder = function(orderAmount: number) {
  this.totalOrders += 1;
  this.totalSpent += orderAmount;
  this.lastOrderDate = new Date();
  this.averageOrderValue = this.totalSpent / this.totalOrders;
  this.updateCustomerSegment();
};

CustomerSchema.methods.getLifetimeValue = function() {
  return this.totalSpent;
};

CustomerSchema.methods.getDaysSinceLastOrder = function() {
  if (!this.lastOrderDate) return Infinity;
  return Math.floor((Date.now() - this.lastOrderDate.getTime()) / (1000 * 60 * 60 * 24));
};
