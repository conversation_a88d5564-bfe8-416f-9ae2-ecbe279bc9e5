import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Review, ReviewDocument } from '@database/schemas/review.schema';
import { Product, ProductDocument } from '@database/schemas/product.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';
import { Customer, CustomerDocument } from '@database/schemas/customer.schema';
import { Order, OrderDocument } from '@database/schemas/order.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES } from '@common/constants';
import { PaginationResult } from '@common/interfaces';

// DTOs
import { CreateReviewDto } from './dto/create-review.dto';

@Injectable()
export class ReviewsService {
  private readonly logger = new Logger(ReviewsService.name);

  constructor(
    @InjectModel(Review.name) private reviewModel: Model<ReviewDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Customer.name) private customerModel: Model<CustomerDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    private storesService: StoresService,
  ) {}

  async findAll(
    storeId: string,
    userId: string,
    query: any,
  ): Promise<PaginationResult<ReviewDocument>> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view reviews from your own store',
        code: ERROR_CODES.UNAUTHORIZED,
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      rating,
      isApproved,
      productId,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { storeId };

    if (search) {
      filter.$or = [
        { customerName: { $regex: search, $options: 'i' } },
        { comment: { $regex: search, $options: 'i' } },
      ];
    }

    if (rating) {
      filter.rating = parseInt(rating);
    }

    if (isApproved !== undefined) {
      filter.isApproved = isApproved === 'true';
    }

    if (productId) {
      filter.productId = productId;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [reviews, total] = await Promise.all([
      this.reviewModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('productId', 'name images')
        .exec(),
      this.reviewModel.countDocuments(filter),
    ]);

    return {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(reviewId: string, storeId: string, userId: string): Promise<ReviewDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view reviews from your own store',
        code: ERROR_CODES.UNAUTHORIZED,
      });
    }

    const review = await this.reviewModel
      .findOne({ _id: reviewId, storeId })
      .populate('productId', 'name images')
      .exec();

    if (!review) {
      throw new NotFoundException({
        message: 'Review not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    return review;
  }

  async moderateReview(
    reviewId: string,
    storeId: string,
    userId: string,
    action: string,
    moderationNotes?: string,
  ): Promise<ReviewDocument> {
    const review = await this.findById(reviewId, storeId, userId);

    const updatedReview = await this.reviewModel.findByIdAndUpdate(
      reviewId,
      {
        isApproved: action === 'approve',
        moderationNotes,
        moderatedAt: new Date(),
        updatedAt: new Date(),
      },
      { new: true },
    );

    // Update product rating if approved/rejected
    if (review.productId) {
      await this.updateProductRating(review.productId.toString());
    }

    this.logger.log(`Review ${action}d: ${reviewId} in store ${storeId}`);
    return updatedReview!;
  }

  async getReviewStats(storeId: string, userId: string): Promise<any> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only view stats from your own store',
        code: ERROR_CODES.UNAUTHORIZED,
      });
    }

    const [
      totalReviews,
      approvedReviews,
      pendingReviews,
      averageRating,
      ratingDistribution,
    ] = await Promise.all([
      this.reviewModel.countDocuments({ storeId }),
      this.reviewModel.countDocuments({ storeId, isApproved: true }),
      this.reviewModel.countDocuments({ storeId, isApproved: false }),
      this.getAverageRating(storeId),
      this.getRatingDistribution(storeId),
    ]);

    return {
      totalReviews,
      approvedReviews,
      pendingReviews,
      rejectedReviews: totalReviews - approvedReviews - pendingReviews,
      averageRating,
      ratingDistribution,
    };
  }

  private async updateProductRating(productId: string): Promise<void> {
    const ratingStats = await this.reviewModel.aggregate([
      { $match: { productId: productId, isApproved: true } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: 1 },
        },
      },
    ]);

    const stats = ratingStats[0] || { averageRating: 0, totalReviews: 0 };

    await this.productModel.findByIdAndUpdate(productId, {
      averageRating: Math.round(stats.averageRating * 10) / 10,
      totalReviews: stats.totalReviews,
    });
  }

  private async getAverageRating(storeId: string): Promise<number> {
    const result = await this.reviewModel.aggregate([
      { $match: { storeId: storeId, isApproved: true } },
      { $group: { _id: null, avgRating: { $avg: '$rating' } } },
    ]);

    return result[0]?.avgRating || 0;
  }

  private async getRatingDistribution(storeId: string): Promise<any[]> {
    return this.reviewModel.aggregate([
      { $match: { storeId: storeId, isApproved: true } },
      {
        $group: {
          _id: '$rating',
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          rating: '$_id',
          count: 1,
          _id: 0,
        },
      },
      { $sort: { rating: 1 } },
    ]);
  }
}
