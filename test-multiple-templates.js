const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 10000
    });
    console.log(`✅ Response: ${response.status}`);
    return true;
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return false;
  }
}

async function testMultipleTemplates() {
  console.log('🧪 TESTING MULTIPLE TEMPLATES WITH DYNAMIC DATA\n');
  
  console.log('1️⃣ Testing ORDER STATUS templates...');
  await sendMessage('status');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('where is my order');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('order status');
  
  console.log('\n2️⃣ Testing DELIVERY templates...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('delivery');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('shipping');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('when will arrive');
  
  console.log('\n3️⃣ Testing PAYMENT templates...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('payment');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('refund');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('how much did i pay');
  
  console.log('\n4️⃣ Testing PRODUCT templates...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('products');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('iphone');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('available');
  
  console.log('\n5️⃣ Testing SUPPORT templates...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('support');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('help');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('customer service');
  
  console.log('\n6️⃣ Testing ORDER MODIFICATION templates...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('cancel');
  await new Promise(resolve => setTimeout(resolve, 2000));
  await sendMessage('modify');

  console.log('\n🎉 Multiple template testing completed!');
  console.log('📊 Check server logs to verify all templates show dynamic data');
}

testMultipleTemplates().catch(console.error);
