import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export type OrderItemDocument = OrderItem & Document;

@Schema({
  timestamps: true,
  collection: 'order_items',
})
export class OrderItem extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Order', 
    required: true,
    index: true
  })
  orderId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Product',
    index: true
  })
  productId?: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'ProductVariant',
    index: true
  })
  variantId?: Types.ObjectId;

  // Item Details (stored for historical purposes)
  @Prop({ 
    required: true, 
    trim: true,
    maxlength: 255
  })
  productName: string;

  @Prop({ 
    trim: true,
    maxlength: 255
  })
  variantName?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  sku?: string;

  // Pricing (at time of order)
  @Prop({ 
    required: true, 
    type: Number,
    min: 0,
    max: 1000000
  })
  unitPrice: number;

  @Prop({ 
    type: Number,
    min: 0,
    max: 1000000
  })
  discountedPrice?: number;

  @Prop({ 
    required: true, 
    type: Number,
    min: 1,
    max: 1000
  })
  quantity: number;

  @Prop({ 
    required: true, 
    type: Number,
    min: 0,
    max: 10000000
  })
  totalPrice: number;

  // Additional item metadata
  @Prop({ 
    type: Number,
    min: 0,
    max: 50000 // 50kg max
  })
  weight?: number; // in grams

  @Prop({ 
    type: Object,
    default: {}
  })
  productAttributes?: Record<string, any>; // Variant attributes at time of order

  @Prop({ trim: true })
  productImageUrl?: string; // Product image at time of order

  // Product details snapshot (for historical reference)
  @Prop({ 
    type: Object,
    default: {}
  })
  productSnapshot?: {
    description?: string;
    category?: string;
    tags?: string[];
  };
}

export const OrderItemSchema = SchemaFactory.createForClass(OrderItem);

// Indexes
OrderItemSchema.index({ orderId: 1 });
OrderItemSchema.index({ productId: 1 });
OrderItemSchema.index({ variantId: 1 });
OrderItemSchema.index({ createdAt: -1 });

// Compound indexes for analytics
OrderItemSchema.index({ productId: 1, createdAt: -1 });
OrderItemSchema.index({ orderId: 1, productId: 1 });

// Pre-save middleware
OrderItemSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Calculate total price if not set
  if (this.isModified(['unitPrice', 'discountedPrice', 'quantity'])) {
    const effectivePrice = this.discountedPrice || this.unitPrice;
    this.totalPrice = effectivePrice * this.quantity;
  }
  
  next();
});

// Instance methods
OrderItemSchema.methods.getEffectivePrice = function() {
  return this.discountedPrice || this.unitPrice;
};

OrderItemSchema.methods.getDiscountAmount = function() {
  if (!this.discountedPrice) return 0;
  return (this.unitPrice - this.discountedPrice) * this.quantity;
};

OrderItemSchema.methods.getDiscountPercentage = function() {
  if (!this.discountedPrice) return 0;
  return Math.round(((this.unitPrice - this.discountedPrice) / this.unitPrice) * 100);
};
