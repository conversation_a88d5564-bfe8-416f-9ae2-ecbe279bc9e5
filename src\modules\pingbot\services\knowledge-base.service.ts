import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { ConfigService } from '@nestjs/config';
import { KnowledgeBase, KnowledgeBaseDocument, KnowledgeType, KnowledgeStatus } from '../schemas/knowledge-base.schema';

export interface CreateKnowledgeBaseDto {
  storeId?: string;
  title: string;
  content: string;
  type: KnowledgeType;
  tags?: string[];
  keywords?: string[];
  category?: string;
  subcategory?: string;
  priority?: number;
  isGlobal?: boolean;
  conditions?: Record<string, any>;
  expiresAt?: Date;
}

export interface SearchKnowledgeDto {
  query: string;
  type?: KnowledgeType;
  category?: string;
  limit?: number;
  threshold?: number;
}

@Injectable()
export class KnowledgeBaseService {
  private readonly logger = new Logger(KnowledgeBaseService.name);
  private embeddings: GoogleGenerativeAIEmbeddings;

  constructor(
    @InjectModel(KnowledgeBase.name)
    private readonly knowledgeBaseModel: Model<KnowledgeBaseDocument>,
    private readonly configService: ConfigService,
  ) {
    this.initializeEmbeddings();
  }

  private initializeEmbeddings() {
    try {
      this.embeddings = new GoogleGenerativeAIEmbeddings({
        apiKey: this.configService.get('GEMINI_API_KEY'),
        modelName: 'embedding-001',
      });
      this.logger.log('Google Generative AI Embeddings initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize embeddings:', error);
    }
  }

  async create(createDto: CreateKnowledgeBaseDto, userId?: string): Promise<KnowledgeBaseDocument> {
    try {
      // Generate embedding for the content
      const embedding = await this.generateEmbedding(createDto.content + ' ' + createDto.title);

      const knowledgeBase = new this.knowledgeBaseModel({
        ...createDto,
        embedding,
        createdBy: userId,
        status: KnowledgeStatus.ACTIVE,
      });

      const saved = await knowledgeBase.save();
      this.logger.log(`Created knowledge base entry: ${saved.title}`);
      return saved;
    } catch (error) {
      this.logger.error('Error creating knowledge base entry:', error);
      throw error;
    }
  }

  async search(
    storeId: string,
    query: string,
    type?: KnowledgeType,
    options: { limit?: number; threshold?: number } = {},
  ): Promise<KnowledgeBaseDocument[]> {
    try {
      const { limit = 5, threshold = 0.7 } = options;

      // Generate embedding for the query
      const queryEmbedding = await this.generateEmbedding(query);

      // Build the search filter
      const filter: any = {
        status: KnowledgeStatus.ACTIVE,
        $or: [
          { isGlobal: true },
          { storeId },
        ],
      };

      if (type) {
        filter.type = type;
      }

      // First, try text search
      let results = await this.knowledgeBaseModel
        .find({
          ...filter,
          $text: { $search: query },
        })
        .limit(limit)
        .sort({ score: { $meta: 'textScore' }, priority: -1, usageCount: -1 })
        .exec();

      // If no text search results, fall back to embedding similarity
      if (results.length === 0 && queryEmbedding) {
        results = await this.findSimilarByEmbedding(
          queryEmbedding,
          filter,
          limit,
          threshold,
        ) as any;
      }

      // Update usage count for returned results
      await Promise.all(
        results.map(result => (result as any).incrementUsage()),
      );

      return results;
    } catch (error) {
      this.logger.error('Error searching knowledge base:', error);
      return [];
    }
  }

  async getStoreKnowledge(storeId: string): Promise<{
    policies: KnowledgeBaseDocument[];
    faqs: KnowledgeBaseDocument[];
    procedures: KnowledgeBaseDocument[];
    storeInfo: KnowledgeBaseDocument[];
  }> {
    try {
      const filter = {
        status: KnowledgeStatus.ACTIVE,
        $or: [
          { isGlobal: true },
          { storeId },
        ],
      };

      const [policies, faqs, procedures, storeInfo] = await Promise.all([
        this.knowledgeBaseModel.find({ ...filter, type: KnowledgeType.POLICY }).exec(),
        this.knowledgeBaseModel.find({ ...filter, type: KnowledgeType.FAQ }).exec(),
        this.knowledgeBaseModel.find({ ...filter, type: KnowledgeType.PROCEDURE }).exec(),
        this.knowledgeBaseModel.find({ ...filter, type: KnowledgeType.STORE_INFO }).exec(),
      ]);

      return { policies, faqs, procedures, storeInfo };
    } catch (error) {
      this.logger.error('Error getting store knowledge:', error);
      return { policies: [], faqs: [], procedures: [], storeInfo: [] };
    }
  }

  async findById(id: string): Promise<KnowledgeBaseDocument | null> {
    try {
      return await this.knowledgeBaseModel.findById(id).exec();
    } catch (error) {
      this.logger.error('Error finding knowledge base entry by ID:', error);
      return null;
    }
  }

  async update(
    id: string,
    updates: Partial<CreateKnowledgeBaseDto>,
    userId?: string,
  ): Promise<KnowledgeBaseDocument | null> {
    try {
      // If content or title is updated, regenerate embedding
      if (updates.content || updates.title) {
        const entry = await this.findById(id);
        if (entry) {
          const newContent = updates.content || entry.content;
          const newTitle = updates.title || entry.title;
          (updates as any).embedding = await this.generateEmbedding(newContent + ' ' + newTitle);
        }
      }

      const updated = await this.knowledgeBaseModel
        .findByIdAndUpdate(
          id,
          { ...updates, updatedBy: userId },
          { new: true },
        )
        .exec();

      if (updated) {
        this.logger.log(`Updated knowledge base entry: ${updated.title}`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error updating knowledge base entry:', error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.knowledgeBaseModel.findByIdAndDelete(id).exec();
      if (result) {
        this.logger.log(`Deleted knowledge base entry: ${result.title}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Error deleting knowledge base entry:', error);
      return false;
    }
  }

  async bulkCreate(entries: CreateKnowledgeBaseDto[], userId?: string): Promise<KnowledgeBaseDocument[]> {
    try {
      const entriesWithEmbeddings = await Promise.all(
        entries.map(async (entry) => ({
          ...entry,
          embedding: await this.generateEmbedding(entry.content + ' ' + entry.title),
          createdBy: userId,
          status: KnowledgeStatus.ACTIVE,
        })),
      );

      const created = await this.knowledgeBaseModel.insertMany(entriesWithEmbeddings);
      this.logger.log(`Bulk created ${created.length} knowledge base entries`);
      return created as unknown as KnowledgeBaseDocument[];
    } catch (error) {
      this.logger.error('Error bulk creating knowledge base entries:', error);
      throw error;
    }
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      if (!this.embeddings) {
        this.logger.warn('Embeddings not initialized, skipping embedding generation');
        return [];
      }

      const embedding = await this.embeddings.embedQuery(text);
      return embedding;
    } catch (error) {
      this.logger.error('Error generating embedding:', error);
      return [];
    }
  }

  private async findSimilarByEmbedding(
    queryEmbedding: number[],
    filter: any,
    limit: number,
    threshold: number,
  ): Promise<KnowledgeBaseDocument[]> {
    try {
      // This is a simplified similarity search
      // In production, you might want to use a vector database like Pinecone or Weaviate
      const allEntries = await this.knowledgeBaseModel.find(filter).exec();
      
      const similarities = allEntries
        .map(entry => ({
          entry,
          similarity: this.cosineSimilarity(queryEmbedding, entry.embedding || []),
        }))
        .filter(item => item.similarity >= threshold)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      return similarities.map(item => item.entry);
    } catch (error) {
      this.logger.error('Error finding similar entries by embedding:', error);
      return [];
    }
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length === 0 || b.length === 0 || a.length !== b.length) {
      return 0;
    }

    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));

    if (magnitudeA === 0 || magnitudeB === 0) {
      return 0;
    }

    return dotProduct / (magnitudeA * magnitudeB);
  }

  async getStats(storeId?: string): Promise<{
    total: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    mostUsed: KnowledgeBaseDocument[];
  }> {
    try {
      const filter = storeId ? { storeId } : {};

      const [total, byType, byStatus, mostUsed] = await Promise.all([
        this.knowledgeBaseModel.countDocuments(filter),
        this.knowledgeBaseModel.aggregate([
          { $match: filter },
          { $group: { _id: '$type', count: { $sum: 1 } } },
        ]),
        this.knowledgeBaseModel.aggregate([
          { $match: filter },
          { $group: { _id: '$status', count: { $sum: 1 } } },
        ]),
        this.knowledgeBaseModel
          .find(filter)
          .sort({ usageCount: -1 })
          .limit(10)
          .exec(),
      ]);

      return {
        total,
        byType: byType.reduce((acc, item) => ({ ...acc, [item._id]: item.count }), {}),
        byStatus: byStatus.reduce((acc, item) => ({ ...acc, [item._id]: item.count }), {}),
        mostUsed,
      };
    } catch (error) {
      this.logger.error('Error getting knowledge base stats:', error);
      return { total: 0, byType: {}, byStatus: {}, mostUsed: [] };
    }
  }
}
