const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

// Test the new template patterns that were added
const testMessages = [
  'support',
  'help',
  'contact',
  'hours',
  'location',
  'cancel',
  'modify'
];

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 10000
    });
    console.log(`✅ Success: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, 3000));
  } catch (error) {
    console.error(`❌ Error testing "${text}":`, error.message);
  }
}

async function testNewTemplates() {
  console.log('🧪 TESTING NEW TEMPLATE PATTERNS\n');
  console.log('============================================================\n');

  // First establish connection
  console.log('1️⃣ Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n2️⃣ Testing new template patterns...');
  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`\n${i + 3}️⃣ Testing "${message}"...`);
    await sendMessage(message);
  }

  console.log('\n🎉 All new template tests completed!');
  console.log('📊 Check server logs for detailed responses');
}

testNewTemplates().catch(console.error);
