import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>num, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum PaymentGateway {
  RAZORPAY = 'razorpay',
  STRIPE = 'stripe',
}

export class ProcessPaymentDto {
  @ApiProperty({
    description: 'Order ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  orderId: string;

  @ApiProperty({
    description: 'Payment gateway',
    enum: PaymentGateway,
    example: PaymentGateway.RAZORPAY,
  })
  @IsEnum(PaymentGateway)
  gateway: PaymentGateway;

  @ApiPropertyOptional({
    description: 'Payment method ID from gateway',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({
    description: 'Customer phone for verification',
    example: '+919876543210',
  })
  @IsOptional()
  @IsString()
  customerPhone?: string;
}

export class VerifyPaymentDto {
  @ApiProperty({
    description: 'Order ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  orderId: string;

  @ApiProperty({
    description: 'Payment ID from gateway',
    example: 'pay_1234567890',
  })
  @IsString()
  paymentId: string;

  @ApiProperty({
    description: 'Payment signature (for Razorpay)',
    example: 'signature_1234567890',
  })
  @IsString()
  signature: string;

  @ApiPropertyOptional({
    description: 'Customer phone for verification',
    example: '+919876543210',
  })
  @IsOptional()
  @IsString()
  customerPhone?: string;
}
