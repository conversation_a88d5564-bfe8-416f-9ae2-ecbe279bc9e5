import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { Conversation, ConversationDocument, ConversationStatus, ConversationChannel } from '../schemas/conversation.schema';
import { PingBotMessage, PingBotMessageDocument, MessageDirection, MessageType, MessageStatus } from '../schemas/pingbot-message.schema';

export interface CreateConversationDto {
  storeId: string;
  customerPhone: string;
  customerName?: string;
  orderId?: string;
  channel?: ConversationChannel;
  subject?: string;
  category?: string;
  context?: Record<string, any>;
}

export interface CreateMessageDto {
  conversationId: string;
  storeId: string;
  customerPhone: string;
  direction: MessageDirection;
  type?: MessageType;
  content: string;
  metadata?: Record<string, any>;
  whatsappMessageId?: string;
  intent?: string;
  entities?: Record<string, any>;
  confidence?: number;
  attachments?: string[];
  aiContext?: Record<string, any>;
}

@Injectable()
export class ConversationService {
  private readonly logger = new Logger(ConversationService.name);

  constructor(
    @InjectModel(Conversation.name)
    private readonly conversationModel: Model<ConversationDocument>,
    @InjectModel(PingBotMessage.name)
    private readonly messageModel: Model<PingBotMessageDocument>,
  ) {}

  async createConversation(createDto: CreateConversationDto): Promise<ConversationDocument> {
    try {
      const conversation = new this.conversationModel({
        ...createDto,
        status: ConversationStatus.ACTIVE,
        messageCount: 0,
        lastMessageAt: new Date(),
      });

      const saved = await conversation.save();
      this.logger.log(`Created conversation ${saved._id} for store ${saved.storeId}`);
      return saved;
    } catch (error) {
      this.logger.error('Error creating conversation:', error);
      throw error;
    }
  }

  async findOrCreateConversation(
    storeId: string,
    customerPhone: string,
    orderId?: string,
  ): Promise<ConversationDocument> {
    try {
      // Look for existing active conversation
      let conversation = await this.conversationModel
        .findOne({
          storeId,
          customerPhone,
          status: ConversationStatus.ACTIVE,
          ...(orderId && { orderId }),
        })
        .exec();

      if (!conversation) {
        // Create new conversation
        const newConversation = await this.createConversation({
          storeId,
          customerPhone,
          orderId,
          channel: ConversationChannel.WHATSAPP,
        });
        conversation = newConversation as any;
      }

      return conversation;
    } catch (error) {
      this.logger.error('Error finding or creating conversation:', error);
      throw error;
    }
  }

  async addMessage(createDto: CreateMessageDto): Promise<PingBotMessageDocument> {
    try {
      const message = new this.messageModel({
        ...createDto,
        status: MessageStatus.SENT,
        isProcessed: false,
      });

      const saved = await message.save();

      // Update conversation
      await this.conversationModel.findByIdAndUpdate(
        createDto.conversationId,
        {
          $inc: { messageCount: 1 },
          lastMessageAt: new Date(),
        },
      );

      this.logger.debug(`Added message to conversation ${createDto.conversationId}`);
      return saved;
    } catch (error) {
      this.logger.error('Error adding message:', error);
      throw error;
    }
  }

  async getConversationHistory(
    conversationId: string,
    limit: number = 50,
  ): Promise<BaseMessage[]> {
    try {
      const messages = await this.messageModel
        .find({ conversationId })
        .sort({ createdAt: 1 })
        .limit(limit)
        .exec();

      return messages.map(msg => {
        if (msg.direction === MessageDirection.INBOUND) {
          return new HumanMessage(msg.content);
        } else {
          return new AIMessage(msg.content);
        }
      });
    } catch (error) {
      this.logger.error('Error getting conversation history:', error);
      return [];
    }
  }

  async getConversationMessages(
    conversationId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    messages: PingBotMessageDocument[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      const [messages, total] = await Promise.all([
        this.messageModel
          .find({ conversationId })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.messageModel.countDocuments({ conversationId }),
      ]);

      return {
        messages: messages.reverse(), // Reverse to show chronological order
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error('Error getting conversation messages:', error);
      return { messages: [], total: 0, page: 1, totalPages: 0 };
    }
  }

  async findConversationById(id: string): Promise<ConversationDocument | null> {
    try {
      return await this.conversationModel.findById(id).exec();
    } catch (error) {
      this.logger.error('Error finding conversation by ID:', error);
      return null;
    }
  }

  async getStoreConversations(
    storeId: string,
    status?: ConversationStatus,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    conversations: ConversationDocument[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const filter: any = { storeId };
      if (status) {
        filter.status = status;
      }

      const skip = (page - 1) * limit;

      const [conversations, total] = await Promise.all([
        this.conversationModel
          .find(filter)
          .sort({ lastMessageAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.conversationModel.countDocuments(filter),
      ]);

      return {
        conversations,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error('Error getting store conversations:', error);
      return { conversations: [], total: 0, page: 1, totalPages: 0 };
    }
  }

  async updateConversationStatus(
    conversationId: string,
    status: ConversationStatus,
    assignedTo?: string,
  ): Promise<ConversationDocument | null> {
    try {
      const updates: any = { status };
      
      if (status === ConversationStatus.RESOLVED) {
        updates.resolvedAt = new Date();
      }
      
      if (assignedTo) {
        updates.assignedTo = assignedTo;
      }

      const updated = await this.conversationModel
        .findByIdAndUpdate(conversationId, updates, { new: true })
        .exec();

      if (updated) {
        this.logger.log(`Updated conversation ${conversationId} status to ${status}`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error updating conversation status:', error);
      return null;
    }
  }

  async addFeedback(
    conversationId: string,
    rating: number,
    feedback?: string,
  ): Promise<ConversationDocument | null> {
    try {
      const updated = await this.conversationModel
        .findByIdAndUpdate(
          conversationId,
          {
            satisfactionRating: rating,
            feedback,
          },
          { new: true },
        )
        .exec();

      if (updated) {
        this.logger.log(`Added feedback to conversation ${conversationId}: ${rating}/5`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error adding feedback:', error);
      return null;
    }
  }

  async getConversationStats(storeId?: string): Promise<{
    total: number;
    active: number;
    resolved: number;
    escalated: number;
    averageResolutionTime: number;
    averageRating: number;
    messageVolume: number;
  }> {
    try {
      const filter = storeId ? { storeId } : {};

      const [
        total,
        active,
        resolved,
        escalated,
        resolutionTimes,
        ratings,
        messageVolume,
      ] = await Promise.all([
        this.conversationModel.countDocuments(filter),
        this.conversationModel.countDocuments({ ...filter, status: ConversationStatus.ACTIVE }),
        this.conversationModel.countDocuments({ ...filter, status: ConversationStatus.RESOLVED }),
        this.conversationModel.countDocuments({ ...filter, status: ConversationStatus.ESCALATED }),
        this.conversationModel.aggregate([
          { $match: { ...filter, status: ConversationStatus.RESOLVED, resolvedAt: { $exists: true } } },
          {
            $project: {
              resolutionTime: {
                $subtract: ['$resolvedAt', '$createdAt'],
              },
            },
          },
          {
            $group: {
              _id: null,
              averageTime: { $avg: '$resolutionTime' },
            },
          },
        ]),
        this.conversationModel.aggregate([
          { $match: { ...filter, satisfactionRating: { $exists: true } } },
          {
            $group: {
              _id: null,
              averageRating: { $avg: '$satisfactionRating' },
            },
          },
        ]),
        this.messageModel.countDocuments(storeId ? { storeId } : {}),
      ]);

      const averageResolutionTime = resolutionTimes[0]?.averageTime || 0;
      const averageRating = ratings[0]?.averageRating || 0;

      return {
        total,
        active,
        resolved,
        escalated,
        averageResolutionTime: Math.round(averageResolutionTime / (1000 * 60 * 60)), // Convert to hours
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        messageVolume,
      };
    } catch (error) {
      this.logger.error('Error getting conversation stats:', error);
      return {
        total: 0,
        active: 0,
        resolved: 0,
        escalated: 0,
        averageResolutionTime: 0,
        averageRating: 0,
        messageVolume: 0,
      };
    }
  }

  async markMessageAsProcessed(messageId: string): Promise<void> {
    try {
      await this.messageModel.findByIdAndUpdate(messageId, {
        isProcessed: true,
        processedAt: new Date(),
      });
    } catch (error) {
      this.logger.error('Error marking message as processed:', error);
    }
  }

  async getUnprocessedMessages(limit: number = 100): Promise<PingBotMessageDocument[]> {
    try {
      return await this.messageModel
        .find({
          isProcessed: false,
          direction: MessageDirection.INBOUND,
        })
        .sort({ createdAt: 1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error('Error getting unprocessed messages:', error);
      return [];
    }
  }
}
