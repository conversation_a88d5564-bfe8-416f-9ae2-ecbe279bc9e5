import { IsString, IsOptional, IsBoolean, IsPhone<PERSON><PERSON>ber, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ConfigureWhatsAppDto {
  @ApiPropertyOptional({
    description: 'Store owner WhatsApp number (for receiving notifications)',
    example: '+************',
  })
  @IsOptional()
  @IsPhoneNumber('IN', { message: 'Please enter a valid Indian phone number' })
  storeOwnerPhone?: string;

  @ApiPropertyOptional({
    description: 'Business description for PingBot context',
    example: 'Fashion store selling trendy clothes and accessories',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Business description must not exceed 500 characters' })
  businessDescription?: string;

  @ApiPropertyOptional({
    description: 'Enable PingBot AI responses',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  pingbotEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Enable auto-respond to customer messages',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  autoRespondEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Forward unknown queries to store owner',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  forwardUnknownQueries?: boolean;

  @ApiPropertyOptional({
    description: 'Enable order notifications via WhatsApp',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  orderNotificationsEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Enable payment notifications via WhatsApp',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  paymentNotificationsEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Enable shipping notifications via WhatsApp',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  shippingNotificationsEnabled?: boolean;
}
