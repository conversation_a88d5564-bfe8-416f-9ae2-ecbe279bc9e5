import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { User, UserDocument } from '../schemas/user.schema';
import { Store, StoreDocument } from '../schemas/store.schema';
import { Category, CategoryDocument } from '../schemas/category.schema';
import { Product, ProductDocument } from '../schemas/product.schema';
import { Order, OrderDocument } from '../schemas/order.schema';
import { OrderItem, OrderItemDocument } from '../schemas/order-item.schema';
import { Customer, CustomerDocument } from '../schemas/customer.schema';
import * as bcrypt from 'bcryptjs';

const logger = new Logger('DatabaseSeeder');

async function seed() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    // Get models
    const userModel = app.get<Model<UserDocument>>(getModelToken(User.name));
    const storeModel = app.get<Model<StoreDocument>>(getModelToken(Store.name));
    const categoryModel = app.get<Model<CategoryDocument>>(getModelToken(Category.name));
    const productModel = app.get<Model<ProductDocument>>(getModelToken(Product.name));
    const orderModel = app.get<Model<OrderDocument>>(getModelToken(Order.name));
    const orderItemModel = app.get<Model<OrderItemDocument>>(getModelToken(OrderItem.name));
    const customerModel = app.get<Model<CustomerDocument>>(getModelToken(Customer.name));

    logger.log('🌱 Starting database seeding...');

    // Clear existing data
    logger.log('🧹 Clearing existing data...');
    await Promise.all([
      orderItemModel.deleteMany({}),
      orderModel.deleteMany({}),
      productModel.deleteMany({}),
      categoryModel.deleteMany({}),
      customerModel.deleteMany({}),
      storeModel.deleteMany({}),
      userModel.deleteMany({}),
    ]);

    // 1. Create Users
    logger.log('👤 Creating users...');
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const users = await userModel.insertMany([
      {
        email: '<EMAIL>',
        fullName: 'John Doe',
        phone: '+************',
        emailVerified: true,
        phoneVerified: true,
        passwordHash: hashedPassword,
      },
      {
        email: '<EMAIL>',
        fullName: 'Sarah Wilson',
        phone: '+************',
        emailVerified: true,
        phoneVerified: true,
        passwordHash: hashedPassword,
      },
      {
        email: '<EMAIL>',
        fullName: 'Mike Johnson',
        phone: '+************',
        emailVerified: true,
        phoneVerified: true,
        passwordHash: hashedPassword,
      }
    ]);

    logger.log(`✅ Created ${users.length} users`);

    // 2. Create Stores
    logger.log('🏪 Creating stores...');
    const stores = await storeModel.insertMany([
      {
        userId: users[0]._id,
        name: 'TechGadgets Pro',
        handle: 'techgadgets',
        description: 'Premium electronics and gadgets for tech enthusiasts',
        bio: 'Your one-stop shop for the latest technology',
        whatsappNumber: '+************',
        email: '<EMAIL>',
        address: '123 Tech Street, Mumbai, Maharashtra',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400001',
        country: 'India',
        isPublished: true,
        isActive: true,
        currency: 'INR',
        shippingFee: 50,
        freeShippingAbove: 1000,
        onboardingCompleted: true,
        onboardingSteps: {
          storeInfo: true,
          logo: true,
          products: true,
          whatsapp: true,
          payments: true,
          publish: true
        }
      },
      {
        userId: users[1]._id,
        name: 'Fashion Forward',
        handle: 'fashionforward',
        description: 'Trendy clothing and accessories for modern lifestyle',
        bio: 'Stay ahead in fashion with our curated collection',
        whatsappNumber: '+************',
        email: '<EMAIL>',
        address: '456 Fashion Avenue, Delhi, Delhi',
        city: 'Delhi',
        state: 'Delhi',
        pincode: '110001',
        country: 'India',
        isPublished: true,
        isActive: true,
        currency: 'INR',
        shippingFee: 75,
        freeShippingAbove: 1500,
        onboardingCompleted: true,
        onboardingSteps: {
          storeInfo: true,
          logo: true,
          products: true,
          whatsapp: true,
          payments: true,
          publish: true
        }
      },
      {
        userId: users[2]._id,
        name: 'Home & Garden',
        handle: 'homegarden',
        description: 'Beautiful home decor and garden essentials',
        bio: 'Transform your space with our premium collection',
        whatsappNumber: '+************',
        email: '<EMAIL>',
        address: '789 Garden Road, Bangalore, Karnataka',
        city: 'Bangalore',
        state: 'Karnataka',
        pincode: '560001',
        country: 'India',
        isPublished: true,
        isActive: true,
        currency: 'INR',
        shippingFee: 100,
        freeShippingAbove: 2000,
        onboardingCompleted: true,
        onboardingSteps: {
          storeInfo: true,
          logo: true,
          products: true,
          whatsapp: true,
          payments: true,
          publish: true
        }
      }
    ]);

    logger.log(`✅ Created ${stores.length} stores`);

    // Continue with categories, products, and orders...
    const { categories, products } = await seedCategoriesAndProducts(categoryModel, productModel, stores);
    await seedCustomersAndOrders(customerModel, orderModel, orderItemModel, stores, products);

    logger.log('🎉 Database seeding completed successfully!');
    
  } catch (error) {
    logger.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await app.close();
  }
}

// Helper function to seed categories and products
async function seedCategoriesAndProducts(categoryModel: any, productModel: any, stores: any[]) {
  logger.log('📂 Creating categories...');
  const categories = await categoryModel.insertMany([
    // TechGadgets categories
    {
      storeId: stores[0]._id,
      name: 'Smartphones',
      description: 'Latest smartphones and mobile devices',
      isActive: true,
      sortOrder: 1
    },
    {
      storeId: stores[0]._id,
      name: 'Laptops',
      description: 'High-performance laptops and notebooks',
      isActive: true,
      sortOrder: 2
    },
    {
      storeId: stores[0]._id,
      name: 'Accessories',
      description: 'Tech accessories and peripherals',
      isActive: true,
      sortOrder: 3
    },
    // Fashion Forward categories
    {
      storeId: stores[1]._id,
      name: 'Men\'s Clothing',
      description: 'Stylish clothing for men',
      isActive: true,
      sortOrder: 1
    },
    {
      storeId: stores[1]._id,
      name: 'Women\'s Clothing',
      description: 'Trendy clothing for women',
      isActive: true,
      sortOrder: 2
    },
    {
      storeId: stores[1]._id,
      name: 'Accessories',
      description: 'Fashion accessories and jewelry',
      isActive: true,
      sortOrder: 3
    },
    // Home & Garden categories
    {
      storeId: stores[2]._id,
      name: 'Home Decor',
      description: 'Beautiful home decoration items',
      isActive: true,
      sortOrder: 1
    },
    {
      storeId: stores[2]._id,
      name: 'Garden Tools',
      description: 'Essential tools for gardening',
      isActive: true,
      sortOrder: 2
    },
    {
      storeId: stores[2]._id,
      name: 'Furniture',
      description: 'Quality furniture for your home',
      isActive: true,
      sortOrder: 3
    }
  ]);

  logger.log(`✅ Created ${categories.length} categories`);

  // 4. Create Products
  logger.log('📱 Creating products...');
  const products = await productModel.insertMany([
    // TechGadgets products
    {
      storeId: stores[0]._id,
      categoryId: categories[0]._id, // Smartphones
      name: 'iPhone 15 Pro',
      description: 'Latest iPhone with advanced camera system and A17 Pro chip',
      shortDescription: 'Premium smartphone with cutting-edge technology',
      basePrice: 99999,
      discountedPrice: 94999,
      stockQuantity: 25,
      trackInventory: true,
      weight: 187,
      tags: ['smartphone', 'iphone', 'apple', 'premium'],
      status: 'active',
      isActive: true,
      isFeatured: true
    },
    {
      storeId: stores[0]._id,
      categoryId: categories[1]._id, // Laptops
      name: 'MacBook Pro 14"',
      description: 'Powerful laptop with M3 chip for professional work',
      shortDescription: 'High-performance laptop for professionals',
      basePrice: 199999,
      discountedPrice: 189999,
      stockQuantity: 15,
      trackInventory: true,
      weight: 1600,
      tags: ['laptop', 'macbook', 'apple', 'professional'],
      status: 'active',
      isActive: true,
      isFeatured: true
    },
    {
      storeId: stores[0]._id,
      categoryId: categories[2]._id, // Accessories
      name: 'AirPods Pro',
      description: 'Wireless earbuds with active noise cancellation',
      shortDescription: 'Premium wireless earbuds',
      basePrice: 24999,
      discountedPrice: 22999,
      stockQuantity: 50,
      trackInventory: true,
      weight: 56,
      tags: ['earbuds', 'airpods', 'apple', 'wireless'],
      status: 'active',
      isActive: true,
      isFeatured: false
    }
  ]);

  logger.log(`✅ Created ${products.length} products`);
  return { categories, products };
}

// Helper function to seed customers and orders
async function seedCustomersAndOrders(customerModel: any, orderModel: any, orderItemModel: any, stores: any[], products: any[]) {
  logger.log('👥 Creating customers...');
  const customers = await customerModel.insertMany([
    {
      storeId: stores[0]._id, // TechGadgets
      name: 'Rahul Sharma',
      phone: '+************',
      email: '<EMAIL>',
      address: '101 Customer Street',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400002',
      country: 'India',
      whatsappOptIn: true,
      emailOptIn: true,
      totalOrders: 0,
      totalSpent: 0,
      customerSegment: 'new'
    },
    {
      storeId: stores[1]._id, // Fashion Forward
      name: 'Priya Patel',
      phone: '+************',
      email: '<EMAIL>',
      address: '202 Fashion Lane',
      city: 'Delhi',
      state: 'Delhi',
      pincode: '110002',
      country: 'India',
      whatsappOptIn: true,
      emailOptIn: true,
      totalOrders: 0,
      totalSpent: 0,
      customerSegment: 'new'
    },
    {
      storeId: stores[2]._id, // Home & Garden
      name: 'Amit Kumar',
      phone: '+************',
      email: '<EMAIL>',
      address: '303 Garden View',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560002',
      country: 'India',
      whatsappOptIn: true,
      emailOptIn: true,
      totalOrders: 0,
      totalSpent: 0,
      customerSegment: 'new'
    }
  ]);

  logger.log(`✅ Created ${customers.length} customers`);

  // 6. Create Orders with proper order numbers
  logger.log('📦 Creating orders...');
  const orders = await orderModel.insertMany([
    {
      storeId: stores[0]._id,
      orderNumber: 'TG-2024-001',
      customerName: 'Rahul Sharma',
      customerPhone: '+************',
      customerEmail: '<EMAIL>',
      shippingAddress: '101 Customer Street',
      shippingCity: 'Mumbai',
      shippingState: 'Maharashtra',
      shippingPincode: '400002',
      shippingCountry: 'India',
      status: 'confirmed',
      paymentStatus: 'paid',
      paymentMethod: 'upi',
      subtotal: 94999,
      shippingFee: 0, // Free shipping
      totalAmount: 94999,
      confirmedAt: new Date(),
      sessionId: 'sess_' + Date.now() + '_1'
    },
    {
      storeId: stores[0]._id,
      orderNumber: 'TG-2024-002',
      customerName: 'Neha Singh',
      customerPhone: '+************',
      customerEmail: '<EMAIL>',
      shippingAddress: '404 Tech Plaza',
      shippingCity: 'Mumbai',
      shippingState: 'Maharashtra',
      shippingPincode: '400003',
      shippingCountry: 'India',
      status: 'processing',
      paymentStatus: 'paid',
      paymentMethod: 'card',
      subtotal: 22999,
      shippingFee: 50,
      totalAmount: 23049,
      confirmedAt: new Date(),
      sessionId: 'sess_' + Date.now() + '_2'
    },
    {
      storeId: stores[1]._id,
      orderNumber: 'FF-2024-001',
      customerName: 'Priya Patel',
      customerPhone: '+************',
      customerEmail: '<EMAIL>',
      shippingAddress: '202 Fashion Lane',
      shippingCity: 'Delhi',
      shippingState: 'Delhi',
      shippingPincode: '110002',
      shippingCountry: 'India',
      status: 'shipped',
      paymentStatus: 'paid',
      paymentMethod: 'upi',
      subtotal: 2999,
      shippingFee: 75,
      totalAmount: 3074,
      confirmedAt: new Date(),
      trackingNumber: 'TRK123456789',
      courierPartner: 'BlueDart',
      sessionId: 'sess_' + Date.now() + '_3'
    }
  ]);

  logger.log(`✅ Created ${orders.length} orders`);

  // 7. Create Order Items
  logger.log('📋 Creating order items...');
  const orderItems = await orderItemModel.insertMany([
    // Order 1: iPhone 15 Pro
    {
      orderId: orders[0]._id,
      productId: products[0]._id, // iPhone 15 Pro
      productName: 'iPhone 15 Pro',
      unitPrice: 99999,
      discountedPrice: 94999,
      quantity: 1,
      totalPrice: 94999,
      weight: 187,
      productSnapshot: {
        description: 'Latest iPhone with advanced camera system and A17 Pro chip',
        category: 'Smartphones',
        tags: ['smartphone', 'iphone', 'apple', 'premium']
      }
    },
    // Order 2: AirPods Pro
    {
      orderId: orders[1]._id,
      productId: products[2]._id, // AirPods Pro
      productName: 'AirPods Pro',
      unitPrice: 24999,
      discountedPrice: 22999,
      quantity: 1,
      totalPrice: 22999,
      weight: 56,
      productSnapshot: {
        description: 'Wireless earbuds with active noise cancellation',
        category: 'Accessories',
        tags: ['earbuds', 'airpods', 'apple', 'wireless']
      }
    },
    // Order 3: Fashion item (placeholder - would need fashion products)
    {
      orderId: orders[2]._id,
      productId: products[0]._id, // Using tech product as placeholder
      productName: 'Fashion Item',
      unitPrice: 2999,
      discountedPrice: 2999,
      quantity: 1,
      totalPrice: 2999,
      weight: 200,
      productSnapshot: {
        description: 'Trendy fashion item',
        category: 'Fashion',
        tags: ['fashion', 'clothing']
      }
    }
  ]);

  logger.log(`✅ Created ${orderItems.length} order items`);
  return { customers, orders, orderItems };
}

// Run the seeder
if (require.main === module) {
  seed()
    .then(() => {
      logger.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seed };
