import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { Model } from 'mongoose';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';

import { Order, OrderDocument } from '@database/schemas/order.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';

import { ERROR_CODES, PAYMENT_STATUS, PAYMENT_METHODS } from '@common/constants';

// DTOs
import { ProcessPaymentDto, VerifyPaymentDto, PaymentGateway } from './dto/process-payment.dto';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);
  private readonly razorpayKeyId: string;
  private readonly razorpayKeySecret: string;
  private readonly stripeSecretKey: string;

  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.razorpayKeyId = this.configService.get<string>('payments.razorpay.keyId');
    this.razorpayKeySecret = this.configService.get<string>('payments.razorpay.keySecret');
    this.stripeSecretKey = this.configService.get<string>('payments.stripe.secretKey');
  }

  async createPaymentIntent(processPaymentDto: ProcessPaymentDto): Promise<any> {
    const { orderId, gateway, customerPhone } = processPaymentDto;

    // Get order
    const order = await this.orderModel
      .findById(orderId)
      .populate('storeId')
      .exec();

    if (!order) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    // Verify customer phone if provided
    if (customerPhone && order.customerPhone !== customerPhone) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    // Check if order is payable
    if (order.paymentStatus === PAYMENT_STATUS.COMPLETED) {
      throw new BadRequestException({
        message: 'Order is already paid',
        code: ERROR_CODES.INVALID_OPERATION,
      });
    }

    if (order.paymentMethod === PAYMENT_METHODS.COD) {
      throw new BadRequestException({
        message: 'This order is Cash on Delivery',
        code: ERROR_CODES.INVALID_OPERATION,
      });
    }

    // Create payment intent based on gateway
    let paymentIntent;
    switch (gateway) {
      case PaymentGateway.RAZORPAY:
        paymentIntent = await this.createRazorpayOrder(order);
        break;
      case PaymentGateway.STRIPE:
        paymentIntent = await this.createStripePaymentIntent(order);
        break;
      default:
        throw new BadRequestException({
          message: 'Unsupported payment gateway',
          code: ERROR_CODES.VALIDATION_ERROR,
        });
    }

    // Update order with payment intent
    await this.orderModel.findByIdAndUpdate(orderId, {
      paymentId: paymentIntent.id,
      paymentStatus: PAYMENT_STATUS.PROCESSING,
    });

    this.logger.log(`Payment intent created for order: ${order.orderNumber}`);
    return paymentIntent;
  }

  async verifyPayment(verifyPaymentDto: VerifyPaymentDto): Promise<any> {
    const { orderId, paymentId, signature, customerPhone } = verifyPaymentDto;

    // Get order
    const order = await this.orderModel
      .findById(orderId)
      .populate('storeId')
      .exec();

    if (!order) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    // Verify customer phone if provided
    if (customerPhone && order.customerPhone !== customerPhone) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    // Verify payment based on gateway
    let isValid = false;
    if (order.paymentMethod === PAYMENT_METHODS.UPI || order.paymentMethod === PAYMENT_METHODS.CARD) {
      // Assume Razorpay for UPI/Card
      isValid = await this.verifyRazorpayPayment(order.paymentId, paymentId, signature);
    }

    if (!isValid) {
      throw new BadRequestException({
        message: 'Payment verification failed',
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Update order status
    const updatedOrder = await this.orderModel.findByIdAndUpdate(
      orderId,
      {
        paymentStatus: PAYMENT_STATUS.COMPLETED,
        paymentId,
        updatedAt: new Date(),
      },
      { new: true },
    );

    this.logger.log(`Payment verified for order: ${order.orderNumber}`);
    return {
      success: true,
      order: updatedOrder,
    };
  }

  async handleWebhook(gateway: string, payload: any, signature: string): Promise<void> {
    switch (gateway) {
      case 'razorpay':
        await this.handleRazorpayWebhook(payload, signature);
        break;
      case 'stripe':
        await this.handleStripeWebhook(payload, signature);
        break;
      default:
        this.logger.warn(`Unknown payment gateway webhook: ${gateway}`);
    }
  }

  async getPaymentStatus(orderId: string, customerPhone?: string): Promise<any> {
    const order = await this.orderModel
      .findById(orderId)
      .populate('storeId', 'name handle')
      .exec();

    if (!order) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    // Verify customer phone if provided
    if (customerPhone && order.customerPhone !== customerPhone) {
      throw new NotFoundException({
        message: 'Order not found',
        code: ERROR_CODES.ORDER_NOT_FOUND,
      });
    }

    const store = order.storeId as any;
    return {
      orderId: order._id,
      orderNumber: order.orderNumber,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      totalAmount: order.totalAmount,
      paymentId: order.paymentId,
      store: {
        name: store.name,
        handle: store.handle,
      },
    };
  }

  // Private helper methods
  private async createRazorpayOrder(order: OrderDocument): Promise<any> {
    if (!this.razorpayKeyId || !this.razorpayKeySecret) {
      throw new BadRequestException({
        message: 'Razorpay not configured',
        code: ERROR_CODES.SERVICE_UNAVAILABLE,
      });
    }

    try {
      const orderData = {
        amount: Math.round(order.totalAmount * 100), // Convert to paise
        currency: 'INR',
        receipt: order.orderNumber,
        notes: {
          orderId: order._id.toString(),
          storeId: order.storeId.toString(),
          customerPhone: order.customerPhone,
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(
          'https://api.razorpay.com/v1/orders',
          orderData,
          {
            auth: {
              username: this.razorpayKeyId,
              password: this.razorpayKeySecret,
            },
          },
        ),
      );

      return {
        id: response.data.id,
        amount: response.data.amount,
        currency: response.data.currency,
        gateway: 'razorpay',
        keyId: this.razorpayKeyId,
      };
    } catch (error) {
      this.logger.error(`Razorpay order creation failed: ${error.message}`);
      throw new BadRequestException({
        message: 'Payment gateway error',
        code: ERROR_CODES.SERVICE_UNAVAILABLE,
      });
    }
  }

  private async createStripePaymentIntent(order: OrderDocument): Promise<any> {
    if (!this.stripeSecretKey) {
      throw new BadRequestException({
        message: 'Stripe not configured',
        code: ERROR_CODES.SERVICE_UNAVAILABLE,
      });
    }

    try {
      const paymentIntentData = {
        amount: Math.round(order.totalAmount * 100), // Convert to paise
        currency: 'inr',
        metadata: {
          orderId: order._id.toString(),
          storeId: order.storeId.toString(),
          orderNumber: order.orderNumber,
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(
          'https://api.stripe.com/v1/payment_intents',
          new URLSearchParams(paymentIntentData as any),
          {
            headers: {
              'Authorization': `Bearer ${this.stripeSecretKey}`,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        ),
      );

      return {
        id: response.data.id,
        clientSecret: response.data.client_secret,
        amount: response.data.amount,
        currency: response.data.currency,
        gateway: 'stripe',
      };
    } catch (error) {
      this.logger.error(`Stripe payment intent creation failed: ${error.message}`);
      throw new BadRequestException({
        message: 'Payment gateway error',
        code: ERROR_CODES.SERVICE_UNAVAILABLE,
      });
    }
  }

  private async verifyRazorpayPayment(
    orderId: string,
    paymentId: string,
    signature: string,
  ): Promise<boolean> {
    try {
      const body = orderId + '|' + paymentId;
      const expectedSignature = crypto
        .createHmac('sha256', this.razorpayKeySecret)
        .update(body.toString())
        .digest('hex');

      return expectedSignature === signature;
    } catch (error) {
      this.logger.error(`Razorpay verification failed: ${error.message}`);
      return false;
    }
  }

  private async handleRazorpayWebhook(payload: any, signature: string): Promise<void> {
    // Verify webhook signature
    const webhookSecret = this.configService.get<string>('payments.razorpay.webhookSecret');
    if (webhookSecret) {
      const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(JSON.stringify(payload))
        .digest('hex');

      if (expectedSignature !== signature) {
        this.logger.warn('Invalid Razorpay webhook signature');
        return;
      }
    }

    // Handle different webhook events
    const { event, payload: eventPayload } = payload;

    switch (event) {
      case 'payment.captured':
        await this.handlePaymentCaptured(eventPayload.payment.entity);
        break;
      case 'payment.failed':
        await this.handlePaymentFailed(eventPayload.payment.entity);
        break;
      default:
        this.logger.debug(`Unhandled Razorpay webhook event: ${event}`);
    }
  }

  private async handleStripeWebhook(payload: any, signature: string): Promise<void> {
    // Implement Stripe webhook handling
    this.logger.debug('Stripe webhook received');
  }

  private async handlePaymentCaptured(payment: any): Promise<void> {
    const orderId = payment.notes?.orderId;
    if (!orderId) {
      this.logger.warn('Payment captured webhook missing orderId');
      return;
    }

    await this.orderModel.findByIdAndUpdate(orderId, {
      paymentStatus: PAYMENT_STATUS.COMPLETED,
      paymentId: payment.id,
      updatedAt: new Date(),
    });

    this.logger.log(`Payment captured for order: ${orderId}`);
  }

  private async handlePaymentFailed(payment: any): Promise<void> {
    const orderId = payment.notes?.orderId;
    if (!orderId) {
      this.logger.warn('Payment failed webhook missing orderId');
      return;
    }

    await this.orderModel.findByIdAndUpdate(orderId, {
      paymentStatus: PAYMENT_STATUS.FAILED,
      paymentId: payment.id,
      updatedAt: new Date(),
    });

    this.logger.log(`Payment failed for order: ${orderId}`);
  }
}
