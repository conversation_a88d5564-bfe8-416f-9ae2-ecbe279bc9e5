import {
  <PERSON><PERSON><PERSON>,
  IsO<PERSON>al,
  Is<PERSON><PERSON>ber,
  IsBoolean,
  IsArray,
  IsUrl,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  IsMongoId,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateProductDto {
  @ApiProperty({
    description: 'Product name',
    example: 'Awesome T-Shirt',
    minLength: 2,
    maxLength: 200,
  })
  @IsString()
  @MinLength(2, { message: 'Product name must be at least 2 characters long' })
  @MaxLength(200, { message: 'Product name must not exceed 200 characters' })
  name: string;

  @ApiPropertyOptional({
    description: 'Product description',
    example: 'A comfortable and stylish t-shirt made from premium cotton',
    maxLength: 2000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(2000, { message: 'Description must not exceed 2000 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Short product description',
    example: 'Premium cotton t-shirt',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Short description must not exceed 500 characters' })
  shortDescription?: string;

  @ApiProperty({
    description: 'Base price of the product',
    example: 999,
    minimum: 0,
    maximum: 1000000,
  })
  @IsNumber()
  @Type(() => Number)
  @Min(0, { message: 'Base price cannot be negative' })
  @Max(1000000, { message: 'Base price cannot exceed 1000000' })
  basePrice: number;

  @ApiPropertyOptional({
    description: 'Discounted price of the product',
    example: 799,
    minimum: 0,
    maximum: 1000000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0, { message: 'Discounted price cannot be negative' })
  @Max(1000000, { message: 'Discounted price cannot exceed 1000000' })
  discountedPrice?: number;

  @ApiPropertyOptional({
    description: 'Cost price of the product (for profit calculation)',
    example: 500,
    minimum: 0,
    maximum: 1000000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0, { message: 'Cost price cannot be negative' })
  @Max(1000000, { message: 'Cost price cannot exceed 1000000' })
  costPrice?: number;

  @ApiPropertyOptional({
    description: 'Category ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsOptional()
  @IsMongoId({ message: 'Please provide a valid category ID' })
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Product SKU',
    example: 'TSH-001',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'SKU must not exceed 100 characters' })
  sku?: string;

  @ApiPropertyOptional({
    description: 'Stock quantity',
    example: 50,
    minimum: 0,
    maximum: 100000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0, { message: 'Stock quantity cannot be negative' })
  @Max(100000, { message: 'Stock quantity cannot exceed 100000' })
  stockQuantity?: number;

  @ApiPropertyOptional({
    description: 'Track inventory',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  trackInventory?: boolean;

  @ApiPropertyOptional({
    description: 'Allow backorder',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  allowBackorder?: boolean;

  @ApiPropertyOptional({
    description: 'Product weight in grams',
    example: 200,
    minimum: 0,
    maximum: 50000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0, { message: 'Weight cannot be negative' })
  @Max(50000, { message: 'Weight cannot exceed 50000 grams' })
  weight?: number;

  @ApiPropertyOptional({
    description: 'Product dimensions',
    example: { length: 10, width: 5, height: 3 },
  })
  @IsOptional()
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };

  @ApiPropertyOptional({
    description: 'Product tags',
    example: ['clothing', 'cotton', 'casual'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Is featured product',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiPropertyOptional({
    description: 'Meta title for SEO',
    example: 'Premium Cotton T-Shirt - Comfortable & Stylish',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255, { message: 'Meta title must not exceed 255 characters' })
  metaTitle?: string;

  @ApiPropertyOptional({
    description: 'Meta description for SEO',
    example: 'Shop our premium cotton t-shirt collection. Comfortable, stylish, and affordable.',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Meta description must not exceed 500 characters' })
  metaDescription?: string;

  @ApiPropertyOptional({
    description: 'External link (for affiliate products)',
    example: 'https://affiliate-site.com/product/123',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please enter a valid external link' })
  externalLink?: string;

  @ApiPropertyOptional({
    description: 'Instagram post URL',
    example: 'https://instagram.com/p/ABC123',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please enter a valid Instagram URL' })
  instagramPostUrl?: string;
}
