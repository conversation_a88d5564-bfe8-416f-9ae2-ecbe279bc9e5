// API Response Messages
export const API_MESSAGES = {
  // Authentication
  AUTH_SUCCESS: 'Authentication successful',
  AUTH_FAILED: 'Authentication failed',
  LOGOUT_SUCCESS: 'Logged out successfully',
  TOKEN_REFRESH_SUCCESS: 'Token refreshed successfully',
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'OTP verified successfully',
  
  // User Management
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
  
  // Store Management
  STORE_CREATED: 'Store created successfully',
  STORE_UPDATED: 'Store updated successfully',
  STORE_DELETED: 'Store deleted successfully',
  STORE_PUBLISHED: 'Store published successfully',
  ONBOARDING_COMPLETED: 'Onboarding completed successfully',
  
  // Product Management
  PRODUCT_CREATED: 'Product created successfully',
  PRODUCT_UPDATED: 'Product updated successfully',
  PRODUCT_DELETED: 'Product deleted successfully',
  PRODUCTS_BULK_UPDATED: 'Products updated successfully',
  
  // Order Management
  ORDER_CREATED: 'Order created successfully',
  ORDER_UPDATED: 'Order updated successfully',
  ORDER_CANCELLED: 'Order cancelled successfully',
  
  // Payment
  PAYMENT_SUCCESS: 'Payment processed successfully',
  PAYMENT_FAILED: 'Payment processing failed',
  REFUND_PROCESSED: 'Refund processed successfully',
  
  // File Upload
  FILE_UPLOADED: 'File uploaded successfully',
  FILES_UPLOADED: 'Files uploaded successfully',
  FILE_DELETED: 'File deleted successfully',
  
  // WhatsApp
  WHATSAPP_CONNECTED: 'WhatsApp connected successfully',
  WHATSAPP_VERIFIED: 'WhatsApp number verified successfully',
  WHATSAPP_DISCONNECTED: 'WhatsApp disconnected successfully',
  
  // Reviews
  REVIEW_SUBMITTED: 'Review submitted successfully',
  REVIEW_APPROVED: 'Review approved successfully',
  REVIEW_REJECTED: 'Review rejected successfully',
  
  // General
  SUCCESS: 'Operation completed successfully',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation failed',
  INTERNAL_ERROR: 'Internal server error',
};

// Error Codes
export const ERROR_CODES = {
  // Authentication
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  AUTH_EXPIRED: 'AUTH_EXPIRED',
  OTP_INVALID: 'OTP_INVALID',
  OTP_EXPIRED: 'OTP_EXPIRED',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD: 'REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  DUPLICATE_VALUE: 'DUPLICATE_VALUE',
  
  // Business Logic
  STORE_NOT_FOUND: 'STORE_NOT_FOUND',
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  ORDER_NOT_FOUND: 'ORDER_NOT_FOUND',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  NOT_FOUND: 'NOT_FOUND',
  INSUFFICIENT_STOCK: 'INSUFFICIENT_STOCK',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  WHATSAPP_NOT_CONNECTED: 'WHATSAPP_NOT_CONNECTED',
  INVALID_OPERATION: 'INVALID_OPERATION',

  // Permissions
  FORBIDDEN: 'FORBIDDEN',
  UNAUTHORIZED: 'UNAUTHORIZED',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // System
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
};

// Order Status
export const ORDER_STATUS = {
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const;

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  PAID: 'paid',
  COMPLETED: 'completed',
  FAILED: 'failed',
  REFUNDED: 'refunded',
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  UPI: 'upi',
  CARD: 'card',
  COD: 'cod',
  BANK_TRANSFER: 'bank_transfer',
} as const;

// Product Status
export const PRODUCT_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const;

// User Roles
export const USER_ROLES = {
  STORE_OWNER: 'store_owner',
  ADMIN: 'admin',
} as const;

// File Types
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// Cache TTL (in seconds)
export const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400, // 24 hours
};

// Analytics Events
export const ANALYTICS_EVENTS = {
  STORE_VIEW: 'store_view',
  PRODUCT_VIEW: 'product_view',
  ADD_TO_CART: 'add_to_cart',
  PURCHASE: 'purchase',
  PRODUCT_CLICK: 'product_click',
} as const;

// Customer Segments
export const CUSTOMER_SEGMENTS = {
  NEW: 'new',
  REGULAR: 'regular',
  VIP: 'vip',
  INACTIVE: 'inactive',
} as const;

// Themes
export const AVAILABLE_THEMES = {
  MODERN_MINIMAL: 'modern-minimal',
  INSTAGRAM_SHOP: 'instagram-shop',
  LUXURY_BOUTIQUE: 'luxury-boutique',
  VIBRANT_MODERN: 'vibrant-modern',
} as const;
