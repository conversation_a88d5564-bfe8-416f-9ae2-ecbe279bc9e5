import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HttpModule } from '@nestjs/axios';

import { WhatsappController } from './whatsapp.controller';
import { WhatsappService } from './whatsapp.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';
import { OrdersModule } from '@modules/orders/orders.module';

// Schemas
import { WhatsAppIntegration, WhatsAppIntegrationSchema } from '@database/schemas/whatsapp-integration.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';
import { Order, OrderSchema } from '@database/schemas/order.schema';
import { Customer, CustomerSchema } from '@database/schemas/customer.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: WhatsAppIntegration.name, schema: WhatsAppIntegrationSchema },
      { name: Store.name, schema: StoreSchema },
      { name: Order.name, schema: OrderSchema },
      { name: Customer.name, schema: CustomerSchema },
    ]),
    HttpModule,
    StoresModule,
    OrdersModule,
  ],
  controllers: [WhatsappController],
  providers: [WhatsappService],
  exports: [WhatsappService],
})
export class WhatsappModule {}
