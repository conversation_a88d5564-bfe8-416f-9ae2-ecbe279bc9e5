import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, ValidateIf } from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiPropertyOptional({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @ValidateIf((o) => !o.phone)
  @IsEmail({}, { message: 'Please enter a valid email address' })
  email?: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+919876543210',
  })
  @IsOptional()
  @ValidateIf((o) => !o.email)
  @IsString()
  @Matches(/^\+?[1-9]\d{1,14}$/, {
    message: 'Please enter a valid phone number',
  })
  phone?: string;

  @ApiProperty({
    description: 'User password',
    example: 'SecurePassword123!',
  })
  @IsString()
  @MinLength(1, { message: 'Password is required' })
  password: string;
}
