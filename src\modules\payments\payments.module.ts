import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HttpModule } from '@nestjs/axios';

import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.service';

// Import other modules
import { OrdersModule } from '@modules/orders/orders.module';
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { Order, OrderSchema } from '@database/schemas/order.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Order.name, schema: OrderSchema },
      { name: Store.name, schema: StoreSchema },
    ]),
    HttpModule,
    OrdersModule,
    StoresModule,
  ],
  controllers: [PaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}
