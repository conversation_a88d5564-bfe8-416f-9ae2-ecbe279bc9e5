import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ConversationDocument = Conversation & Document;

export enum ConversationStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  ESCALATED = 'escalated',
  CLOSED = 'closed',
}

export enum ConversationChannel {
  WHATSAPP = 'whatsapp',
  WEB = 'web',
  API = 'api',
}

@Schema({
  collection: 'pingbot_conversations',
  timestamps: true,
})
export class Conversation {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true 
  })
  storeId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Customer',
    index: true 
  })
  customerId?: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Order',
    index: true 
  })
  orderId?: Types.ObjectId;

  @Prop({ 
    required: true,
    trim: true,
    index: true 
  })
  customerPhone: string;

  @Prop({ 
    trim: true 
  })
  customerName?: string;

  @Prop({ 
    type: String,
    enum: ConversationChannel,
    default: ConversationChannel.WHATSAPP,
    index: true 
  })
  channel: ConversationChannel;

  @Prop({ 
    type: String,
    enum: ConversationStatus,
    default: ConversationStatus.ACTIVE,
    index: true 
  })
  status: ConversationStatus;

  @Prop({ 
    trim: true 
  })
  subject?: string;

  @Prop({ 
    trim: true 
  })
  category?: string;

  @Prop({ 
    type: Object,
    default: {} 
  })
  context: Record<string, any>;

  @Prop({ 
    type: Object,
    default: {} 
  })
  metadata: Record<string, any>;

  @Prop({ 
    default: 0 
  })
  messageCount: number;

  @Prop({ 
    type: Date 
  })
  lastMessageAt?: Date;

  @Prop({ 
    type: Date 
  })
  resolvedAt?: Date;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'User' 
  })
  assignedTo?: Types.ObjectId;

  @Prop({ 
    type: [String],
    default: [] 
  })
  tags: string[];

  @Prop({ 
    min: 1,
    max: 5 
  })
  satisfactionRating?: number;

  @Prop({ 
    trim: true 
  })
  feedback?: string;
}

export const ConversationSchema = SchemaFactory.createForClass(Conversation);

// Indexes for performance
ConversationSchema.index({ storeId: 1, status: 1 });
ConversationSchema.index({ customerPhone: 1, storeId: 1 });
ConversationSchema.index({ orderId: 1 });
ConversationSchema.index({ createdAt: -1 });
ConversationSchema.index({ lastMessageAt: -1 });

// Methods
ConversationSchema.methods.markAsResolved = function() {
  this.status = ConversationStatus.RESOLVED;
  this.resolvedAt = new Date();
  return this.save();
};

ConversationSchema.methods.escalate = function(assignedTo?: string) {
  this.status = ConversationStatus.ESCALATED;
  if (assignedTo) {
    this.assignedTo = new Types.ObjectId(assignedTo);
  }
  return this.save();
};

ConversationSchema.methods.updateLastMessage = function() {
  this.lastMessageAt = new Date();
  this.messageCount += 1;
  return this.save();
};
