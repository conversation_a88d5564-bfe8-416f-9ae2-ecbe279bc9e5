import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument } from '@database/schemas/user.schema';
import { UsersService } from '@modules/users/users.service';
import { JwtPayload, AuthTokens, GoogleProfile } from '@common/interfaces';
import { ERROR_CODES, API_MESSAGES } from '@common/constants';

// DTOs
import { SignupDto } from './dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { GoogleAuthDto } from './dto/google-auth.dto';
import { SendOtpDto } from './dto/send-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private usersService: UsersService,
  ) {}

  async signup(signupDto: SignupDto): Promise<{ user: UserDocument; tokens: AuthTokens }> {
    const { email, phone, fullName, password } = signupDto;

    // Check if user already exists
    const existingUser = await this.userModel.findOne({
      $or: [{ email }, ...(phone ? [{ phone }] : [])],
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException({
          message: 'Email already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
      if (existingUser.phone === phone) {
        throw new ConflictException({
          message: 'Phone number already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    // Hash password
    const saltRounds = this.configService.get<number>('auth.bcrypt.saltRounds');
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = new this.userModel({
      email,
      phone,
      fullName,
      passwordHash,
      emailVerified: false,
      phoneVerified: false,
    });

    await user.save();

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    this.logger.log(`User signed up: ${email}`);

    return { user, tokens };
  }

  async login(loginDto: LoginDto): Promise<{ user: UserDocument; tokens: AuthTokens }> {
    const { email, phone, password } = loginDto;

    // Find user by email or phone
    const user = await this.userModel
      .findOne({
        $or: [
          ...(email ? [{ email }] : []),
          ...(phone ? [{ phone }] : []),
        ],
      })
      .select('+passwordHash');

    if (!user) {
      throw new UnauthorizedException({
        message: 'Invalid credentials',
        code: ERROR_CODES.AUTH_INVALID,
      });
    }

    // Check password
    if (!user.passwordHash) {
      throw new UnauthorizedException({
        message: 'Please use Google login or reset your password',
        code: ERROR_CODES.AUTH_INVALID,
      });
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new UnauthorizedException({
        message: 'Invalid credentials',
        code: ERROR_CODES.AUTH_INVALID,
      });
    }

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    this.logger.log(`User logged in: ${user.email}`);

    return { user, tokens };
  }

  async googleAuth(googleAuthDto: GoogleAuthDto): Promise<{ user: UserDocument; tokens: AuthTokens }> {
    const { googleToken } = googleAuthDto;

    // Verify Google token and get profile
    const profile = await this.verifyGoogleToken(googleToken);

    // Find or create user
    let user = await this.userModel.findOne({
      $or: [{ googleId: profile.id }, { email: profile.email }],
    });

    if (user) {
      // Update Google ID if not set
      if (!user.googleId) {
        user.googleId = profile.id;
        user.emailVerified = true;
        await user.save();
      }
    } else {
      // Create new user
      user = new this.userModel({
        email: profile.email,
        fullName: profile.name,
        googleId: profile.id,
        avatarUrl: profile.picture,
        emailVerified: true,
      });
      await user.save();
    }

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    this.logger.log(`User authenticated with Google: ${user.email}`);

    return { user, tokens };
  }

  async sendOtp(sendOtpDto: SendOtpDto): Promise<{ message: string }> {
    const { phone } = sendOtpDto;

    // Generate OTP
    const otp = this.generateOtp();

    // Store OTP in cache/database (implement based on your preference)
    await this.storeOtp(phone, otp);

    // Send OTP via SMS (implement with Twilio or other SMS service)
    await this.sendSms(phone, `Your PingStore verification code is: ${otp}`);

    this.logger.log(`OTP sent to: ${phone}`);

    return { message: API_MESSAGES.OTP_SENT };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<{ user: UserDocument; tokens: AuthTokens }> {
    const { phone, otp } = verifyOtpDto;

    // Verify OTP
    const isValidOtp = await this.verifyStoredOtp(phone, otp);
    if (!isValidOtp) {
      throw new UnauthorizedException({
        message: 'Invalid or expired OTP',
        code: ERROR_CODES.OTP_INVALID,
      });
    }

    // Find or create user
    let user = await this.userModel.findOne({ phone });

    if (!user) {
      // Create new user with phone
      user = new this.userModel({
        phone,
        fullName: `User ${phone}`, // Temporary name
        phoneVerified: true,
      });
      await user.save();
    } else {
      // Update phone verification
      user.phoneVerified = true;
      await user.save();
    }

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Clear OTP
    await this.clearOtp(phone);

    this.logger.log(`User verified OTP: ${phone}`);

    return { user, tokens };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthTokens> {
    const { refreshToken } = refreshTokenDto;

    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.refreshToken.secret'),
      });

      const user = await this.userModel.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedException({
          message: 'User not found',
          code: ERROR_CODES.AUTH_INVALID,
        });
      }

      return this.generateTokens(user);
    } catch (error) {
      throw new UnauthorizedException({
        message: 'Invalid refresh token',
        code: ERROR_CODES.AUTH_EXPIRED,
      });
    }
  }

  async validateUser(payload: JwtPayload): Promise<UserDocument> {
    const user = await this.userModel.findById(payload.sub);
    if (!user) {
      throw new UnauthorizedException({
        message: 'User not found',
        code: ERROR_CODES.AUTH_INVALID,
      });
    }
    return user;
  }

  private async generateTokens(user: UserDocument): Promise<AuthTokens> {
    const payload: JwtPayload = {
      sub: user._id.toString(),
      email: user.email,
      role: 'store_owner', // Default role
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('auth.jwt.secret'),
        expiresIn: this.configService.get<string>('auth.jwt.expiresIn'),
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('auth.refreshToken.secret'),
        expiresIn: this.configService.get<string>('auth.refreshToken.expiresIn'),
      }),
    ]);

    return { accessToken, refreshToken };
  }

  private async verifyGoogleToken(token: string): Promise<GoogleProfile> {
    // Implement Google token verification
    // This is a placeholder - implement with Google APIs
    try {
      // Use Google Auth Library to verify token
      // const ticket = await client.verifyIdToken({
      //   idToken: token,
      //   audience: this.configService.get<string>('auth.google.clientId'),
      // });
      // const payload = ticket.getPayload();
      
      // For now, return mock data
      return {
        id: 'google_id',
        email: '<EMAIL>',
        name: 'Google User',
        picture: 'https://example.com/avatar.jpg',
      };
    } catch (error) {
      throw new UnauthorizedException({
        message: 'Invalid Google token',
        code: ERROR_CODES.AUTH_INVALID,
      });
    }
  }

  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private async storeOtp(phone: string, otp: string): Promise<void> {
    // Implement OTP storage (Redis, database, etc.)
    // For now, this is a placeholder
    this.logger.debug(`Storing OTP for ${phone}: ${otp}`);
  }

  private async verifyStoredOtp(phone: string, otp: string): Promise<boolean> {
    // Implement OTP verification
    // For now, return true for demo purposes
    return otp === '123456'; // Demo OTP
  }

  private async clearOtp(phone: string): Promise<void> {
    // Implement OTP clearing
    this.logger.debug(`Clearing OTP for ${phone}`);
  }

  private async sendSms(phone: string, message: string): Promise<void> {
    // Implement SMS sending with Twilio or other service
    this.logger.debug(`Sending SMS to ${phone}: ${message}`);
  }
}
