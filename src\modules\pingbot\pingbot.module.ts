import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PingBotService } from './pingbot.service';
import { PingBotController } from './pingbot.controller';
import { WhatsAppWebhookController } from './controllers/whatsapp-webhook.controller';
import { TelegramWebhookController } from './controllers/telegram-webhook.controller';
import { TwilioWebhookController } from './controllers/twilio-webhook.controller';

import { PingBotAgent } from './agents/pingbot.agent';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { ConversationService } from './services/conversation.service';
import { ToolsService } from './services/tools.service';
import { WhatsAppService } from './services/whatsapp.service';
import { TelegramService } from './services/telegram.service';
import { TwilioWhatsAppService } from './services/twilio-whatsapp.service';
import { CustomerStoreMappingService } from './services/customer-store-mapping.service';
import { TemplateService } from './services/template.service';


// Import schemas
import { Conversation, ConversationSchema } from './schemas/conversation.schema';
import { KnowledgeBase, KnowledgeBaseSchema } from './schemas/knowledge-base.schema';
import { PingBotMessage, PingBotMessageSchema } from './schemas/pingbot-message.schema';
import { Order, OrderSchema } from '@database/schemas/order.schema';

// Import other modules
import { OrdersModule } from '../orders/orders.module';
import { StoresModule } from '../stores/stores.module';
import { ProductsModule } from '../products/products.module';
import { CustomersModule } from '../customers/customers.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Conversation.name, schema: ConversationSchema },
      { name: KnowledgeBase.name, schema: KnowledgeBaseSchema },
      { name: PingBotMessage.name, schema: PingBotMessageSchema },
      { name: Order.name, schema: OrderSchema },
    ]),
    OrdersModule,
    StoresModule,
    ProductsModule,
    CustomersModule,
  ],
  controllers: [PingBotController, WhatsAppWebhookController, TelegramWebhookController, TwilioWebhookController],
  providers: [
    PingBotService,
    PingBotAgent,
    KnowledgeBaseService,
    ConversationService,
    ToolsService,
    TemplateService,
    WhatsAppService,
    TelegramService,
    TwilioWhatsAppService,
    CustomerStoreMappingService,
  ],
  exports: [PingBotService, PingBotAgent],
})
export class PingBotModule {}
