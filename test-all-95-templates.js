const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text, delay = 1500) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`\n📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 10000
    });
    console.log(`✅ Response: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, delay));
    return true;
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return false;
  }
}

async function testAll95Templates() {
  console.log('🧪 TESTING ALL 95+ PINGBOT TEMPLATES\n');
  console.log('============================================================\n');

  // SETUP
  console.log('🔧 SETUP: Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n📦 ORDER STATUS TEMPLATES (Templates 1-20):');
  await sendMessage('status');
  await sendMessage('track');
  await sendMessage('tracking');
  await sendMessage('where is my order');
  await sendMessage('order status');
  await sendMessage('my order');
  await sendMessage('order details');
  await sendMessage('order info');
  await sendMessage('check order');
  await sendMessage('order update');
  await sendMessage('what is my order status');
  await sendMessage('show my order');
  await sendMessage('order progress');
  await sendMessage('order tracking');
  await sendMessage('track my order');
  await sendMessage('order number');
  await sendMessage('find my order');
  await sendMessage('locate order');
  await sendMessage('order lookup');
  await sendMessage('check status');

  console.log('\n🚚 DELIVERY TEMPLATES (Templates 21-40):');
  await sendMessage('delivery');
  await sendMessage('shipping');
  await sendMessage('when will arrive');
  await sendMessage('delivery time');
  await sendMessage('shipping cost');
  await sendMessage('delivery status');
  await sendMessage('when will my order arrive');
  await sendMessage('where is my package');
  await sendMessage('delivery date');
  await sendMessage('shipping date');
  await sendMessage('estimated delivery');
  await sendMessage('delivery estimate');
  await sendMessage('when will it ship');
  await sendMessage('shipping time');
  await sendMessage('delivery tracking');
  await sendMessage('package status');
  await sendMessage('shipment');
  await sendMessage('courier');
  await sendMessage('delivery address');
  await sendMessage('shipping address');

  console.log('\n💰 PAYMENT TEMPLATES (Templates 41-60):');
  await sendMessage('payment');
  await sendMessage('pay');
  await sendMessage('refund');
  await sendMessage('money back');
  await sendMessage('payment method');
  await sendMessage('payment status');
  await sendMessage('how much did i pay');
  await sendMessage('receipt');
  await sendMessage('invoice');
  await sendMessage('bill');
  await sendMessage('payment details');
  await sendMessage('transaction');
  await sendMessage('payment history');
  await sendMessage('billing');
  await sendMessage('cost');
  await sendMessage('price');
  await sendMessage('amount');
  await sendMessage('total');
  await sendMessage('payment confirmation');
  await sendMessage('payment receipt');

  console.log('\n🛍️ PRODUCT TEMPLATES (Templates 61-80):');
  await sendMessage('products');
  await sendMessage('iphone');
  await sendMessage('laptop');
  await sendMessage('smartphone');
  await sendMessage('available');
  await sendMessage('in stock');
  await sendMessage('catalog');
  await sendMessage('all products');
  await sendMessage('show me products');
  await sendMessage('featured');
  await sendMessage('new arrivals');
  await sendMessage('bestsellers');
  await sendMessage('popular');
  await sendMessage('recommendations');
  await sendMessage('what do you sell');
  await sendMessage('product list');
  await sendMessage('inventory');
  await sendMessage('stock');
  await sendMessage('items');
  await sendMessage('merchandise');

  console.log('\n📂 CATEGORY & VARIANT TEMPLATES (Templates 81-90):');
  await sendMessage('categories');
  await sendMessage('category');
  await sendMessage('browse');
  await sendMessage('types');
  await sendMessage('sections');
  await sendMessage('variants');
  await sendMessage('colors');
  await sendMessage('sizes');
  await sendMessage('options');
  await sendMessage('models');

  console.log('\n🔧 ORDER MODIFICATION TEMPLATES (Templates 91-95):');
  await sendMessage('cancel');
  await sendMessage('cancel order');
  await sendMessage('modify');
  await sendMessage('change order');
  await sendMessage('update order');

  console.log('\n🆘 SUPPORT TEMPLATES (Templates 96-100):');
  await sendMessage('support');
  await sendMessage('help');
  await sendMessage('customer service');
  await sendMessage('talk to human');
  await sendMessage('agent');

  console.log('\n🎉 ALL 100+ TEMPLATE TESTS COMPLETED!');
  console.log('📊 Check server logs for detailed responses');
  console.log('🔍 Look for dynamic data vs generic fallbacks');
}

testAll95Templates().catch(console.error);
