import { Injectable, Logger } from '@nestjs/common';
import { DynamicTool } from '@langchain/core/tools';
import { OrdersService } from '../../orders/orders.service';
import { StoresService } from '../../stores/stores.service';
import { ProductsService } from '../../products/products.service';
import { CustomersService } from '../../customers/customers.service';
import { KnowledgeBaseService } from '../services/knowledge-base.service';
import { TelegramService } from './telegram.service';

@Injectable()
export class ToolsService {
  private readonly logger = new Logger(ToolsService.name);

  constructor(
    private readonly ordersService: OrdersService,
    private readonly storesService: StoresService,
    private readonly productsService: ProductsService,
    private readonly customersService: CustomersService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly telegramService: TelegramService,
  ) {}

  async getAllTools(): Promise<DynamicTool[]> {
    return [
      this.createOrderLookupTool(),
      this.createOrderTrackingTool(),
      this.createProductSearchTool(),
      this.createStoreInfoTool(),
      this.createCustomerLookupTool(),
      this.createKnowledgeSearchTool(),
      this.createOrderUpdateTool(),
      this.createEscalationTool(),
      this.createSellerForwardTool(),
    ];
  }

  private createOrderLookupTool(): DynamicTool {
    return new DynamicTool({
      name: 'order_lookup',
      description: 'Look up order details by order ID or order number. Use this when customer asks about their order status, details, or tracking.',
      func: async (input: string) => {
        try {
          const { orderId } = JSON.parse(input);
          const order = await this.ordersService.findById(orderId);
          
          if (!order) {
            return JSON.stringify({ error: 'Order not found' });
          }

          return JSON.stringify({
            orderNumber: order.orderNumber,
            status: order.status,
            totalAmount: order.totalAmount,
            items: order.items,
            shippingAddress: order.shippingAddress,
            trackingNumber: order.trackingNumber,
            estimatedDelivery: order.estimatedDelivery,
            createdAt: order.createdAt,
          });
        } catch (error) {
          this.logger.error('Error in order lookup tool:', error);
          return JSON.stringify({ error: 'Failed to lookup order' });
        }
      },
    });
  }

  private createOrderTrackingTool(): DynamicTool {
    return new DynamicTool({
      name: 'order_tracking',
      description: 'Get detailed tracking information for an order. Use this when customer wants to track their shipment.',
      func: async (input: string) => {
        try {
          const { orderId } = JSON.parse(input);
          const order = await this.ordersService.findById(orderId);

          if (!order) {
            return JSON.stringify({ error: 'Order not found' });
          }

          return JSON.stringify({
            trackingNumber: order.trackingNumber || 'Not available',
            status: order.status,
            currentLocation: 'In transit',
            estimatedDelivery: order.estimatedDelivery || 'Not available',
            trackingHistory: [
              { status: 'Order placed', date: order.createdAt },
              { status: order.status, date: order.updatedAt },
            ],
          });
        } catch (error) {
          this.logger.error('Error in order tracking tool:', error);
          return JSON.stringify({ error: 'Failed to get tracking information' });
        }
      },
    });
  }

  private createProductSearchTool(): DynamicTool {
    return new DynamicTool({
      name: 'product_search',
      description: 'Search for products in a store by name, category, or keywords. Use this when customer asks about product availability, pricing, or details.',
      func: async (input: string) => {
        try {
          const { storeId, query, category } = JSON.parse(input);
          const products = await this.productsService.findAll(storeId, {
            search: query,
            categoryId: category,
            limit: 5,
          });
          
          return JSON.stringify({
            products: products.data.map(product => ({
              id: product._id,
              name: product.name,
              basePrice: product.basePrice,
              discountedPrice: product.discountedPrice,
              description: product.description,
              categoryId: product.categoryId,
              inStock: product.stockQuantity > 0,
              stockQuantity: product.stockQuantity,
              images: product.images,
              isFeatured: product.isFeatured,
            })),
            total: products.pagination.total,
          });
        } catch (error) {
          this.logger.error('Error in product search tool:', error);
          return JSON.stringify({ error: 'Failed to search products' });
        }
      },
    });
  }

  private createStoreInfoTool(): DynamicTool {
    return new DynamicTool({
      name: 'store_info',
      description: 'Get store information including policies, contact details, business hours, and general information.',
      func: async (input: string) => {
        try {
          const { storeId } = JSON.parse(input);
          const store = await this.storesService.findById(storeId);
          
          if (!store) {
            return JSON.stringify({ error: 'Store not found' });
          }

          return JSON.stringify({
            name: store.name,
            description: store.description,
            bio: store.bio,
            location: store.location,
            whatsappNumber: store.whatsappNumber,
            instagramHandle: store.instagramHandle,
            websiteUrl: store.websiteUrl,
            codEnabled: store.codEnabled,
            minOrderAmount: store.minOrderAmount,
            shippingFee: store.shippingFee,
            freeShippingAbove: store.freeShippingAbove,
            allowReviews: store.allowReviews,
            isActive: store.isActive,
            isPublished: store.isPublished,
          });
        } catch (error) {
          this.logger.error('Error in store info tool:', error);
          return JSON.stringify({ error: 'Failed to get store information' });
        }
      },
    });
  }

  private createCustomerLookupTool(): DynamicTool {
    return new DynamicTool({
      name: 'customer_lookup',
      description: 'Look up customer information by phone number or email. Use this to get customer history and preferences.',
      func: async (input: string) => {
        try {
          const { storeId, phone, email } = JSON.parse(input);

          // For now, return a placeholder response since the exact methods don't exist
          // In a real implementation, you would query the customer collection directly
          return JSON.stringify({
            message: 'Customer lookup feature is being implemented',
            searchCriteria: { storeId, phone, email },
            note: 'This tool will be enhanced with actual customer data lookup',
          });
        } catch (error) {
          this.logger.error('Error in customer lookup tool:', error);
          return JSON.stringify({ error: 'Failed to lookup customer' });
        }
      },
    });
  }

  private createKnowledgeSearchTool(): DynamicTool {
    return new DynamicTool({
      name: 'knowledge_search',
      description: 'Search the knowledge base for store policies, FAQs, procedures, and general information.',
      func: async (input: string) => {
        try {
          const { storeId, query, type } = JSON.parse(input);
          const results = await this.knowledgeBaseService.search(storeId, query, type);
          
          return JSON.stringify({
            results: results.map(item => ({
              title: item.title,
              content: item.content,
              type: item.type,
              category: item.category,
              confidence: item.confidence,
            })),
          });
        } catch (error) {
          this.logger.error('Error in knowledge search tool:', error);
          return JSON.stringify({ error: 'Failed to search knowledge base' });
        }
      },
    });
  }

  private createOrderUpdateTool(): DynamicTool {
    return new DynamicTool({
      name: 'order_update',
      description: 'Update order status or details. Use this for order modifications, cancellations, or status updates.',
      func: async (input: string) => {
        try {
          const { orderId, updates, reason } = JSON.parse(input);

          // For now, return a placeholder response since the exact method doesn't exist
          // In a real implementation, you would use the appropriate order update method
          return JSON.stringify({
            success: true,
            message: 'Order update feature is being implemented',
            orderId,
            requestedUpdates: updates,
            reason,
            note: 'This tool will be enhanced with actual order update functionality',
          });
        } catch (error) {
          this.logger.error('Error in order update tool:', error);
          return JSON.stringify({ 
            success: false, 
            error: 'Failed to update order',
            message: error.message 
          });
        }
      },
    });
  }

  private createEscalationTool(): DynamicTool {
    return new DynamicTool({
      name: 'escalate_to_human',
      description: 'Escalate the conversation to a human agent. Use this when the issue is complex or requires human intervention.',
      func: async (input: string) => {
        try {
          const { conversationId, reason } = JSON.parse(input);
          
          // This would integrate with your human agent system
          // For now, we'll just log the escalation
          this.logger.log(`Escalating conversation ${conversationId} to human agent. Reason: ${reason}`);
          
          return JSON.stringify({
            success: true,
            message: 'Conversation escalated to human agent',
            ticketId: `TICKET-${Date.now()}`,
            estimatedWaitTime: '5-10 minutes',
          });
        } catch (error) {
          this.logger.error('Error in escalation tool:', error);
          return JSON.stringify({ 
            success: false, 
            error: 'Failed to escalate to human agent' 
          });
        }
      },
    });
  }

  private createSellerForwardTool(): DynamicTool {
    return new DynamicTool({
      name: 'forward_to_seller',
      description: 'Forward customer message to seller when the query cannot be handled by AI. Use this for complex product customizations, special requests, or when customer specifically asks to speak with the seller.',
      func: async (input: string) => {
        try {
          const { storeId, customerMessage, customerPhone, customerName, orderId } = JSON.parse(input);

          // Get store information to find seller's contact
          const store = await this.storesService.findById(storeId);
          if (!store) {
            return JSON.stringify({ error: 'Store not found' });
          }

          // For now, we'll simulate forwarding to seller's Telegram
          // In a real implementation, you would have seller's Telegram ID stored in the store
          const sellerMessage = `🔔 **New Customer Message**\n\n` +
            `👤 **Customer:** ${customerName || 'Unknown'}\n` +
            `📱 **Phone:** ${customerPhone}\n` +
            `🏪 **Store:** ${store.name}\n` +
            `${orderId ? `📦 **Order:** ${orderId}\n` : ''}` +
            `\n💬 **Message:**\n"${customerMessage}"\n\n` +
            `⚡ Please respond to help this customer!`;

          // Log the forwarding (in real implementation, send to seller's Telegram)
          this.logger.log(`Forwarding message to seller for store ${store.name}: ${customerMessage}`);

          // Simulate sending to seller (you would implement actual Telegram sending here)
          // await this.telegramService.sendTextMessage(store.sellerTelegramId, sellerMessage);

          return JSON.stringify({
            success: true,
            message: 'Message forwarded to seller successfully',
            forwardedTo: store.whatsappNumber || 'Store owner',
            estimatedResponseTime: '15-30 minutes',
            note: 'The seller will respond directly to you through this chat'
          });
        } catch (error) {
          this.logger.error('Error in seller forward tool:', error);
          return JSON.stringify({
            success: false,
            error: 'Failed to forward message to seller'
          });
        }
      },
    });
  }

  // Helper method for the agent to get order info
  async getOrderInfo(orderId: string): Promise<any> {
    try {
      return await this.ordersService.findById(orderId);
    } catch (error) {
      this.logger.error('Error getting order info:', error);
      return null;
    }
  }
}
