import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Logger,
  HttpStatus,
  HttpException,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { Response } from 'express';
import { PingBotService } from '../pingbot.service';
import { WhatsAppService } from '../services/whatsapp.service';
import { StoresService } from '../../stores/stores.service';
import { MessageType } from '../schemas/pingbot-message.schema';

@ApiTags('WhatsApp Webhook')
@Controller('pingbot/whatsapp')
export class WhatsAppWebhookController {
  private readonly logger = new Logger(WhatsAppWebhookController.name);

  constructor(
    private readonly pingBotService: PingBotService,
    private readonly whatsAppService: WhatsAppService,
    private readonly storesService: StoresService,
  ) {}

  @Get('webhook')
  @ApiOperation({ summary: 'Verify WhatsApp webhook' })
  @ApiQuery({ name: 'hub.mode', description: 'Webhook mode' })
  @ApiQuery({ name: 'hub.verify_token', description: 'Verification token' })
  @ApiQuery({ name: 'hub.challenge', description: 'Challenge string' })
  @ApiResponse({ status: 200, description: 'Webhook verified successfully' })
  @ApiResponse({ status: 403, description: 'Webhook verification failed' })
  async verifyWebhook(
    @Query('hub.mode') mode: string,
    @Query('hub.verify_token') token: string,
    @Query('hub.challenge') challenge: string,
    @Res() res: Response,
  ) {
    try {
      this.logger.log(`Webhook verification attempt: mode=${mode}, token=${token}`);
      
      const verificationResult = this.whatsAppService.verifyWebhook(mode, token, challenge);
      
      if (verificationResult) {
        this.logger.log('Webhook verified successfully');
        return res.status(200).send(verificationResult);
      } else {
        this.logger.warn('Webhook verification failed');
        return res.status(403).send('Forbidden');
      }
    } catch (error) {
      this.logger.error('Error verifying webhook:', error);
      return res.status(500).send('Internal Server Error');
    }
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Handle incoming WhatsApp messages' })
  @ApiBody({ description: 'WhatsApp webhook payload' })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload' })
  async handleWebhook(@Body() body: any, @Res() res: Response) {
    try {
      this.logger.debug('Received WhatsApp webhook:', JSON.stringify(body, null, 2));

      // Parse the webhook message
      const message = this.whatsAppService.parseWebhookMessage(body);
      
      if (!message) {
        this.logger.warn('No valid message found in webhook payload');
        return res.status(200).send('OK');
      }

      this.logger.log(`Processing message from ${message.from}: ${message.text?.body || message.type}`);

      // For now, we'll use a default store. In production, you'd determine the store
      // based on the phone number or other routing logic
      const defaultStore = await this.getDefaultStore();
      
      if (!defaultStore) {
        this.logger.error('No default store found for WhatsApp integration');
        return res.status(200).send('OK');
      }

      // Process the message with PingBot
      const result = await this.pingBotService.processMessage({
        storeId: defaultStore._id.toString(),
        customerPhone: message.from,
        message: message.text?.body || `[${message.type} message]`,
        whatsappMessageId: message.id,
        messageType: this.mapWhatsAppMessageType(message.type),
        metadata: {
          whatsappTimestamp: message.timestamp,
          messageType: message.type,
          originalMessage: message,
        },
      });

      // Send response back to WhatsApp
      if (result.response.message) {
        await this.whatsAppService.sendTextMessage(message.from, result.response.message);
      }

      // Handle escalation if needed
      if (result.response.requiresHuman) {
        this.logger.log(`Message escalated to human agent for conversation ${result.conversationId}`);
        // You could send a notification to store owners here
      }

      return res.status(200).send('OK');
    } catch (error) {
      this.logger.error('Error processing WhatsApp webhook:', error);
      return res.status(200).send('OK'); // Always return 200 to WhatsApp to avoid retries
    }
  }

  @Post('send-message')
  @ApiOperation({ summary: 'Send message via WhatsApp (for testing)' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', description: 'Recipient phone number' },
        message: { type: 'string', description: 'Message text' },
      },
      required: ['to', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendMessage(@Body() body: { to: string; message: string }) {
    try {
      const result = await this.whatsAppService.sendTextMessage(body.to, body.message);
      
      return {
        message: 'Message sent successfully',
        data: result,
      };
    } catch (error) {
      this.logger.error('Error sending WhatsApp message:', error);
      throw new HttpException(
        'Failed to send message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('config')
  @ApiOperation({ summary: 'Get WhatsApp configuration status' })
  @ApiResponse({ status: 200, description: 'Configuration retrieved successfully' })
  async getConfig() {
    try {
      const config = this.whatsAppService.getConfiguration();
      
      return {
        message: 'WhatsApp configuration retrieved successfully',
        data: config,
      };
    } catch (error) {
      this.logger.error('Error getting WhatsApp config:', error);
      throw new HttpException(
        'Failed to get configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async getDefaultStore() {
    try {
      // Get the first active store as default
      // In production, you'd have proper routing logic
      const stores = await this.storesService.findAll({ limit: 1, page: 1 });
      return stores.data[0] || null;
    } catch (error) {
      this.logger.error('Error getting default store:', error);
      return null;
    }
  }

  private mapWhatsAppMessageType(whatsappType: string): MessageType {
    switch (whatsappType) {
      case 'text':
        return MessageType.TEXT;
      case 'image':
        return MessageType.IMAGE;
      case 'document':
        return MessageType.DOCUMENT;
      case 'audio':
        return MessageType.AUDIO;
      case 'video':
        return MessageType.VIDEO;
      case 'location':
        return MessageType.LOCATION;
      case 'contacts':
        return MessageType.CONTACT;
      default:
        return MessageType.TEXT;
    }
  }
}
