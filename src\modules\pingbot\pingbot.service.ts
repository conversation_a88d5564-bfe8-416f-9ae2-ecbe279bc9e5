import { Injectable, Logger } from '@nestjs/common';
import { PingBotAgent, PingBotContext, PingBotResponse } from './agents/pingbot.agent';
import { ConversationService } from './services/conversation.service';
import { KnowledgeBaseService } from './services/knowledge-base.service';
import { StoresService } from '../stores/stores.service';
import { MessageDirection, MessageType } from './schemas/pingbot-message.schema';
import { ConversationStatus } from './schemas/conversation.schema';

export interface ProcessMessageDto {
  storeId: string;
  customerPhone: string;
  customerName?: string;
  message: string;
  orderId?: string;
  whatsappMessageId?: string;
  messageType?: MessageType;
  metadata?: Record<string, any>;
}

export interface PingBotStats {
  conversations: {
    total: number;
    active: number;
    resolved: number;
    escalated: number;
  };
  messages: {
    total: number;
    processed: number;
    pending: number;
  };
  performance: {
    averageResponseTime: number;
    resolutionRate: number;
    escalationRate: number;
    satisfactionScore: number;
  };
}

@Injectable()
export class PingBotService {
  private readonly logger = new Logger(PingBotService.name);

  constructor(
    private readonly pingBotAgent: PingBotAgent,
    private readonly conversationService: ConversationService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly storesService: StoresService,
  ) {}

  async processMessage(dto: ProcessMessageDto): Promise<{
    response: PingBotResponse;
    conversationId: string;
    messageId: string;
  }> {
    try {
      this.logger.debug(`Processing message from ${dto.customerPhone} for store ${dto.storeId}`);

      // Get store information
      const store = await this.storesService.findById(dto.storeId);
      if (!store) {
        throw new Error(`Store ${dto.storeId} not found`);
      }

      // Find or create conversation
      const conversation = await this.conversationService.findOrCreateConversation(
        dto.storeId,
        dto.customerPhone,
        dto.orderId,
      );

      // Save incoming message
      const incomingMessage = await this.conversationService.addMessage({
        conversationId: conversation._id.toString(),
        storeId: dto.storeId,
        customerPhone: dto.customerPhone,
        direction: MessageDirection.INBOUND,
        type: dto.messageType || MessageType.TEXT,
        content: dto.message,
        whatsappMessageId: dto.whatsappMessageId,
        metadata: dto.metadata,
      });

      // Get conversation history for context
      const messageHistory = await this.conversationService.getConversationHistory(
        conversation._id.toString(),
        20, // Last 20 messages for context
      );

      // Prepare context for the agent
      const context: PingBotContext = {
        storeId: dto.storeId,
        storeName: store.name,
        customerPhone: dto.customerPhone,
        customerName: dto.customerName,
        orderId: dto.orderId,
        conversationId: conversation._id.toString(),
        messageHistory,
        metadata: {
          ...dto.metadata,
          conversationStatus: conversation.status,
          messageCount: conversation.messageCount,
        },
      };

      // Process message with AI agent
      const response = await this.pingBotAgent.processMessage(dto.message, context);

      // Save AI response
      const outgoingMessage = await this.conversationService.addMessage({
        conversationId: conversation._id.toString(),
        storeId: dto.storeId,
        customerPhone: dto.customerPhone,
        direction: MessageDirection.OUTBOUND,
        type: MessageType.TEXT,
        content: response.message,
        intent: response.intent,
        confidence: response.confidence,
        aiContext: response.metadata,
      });

      // Handle escalation if needed
      if (response.requiresHuman) {
        await this.conversationService.updateConversationStatus(
          conversation._id.toString(),
          ConversationStatus.ESCALATED,
        );
        this.logger.log(`Escalated conversation ${conversation._id} to human agent`);
      }

      // Mark incoming message as processed
      await this.conversationService.markMessageAsProcessed(incomingMessage._id.toString());

      this.logger.debug(`Successfully processed message for conversation ${conversation._id}`);

      return {
        response,
        conversationId: conversation._id.toString(),
        messageId: outgoingMessage._id.toString(),
      };
    } catch (error) {
      this.logger.error('Error processing message:', error);
      throw error;
    }
  }

  async getConversation(conversationId: string) {
    try {
      const conversation = await this.conversationService.findConversationById(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const messages = await this.conversationService.getConversationMessages(conversationId);
      
      return {
        conversation,
        messages: messages.messages,
        pagination: {
          total: messages.total,
          page: messages.page,
          totalPages: messages.totalPages,
        },
      };
    } catch (error) {
      this.logger.error('Error getting conversation:', error);
      throw error;
    }
  }

  async getStoreConversations(
    storeId: string,
    status?: ConversationStatus,
    page: number = 1,
    limit: number = 20,
  ) {
    try {
      return await this.conversationService.getStoreConversations(storeId, status, page, limit);
    } catch (error) {
      this.logger.error('Error getting store conversations:', error);
      throw error;
    }
  }

  async escalateConversation(conversationId: string, reason: string, assignedTo?: string) {
    try {
      const updated = await this.conversationService.updateConversationStatus(
        conversationId,
        ConversationStatus.ESCALATED,
        assignedTo,
      );

      if (updated) {
        this.logger.log(`Manually escalated conversation ${conversationId}. Reason: ${reason}`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error escalating conversation:', error);
      throw error;
    }
  }

  async resolveConversation(conversationId: string) {
    try {
      const updated = await this.conversationService.updateConversationStatus(
        conversationId,
        ConversationStatus.RESOLVED,
      );

      if (updated) {
        this.logger.log(`Resolved conversation ${conversationId}`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error resolving conversation:', error);
      throw error;
    }
  }

  async addConversationFeedback(conversationId: string, rating: number, feedback?: string) {
    try {
      const updated = await this.conversationService.addFeedback(conversationId, rating, feedback);
      
      if (updated) {
        this.logger.log(`Added feedback to conversation ${conversationId}: ${rating}/5`);
      }

      return updated;
    } catch (error) {
      this.logger.error('Error adding conversation feedback:', error);
      throw error;
    }
  }

  async getStats(storeId?: string): Promise<PingBotStats> {
    try {
      const [conversationStats, agentStatus] = await Promise.all([
        this.conversationService.getConversationStats(storeId),
        this.pingBotAgent.getAgentStatus(),
      ]);

      const totalConversations = conversationStats.total;
      const resolvedConversations = conversationStats.resolved;
      const escalatedConversations = conversationStats.escalated;

      return {
        conversations: {
          total: conversationStats.total,
          active: conversationStats.active,
          resolved: conversationStats.resolved,
          escalated: conversationStats.escalated,
        },
        messages: {
          total: conversationStats.messageVolume,
          processed: conversationStats.messageVolume, // Assuming all are processed for now
          pending: 0, // Would need to implement pending message tracking
        },
        performance: {
          averageResponseTime: 2.5, // Would need to implement response time tracking
          resolutionRate: totalConversations > 0 ? (resolvedConversations / totalConversations) * 100 : 0,
          escalationRate: totalConversations > 0 ? (escalatedConversations / totalConversations) * 100 : 0,
          satisfactionScore: conversationStats.averageRating,
        },
      };
    } catch (error) {
      this.logger.error('Error getting PingBot stats:', error);
      return {
        conversations: { total: 0, active: 0, resolved: 0, escalated: 0 },
        messages: { total: 0, processed: 0, pending: 0 },
        performance: { averageResponseTime: 0, resolutionRate: 0, escalationRate: 0, satisfactionScore: 0 },
      };
    }
  }

  async processUnprocessedMessages(): Promise<number> {
    try {
      const unprocessedMessages = await this.conversationService.getUnprocessedMessages(50);
      let processedCount = 0;

      for (const message of unprocessedMessages) {
        try {
          // Get conversation and store info
          const conversation = await this.conversationService.findConversationById(
            message.conversationId.toString(),
          );
          
          if (!conversation) {
            continue;
          }

          const store = await this.storesService.findById(conversation.storeId.toString());
          if (!store) {
            continue;
          }

          // Process the message
          await this.processMessage({
            storeId: conversation.storeId.toString(),
            customerPhone: message.customerPhone,
            message: message.content,
            orderId: conversation.orderId?.toString(),
            whatsappMessageId: message.whatsappMessageId,
            messageType: message.type,
            metadata: message.metadata,
          });

          processedCount++;
        } catch (error) {
          this.logger.error(`Error processing unprocessed message ${message._id}:`, error);
        }
      }

      if (processedCount > 0) {
        this.logger.log(`Processed ${processedCount} unprocessed messages`);
      }

      return processedCount;
    } catch (error) {
      this.logger.error('Error processing unprocessed messages:', error);
      return 0;
    }
  }

  async getAgentStatus() {
    try {
      return await this.pingBotAgent.getAgentStatus();
    } catch (error) {
      this.logger.error('Error getting agent status:', error);
      return {
        isInitialized: false,
        toolsCount: 0,
        modelName: 'unknown',
      };
    }
  }
}
