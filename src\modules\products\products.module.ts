import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { ProductsController } from './products.controller';
import { ProductsService } from './products.service';

// Import Stores module for validation
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { Product, ProductSchema } from '@database/schemas/product.schema';
import { ProductImage, ProductImageSchema } from '@database/schemas/product-image.schema';
import { ProductVariant, ProductVariantSchema } from '@database/schemas/product-variant.schema';
import { Category, CategorySchema } from '@database/schemas/category.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema },
      { name: ProductImage.name, schema: ProductImageSchema },
      { name: ProductVariant.name, schema: ProductVariantSchema },
      { name: Category.name, schema: CategorySchema },
      { name: Store.name, schema: StoreSchema },
    ]),
    StoresModule,
  ],
  controllers: [ProductsController],
  providers: [ProductsService],
  exports: [ProductsService],
})
export class ProductsModule {}
