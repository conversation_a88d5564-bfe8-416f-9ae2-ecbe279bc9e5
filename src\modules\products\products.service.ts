import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { Product, ProductDocument } from '@database/schemas/product.schema';
import { ProductImage, ProductImageDocument } from '@database/schemas/product-image.schema';
import { ProductVariant, ProductVariantDocument } from '@database/schemas/product-variant.schema';
import { Store, StoreDocument } from '@database/schemas/store.schema';
import { Category, CategoryDocument } from '@database/schemas/category.schema';

import { StoresService } from '@modules/stores/stores.service';
import { ERROR_CODES, PRODUCT_STATUS } from '@common/constants';
import { PaginationResult } from '@common/interfaces';

// DTOs
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(ProductImage.name) private productImageModel: Model<ProductImageDocument>,
    @InjectModel(ProductVariant.name) private productVariantModel: Model<ProductVariantDocument>,
    @InjectModel(Store.name) private storeModel: Model<StoreDocument>,
    @InjectModel(Category.name) private categoryModel: Model<CategoryDocument>,
    private storesService: StoresService,
  ) {}

  async create(storeId: string, userId: string, createProductDto: CreateProductDto): Promise<ProductDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only add products to your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Validate category if provided
    if (createProductDto.categoryId) {
      const category = await this.categoryModel.findOne({
        _id: createProductDto.categoryId,
        storeId,
      });
      if (!category) {
        throw new BadRequestException({
          message: 'Category not found in this store',
          code: ERROR_CODES.VALIDATION_ERROR,
        });
      }
    }

    // Validate discounted price
    if (createProductDto.discountedPrice && createProductDto.discountedPrice >= createProductDto.basePrice) {
      throw new BadRequestException({
        message: 'Discounted price must be less than base price',
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Check SKU uniqueness within store
    if (createProductDto.sku) {
      const existingProduct = await this.productModel.findOne({
        storeId,
        sku: createProductDto.sku,
      });
      if (existingProduct) {
        throw new BadRequestException({
          message: 'SKU already exists in this store',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    const product = new this.productModel({
      storeId,
      ...createProductDto,
      status: PRODUCT_STATUS.DRAFT,
    });

    await product.save();

    this.logger.log(`Product created: ${product.name} in store ${storeId}`);
    return product;
  }

  async findAll(storeId: string, query: ProductQueryDto): Promise<PaginationResult<ProductDocument>> {
    const {
      page = 1,
      limit = 20,
      search,
      categoryId,
      status,
      isFeatured,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = query;

    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = { storeId };

    if (search) {
      filter.$text = { $search: search };
    }

    if (categoryId) {
      filter.categoryId = categoryId;
    }

    if (status) {
      filter.status = status;
    }

    if (typeof isFeatured === 'boolean') {
      filter.isFeatured = isFeatured;
    }

    // Build sort
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [products, total] = await Promise.all([
      this.productModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('categoryId', 'name')
        .populate('images')
        .populate('variants')
        .exec(),
      this.productModel.countDocuments(filter),
    ]);

    return {
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    };
  }

  async findById(productId: string): Promise<ProductDocument> {
    const product = await this.productModel
      .findById(productId)
      .populate('categoryId', 'name')
      .populate('images')
      .populate('variants')
      .exec();

    if (!product) {
      throw new NotFoundException({
        message: 'Product not found',
        code: ERROR_CODES.PRODUCT_NOT_FOUND,
      });
    }

    return product;
  }

  async findByIdAndStore(productId: string, storeId: string): Promise<ProductDocument> {
    const product = await this.productModel
      .findOne({ _id: productId, storeId })
      .populate('categoryId', 'name')
      .populate('images')
      .populate('variants')
      .exec();

    if (!product) {
      throw new NotFoundException({
        message: 'Product not found',
        code: ERROR_CODES.PRODUCT_NOT_FOUND,
      });
    }

    return product;
  }

  async update(
    productId: string,
    storeId: string,
    userId: string,
    updateProductDto: UpdateProductDto,
  ): Promise<ProductDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    // Validate category if provided
    if (updateProductDto.categoryId) {
      const category = await this.categoryModel.findOne({
        _id: updateProductDto.categoryId,
        storeId,
      });
      if (!category) {
        throw new BadRequestException({
          message: 'Category not found in this store',
          code: ERROR_CODES.VALIDATION_ERROR,
        });
      }
    }

    // Validate discounted price
    const basePrice = updateProductDto.basePrice ?? product.basePrice;
    if (updateProductDto.discountedPrice && updateProductDto.discountedPrice >= basePrice) {
      throw new BadRequestException({
        message: 'Discounted price must be less than base price',
        code: ERROR_CODES.VALIDATION_ERROR,
      });
    }

    // Check SKU uniqueness within store
    if (updateProductDto.sku && updateProductDto.sku !== product.sku) {
      const existingProduct = await this.productModel.findOne({
        storeId,
        sku: updateProductDto.sku,
        _id: { $ne: productId },
      });
      if (existingProduct) {
        throw new BadRequestException({
          message: 'SKU already exists in this store',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    const updatedProduct = await this.productModel
      .findByIdAndUpdate(
        productId,
        {
          ...updateProductDto,
          updatedAt: new Date(),
        },
        { new: true },
      )
      .populate('categoryId', 'name')
      .populate('images')
      .populate('variants')
      .exec();

    this.logger.log(`Product updated: ${product.name} in store ${storeId}`);
    return updatedProduct!;
  }

  async delete(productId: string, storeId: string, userId: string): Promise<void> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only delete products from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    // Delete related data
    await Promise.all([
      this.productImageModel.deleteMany({ productId }),
      this.productVariantModel.deleteMany({ productId }),
    ]);

    await this.productModel.findByIdAndDelete(productId);

    this.logger.log(`Product deleted: ${product.name} from store ${storeId}`);
  }

  async updateStatus(
    productId: string,
    storeId: string,
    userId: string,
    status: string,
  ): Promise<ProductDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    const updatedProduct = await this.productModel
      .findByIdAndUpdate(
        productId,
        {
          status,
          updatedAt: new Date(),
        },
        { new: true },
      )
      .populate('categoryId', 'name')
      .populate('images')
      .populate('variants')
      .exec();

    this.logger.log(`Product status updated: ${product.name} to ${status}`);
    return updatedProduct!;
  }

  async toggleFeatured(
    productId: string,
    storeId: string,
    userId: string,
    isFeatured: boolean,
  ): Promise<ProductDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    const updatedProduct = await this.productModel
      .findByIdAndUpdate(
        productId,
        {
          isFeatured,
          updatedAt: new Date(),
        },
        { new: true },
      )
      .populate('categoryId', 'name')
      .populate('images')
      .populate('variants')
      .exec();

    this.logger.log(`Product featured status updated: ${product.name} to ${isFeatured}`);
    return updatedProduct!;
  }

  async incrementViewCount(productId: string): Promise<void> {
    await this.productModel.findByIdAndUpdate(productId, {
      $inc: { viewCount: 1 },
    });
  }

  async incrementClickCount(productId: string): Promise<void> {
    await this.productModel.findByIdAndUpdate(productId, {
      $inc: { clickCount: 1 },
    });
  }

  async incrementAddToCartCount(productId: string): Promise<void> {
    await this.productModel.findByIdAndUpdate(productId, {
      $inc: { addToCartCount: 1 },
    });
  }

  async incrementPurchaseCount(productId: string): Promise<void> {
    await this.productModel.findByIdAndUpdate(productId, {
      $inc: { purchaseCount: 1 },
    });
  }

  // Product Images Management
  async addImages(
    productId: string,
    storeId: string,
    userId: string,
    imageUrls: string[],
  ): Promise<ProductImageDocument[]> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only add images to products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    // Get current max sort order
    const lastImage = await this.productImageModel
      .findOne({ productId })
      .sort({ sortOrder: -1 });

    let sortOrder = lastImage ? lastImage.sortOrder + 1 : 0;

    const images = imageUrls.map((url, index) => ({
      productId,
      imageUrl: url,
      sortOrder: sortOrder + index,
      isMain: false, // Will be set manually
    }));

    const createdImages = await this.productImageModel.insertMany(images);

    // If this is the first image, make it main
    if (!lastImage && createdImages.length > 0) {
      createdImages[0].isMain = true;
      await createdImages[0].save();
    }

    this.logger.log(`${images.length} images added to product: ${product.name}`);
    return createdImages as any;
  }

  async deleteImage(
    imageId: string,
    storeId: string,
    userId: string,
  ): Promise<void> {
    const image = await this.productImageModel.findById(imageId);
    if (!image) {
      throw new NotFoundException({
        message: 'Image not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    // Verify store ownership through product
    const product = await this.findByIdAndStore(image.productId.toString(), storeId);
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only delete images from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    await this.productImageModel.findByIdAndDelete(imageId);

    this.logger.log(`Image deleted from product: ${product.name}`);
  }

  async setMainImage(
    imageId: string,
    storeId: string,
    userId: string,
  ): Promise<ProductImageDocument> {
    const image = await this.productImageModel.findById(imageId);
    if (!image) {
      throw new NotFoundException({
        message: 'Image not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    // Verify store ownership through product
    const product = await this.findByIdAndStore(image.productId.toString(), storeId);
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update images in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Set all other images of this product to not main
    await this.productImageModel.updateMany(
      { productId: image.productId },
      { isMain: false },
    );

    // Set this image as main
    image.isMain = true;
    await image.save();

    this.logger.log(`Main image set for product: ${product.name}`);
    return image;
  }

  async reorderImages(
    productId: string,
    storeId: string,
    userId: string,
    imageIds: string[],
  ): Promise<ProductImageDocument[]> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only reorder images in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    // Update sort order for each image
    const updatePromises = imageIds.map((imageId, index) =>
      this.productImageModel.findByIdAndUpdate(imageId, { sortOrder: index }),
    );

    await Promise.all(updatePromises);

    const reorderedImages = await this.productImageModel
      .find({ productId })
      .sort({ sortOrder: 1 });

    this.logger.log(`Images reordered for product: ${product.name}`);
    return reorderedImages;
  }

  // Product Variants Management
  async addVariant(
    productId: string,
    storeId: string,
    userId: string,
    variantData: any,
  ): Promise<ProductVariantDocument> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only add variants to products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const product = await this.findByIdAndStore(productId, storeId);

    // Check SKU uniqueness if provided
    if (variantData.sku) {
      const existingVariant = await this.productVariantModel.findOne({
        sku: variantData.sku,
      });
      if (existingVariant) {
        throw new BadRequestException({
          message: 'Variant SKU already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    // Get current max sort order
    const lastVariant = await this.productVariantModel
      .findOne({ productId })
      .sort({ sortOrder: -1 });

    const sortOrder = lastVariant ? lastVariant.sortOrder + 1 : 0;

    const variant = new this.productVariantModel({
      productId,
      ...variantData,
      sortOrder,
    });

    await variant.save();

    this.logger.log(`Variant added to product: ${product.name}`);
    return variant;
  }

  async updateVariant(
    variantId: string,
    storeId: string,
    userId: string,
    variantData: any,
  ): Promise<ProductVariantDocument> {
    const variant = await this.productVariantModel.findById(variantId);
    if (!variant) {
      throw new NotFoundException({
        message: 'Variant not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    // Verify store ownership through product
    const product = await this.findByIdAndStore(variant.productId.toString(), storeId);
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update variants in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Check SKU uniqueness if provided and changed
    if (variantData.sku && variantData.sku !== variant.sku) {
      const existingVariant = await this.productVariantModel.findOne({
        sku: variantData.sku,
        _id: { $ne: variantId },
      });
      if (existingVariant) {
        throw new BadRequestException({
          message: 'Variant SKU already exists',
          code: ERROR_CODES.DUPLICATE_VALUE,
        });
      }
    }

    const updatedVariant = await this.productVariantModel.findByIdAndUpdate(
      variantId,
      {
        ...variantData,
        updatedAt: new Date(),
      },
      { new: true },
    );

    this.logger.log(`Variant updated for product: ${product.name}`);
    return updatedVariant!;
  }

  async deleteVariant(
    variantId: string,
    storeId: string,
    userId: string,
  ): Promise<void> {
    const variant = await this.productVariantModel.findById(variantId);
    if (!variant) {
      throw new NotFoundException({
        message: 'Variant not found',
        code: ERROR_CODES.NOT_FOUND,
      });
    }

    // Verify store ownership through product
    const product = await this.findByIdAndStore(variant.productId.toString(), storeId);
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only delete variants from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    await this.productVariantModel.findByIdAndDelete(variantId);

    this.logger.log(`Variant deleted from product: ${product.name}`);
  }

  // Bulk Operations
  async bulkUpdateProducts(
    storeId: string,
    userId: string,
    productIds: string[],
    updates: any,
  ): Promise<{ updatedCount: number }> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only update products in your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    const result = await this.productModel.updateMany(
      {
        _id: { $in: productIds },
        storeId,
      },
      {
        ...updates,
        updatedAt: new Date(),
      },
    );

    this.logger.log(`Bulk updated ${result.modifiedCount} products in store ${storeId}`);
    return { updatedCount: result.modifiedCount };
  }

  async bulkDeleteProducts(
    storeId: string,
    userId: string,
    productIds: string[],
  ): Promise<{ deletedCount: number }> {
    // Verify store ownership
    const store = await this.storesService.findById(storeId);
    if (store.userId.toString() !== userId) {
      throw new ForbiddenException({
        message: 'You can only delete products from your own store',
        code: ERROR_CODES.FORBIDDEN,
      });
    }

    // Delete related data first
    await Promise.all([
      this.productImageModel.deleteMany({ productId: { $in: productIds } }),
      this.productVariantModel.deleteMany({ productId: { $in: productIds } }),
    ]);

    const result = await this.productModel.deleteMany({
      _id: { $in: productIds },
      storeId,
    });

    this.logger.log(`Bulk deleted ${result.deletedCount} products from store ${storeId}`);
    return { deletedCount: result.deletedCount };
  }
}
