const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 8000
    });
    console.log(`✅ Response: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, 1500));
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

async function testKeyTemplates() {
  console.log('🧪 TESTING KEY PINGBOT TEMPLATES\n');
  console.log('============================================================\n');

  console.log('1️⃣ SETUP: Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n2️⃣ ORDER STATUS TEMPLATES:');
  await sendMessage('status');
  await sendMessage('track');
  await sendMessage('where is my order');
  
  console.log('\n3️⃣ DELIVERY TEMPLATES:');
  await sendMessage('delivery');
  await sendMessage('shipping');
  await sendMessage('when will arrive');
  
  console.log('\n4️⃣ PAYMENT TEMPLATES:');
  await sendMessage('payment');
  await sendMessage('refund');
  await sendMessage('how much did i pay');
  
  console.log('\n5️⃣ PRODUCT TEMPLATES:');
  await sendMessage('products');
  await sendMessage('price');
  await sendMessage('available');
  
  console.log('\n6️⃣ SUPPORT TEMPLATES:');
  await sendMessage('support');
  await sendMessage('help');
  await sendMessage('talk to human');
  
  console.log('\n7️⃣ ORDER MODIFICATION TEMPLATES:');
  await sendMessage('cancel');
  await sendMessage('modify');
  
  console.log('\n8️⃣ CUSTOM QUERIES:');
  await sendMessage('how much is it');
  await sendMessage('what is the price');
  await sendMessage('is this available');

  console.log('\n🎉 KEY TEMPLATE TESTING COMPLETED!');
  console.log('📊 Check server logs for template matching results');
}

testKeyTemplates().catch(console.error);
