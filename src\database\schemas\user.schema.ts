import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export type UserDocument = User & Document & {
  _id: Types.ObjectId;
};

@Schema({
  timestamps: true,
  collection: 'users',
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.passwordHash; // Never expose password hash
      return ret;
    },
  },
})
export class User extends BaseSchema {
  @Prop({ 
    required: true, 
    unique: true, 
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  })
  email: string;

  @Prop({ 
    unique: true, 
    sparse: true,
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number']
  })
  phone?: string;

  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  fullName: string;

  @Prop({ trim: true })
  avatarUrl?: string;

  @Prop({ default: false })
  emailVerified: boolean;

  @Prop({ default: false })
  phoneVerified: boolean;

  @Prop({ 
    unique: true, 
    sparse: true,
    trim: true
  })
  googleId?: string;

  @Prop({ 
    minlength: 6,
    select: false // Don't include in queries by default
  })
  passwordHash?: string;

  @Prop()
  lastLogin?: Date;

  // Virtual for stores
  stores?: any[];
}

export const UserSchema = SchemaFactory.createForClass(User);

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ phone: 1 });
UserSchema.index({ googleId: 1 });
UserSchema.index({ createdAt: -1 });

// Virtual populate for stores
UserSchema.virtual('stores', {
  ref: 'Store',
  localField: '_id',
  foreignField: 'userId',
});

// Pre-save middleware
UserSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  next();
});

// Instance methods
UserSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.passwordHash;
  delete user.__v;
  user.id = user._id;
  delete user._id;
  return user;
};
