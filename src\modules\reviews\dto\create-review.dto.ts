import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateReviewDto {
  @ApiProperty({
    description: 'Product ID being reviewed',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  productId: string;

  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON>',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2, { message: 'Customer name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Customer name must not exceed 100 characters' })
  customerName: string;

  @ApiProperty({
    description: 'Customer phone number',
    example: '+919876543210',
  })
  @IsString()
  customerPhone: string;

  @ApiPropertyOptional({
    description: 'Customer email',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  customerEmail?: string;

  @ApiProperty({
    description: 'Rating (1-5 stars)',
    example: 5,
    minimum: 1,
    maximum: 5,
  })
  @IsNumber()
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must not exceed 5' })
  rating: number;

  @ApiProperty({
    description: 'Review comment',
    example: 'Great product! Highly recommended.',
    minLength: 10,
    maxLength: 1000,
  })
  @IsString()
  @MinLength(10, { message: 'Review comment must be at least 10 characters long' })
  @MaxLength(1000, { message: 'Review comment must not exceed 1000 characters' })
  comment: string;

  @ApiPropertyOptional({
    description: 'Order ID (if review is for a purchased product)',
    example: '507f1f77bcf86cd799439012',
  })
  @IsOptional()
  @IsString()
  orderId?: string;
}
