import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';
import { ANALYTICS_EVENTS } from '@common/constants';

export type AnalyticsEventDocument = AnalyticsEvent & Document;

@Schema({
  timestamps: true,
  collection: 'analytics_events',
  // TTL index to automatically delete old events after 1 year
  expires: 365 * 24 * 60 * 60, // 1 year in seconds
})
export class AnalyticsEvent extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  // Event Details
  @Prop({ 
    required: true,
    enum: Object.values(ANALYTICS_EVENTS),
    index: true
  })
  eventType: string;

  @Prop({ 
    type: Object,
    default: {}
  })
  eventData?: Record<string, any>;

  // Context
  @Prop({ 
    required: true,
    trim: true,
    maxlength: 255,
    index: true
  })
  sessionId: string;

  @Prop({ 
    trim: true,
    maxlength: 1000
  })
  userAgent?: string;

  @Prop({ 
    trim: true,
    maxlength: 45 // IPv6 max length
  })
  ipAddress?: string;

  @Prop({ 
    trim: true,
    maxlength: 500
  })
  referrer?: string;

  // UTM Parameters
  @Prop({ 
    trim: true,
    maxlength: 100
  })
  utmSource?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  utmMedium?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  utmCampaign?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  utmTerm?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  utmContent?: string;

  // Product Context (if applicable)
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Product',
    index: true
  })
  productId?: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Category'
  })
  categoryId?: Types.ObjectId;

  // Order Context (if applicable)
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Order'
  })
  orderId?: Types.ObjectId;

  // Device Information
  @Prop({ 
    trim: true,
    maxlength: 50
  })
  deviceType?: string; // mobile, tablet, desktop

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  browser?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  operatingSystem?: string;

  // Geographic Information
  @Prop({ 
    trim: true,
    maxlength: 100
  })
  country?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  city?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  region?: string;

  // Additional metadata
  @Prop({ 
    type: Object,
    default: {}
  })
  metadata?: Record<string, any>;

  // Event timestamp (for precise timing)
  @Prop({ 
    default: Date.now,
    index: true
  })
  timestamp: Date;
}

export const AnalyticsEventSchema = SchemaFactory.createForClass(AnalyticsEvent);

// Indexes for analytics queries
AnalyticsEventSchema.index({ storeId: 1, eventType: 1 });
AnalyticsEventSchema.index({ storeId: 1, timestamp: -1 });
AnalyticsEventSchema.index({ storeId: 1, eventType: 1, timestamp: -1 });
AnalyticsEventSchema.index({ sessionId: 1, timestamp: 1 });
AnalyticsEventSchema.index({ productId: 1, eventType: 1 });
AnalyticsEventSchema.index({ orderId: 1 });

// Compound indexes for common analytics queries
AnalyticsEventSchema.index({ 
  storeId: 1, 
  eventType: 1, 
  timestamp: -1,
  productId: 1 
});

AnalyticsEventSchema.index({ 
  storeId: 1, 
  timestamp: -1,
  utmSource: 1 
});

// TTL index to automatically delete old events
AnalyticsEventSchema.index({ createdAt: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

// Pre-save middleware
AnalyticsEventSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Ensure timestamp is set
  if (!this.timestamp) {
    this.timestamp = new Date();
  }
  
  next();
});

// Static methods for analytics aggregation
AnalyticsEventSchema.statics.getStoreAnalytics = async function(
  storeId: Types.ObjectId, 
  startDate: Date, 
  endDate: Date
) {
  return this.aggregate([
    {
      $match: {
        storeId: storeId,
        timestamp: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: '$eventType',
        count: { $sum: 1 },
        uniqueSessions: { $addToSet: '$sessionId' }
      }
    },
    {
      $project: {
        eventType: '$_id',
        count: 1,
        uniqueVisitors: { $size: '$uniqueSessions' },
        _id: 0
      }
    }
  ]);
};

AnalyticsEventSchema.statics.getTopProducts = async function(
  storeId: Types.ObjectId, 
  eventType: string,
  startDate: Date, 
  endDate: Date,
  limit: number = 10
) {
  return this.aggregate([
    {
      $match: {
        storeId: storeId,
        eventType: eventType,
        productId: { $exists: true },
        timestamp: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: '$productId',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: limit
    },
    {
      $lookup: {
        from: 'products',
        localField: '_id',
        foreignField: '_id',
        as: 'product'
      }
    },
    {
      $unwind: '$product'
    },
    {
      $project: {
        productId: '$_id',
        productName: '$product.name',
        count: 1,
        _id: 0
      }
    }
  ]);
};

AnalyticsEventSchema.statics.getTrafficSources = async function(
  storeId: Types.ObjectId, 
  startDate: Date, 
  endDate: Date
) {
  return this.aggregate([
    {
      $match: {
        storeId: storeId,
        eventType: ANALYTICS_EVENTS.STORE_VIEW,
        timestamp: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          source: { $ifNull: ['$utmSource', 'direct'] },
          medium: { $ifNull: ['$utmMedium', 'none'] }
        },
        sessions: { $addToSet: '$sessionId' },
        totalViews: { $sum: 1 }
      }
    },
    {
      $project: {
        source: '$_id.source',
        medium: '$_id.medium',
        uniqueVisitors: { $size: '$sessions' },
        totalViews: 1,
        _id: 0
      }
    },
    {
      $sort: { uniqueVisitors: -1 }
    }
  ]);
};
