import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as twilio from 'twilio';

export interface TwilioWhatsAppMessage {
  From: string;
  To: string;
  Body: string;
  MessageSid: string;
  AccountSid: string;
  NumMedia?: string;
  MediaUrl0?: string;
  MediaContentType0?: string;
}

@Injectable()
export class TwilioWhatsAppService {
  private readonly logger = new Logger(TwilioWhatsAppService.name);
  private readonly client: twilio.Twilio;
  private readonly accountSid: string;
  private readonly authToken: string;
  private readonly whatsappNumber: string;

  constructor(private readonly configService: ConfigService) {
    this.accountSid = this.configService.get('TWILIO_ACCOUNT_SID') || '';
    this.authToken = this.configService.get('TWILIO_AUTH_TOKEN') || '';
    this.whatsappNumber = this.configService.get('TWILIO_WHATSAPP_NUMBER') || 'whatsapp:+***********';

    // Initialize client lazily to avoid startup errors
    this.logger.log('Twilio WhatsApp service initialized (client will be created on demand)');
  }

  /**
   * Get Twilio client (lazy initialization)
   */
  private getClient(): twilio.Twilio {
    if (!this.client) {
      if (!this.accountSid || !this.authToken) {
        throw new Error('Twilio credentials not configured');
      }

      if (!this.accountSid.startsWith('AC')) {
        throw new Error('Invalid Twilio Account SID format');
      }

      (this as any).client = twilio(this.accountSid, this.authToken);
      this.logger.log('Twilio WhatsApp client initialized successfully');
    }
    return this.client;
  }

  /**
   * Parse incoming Twilio WhatsApp webhook message
   */
  parseWebhookMessage(body: any): TwilioWhatsAppMessage | null {
    try {
      if (body.From && body.Body) {
        return {
          From: body.From,
          To: body.To,
          Body: body.Body,
          MessageSid: body.MessageSid,
          AccountSid: body.AccountSid,
          NumMedia: body.NumMedia,
          MediaUrl0: body.MediaUrl0,
          MediaContentType0: body.MediaContentType0,
        };
      }
      return null;
    } catch (error) {
      this.logger.error('Error parsing Twilio WhatsApp message:', error);
      return null;
    }
  }

  /**
   * Send WhatsApp message via Twilio
   */
  async sendMessage(to: string, message: string): Promise<any> {
    try {
      const client = this.getClient();

      // Ensure the 'to' number has whatsapp: prefix
      const toNumber = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`;

      const result = await client.messages.create({
        from: this.whatsappNumber,
        to: toNumber,
        body: message,
      });

      this.logger.log(`WhatsApp message sent via Twilio to ${toNumber}: ${result.sid}`);
      return result;
    } catch (error) {
      this.logger.error('Error sending Twilio WhatsApp message:', error);
      throw error;
    }
  }

  /**
   * Send WhatsApp message with media
   */
  async sendMessageWithMedia(to: string, message: string, mediaUrl: string): Promise<any> {
    try {
      const client = this.getClient();

      const toNumber = to.startsWith('whatsapp:') ? to : `whatsapp:${to}`;

      const result = await client.messages.create({
        from: this.whatsappNumber,
        to: toNumber,
        body: message,
        mediaUrl: [mediaUrl],
      });

      this.logger.log(`WhatsApp message with media sent via Twilio to ${toNumber}: ${result.sid}`);
      return result;
    } catch (error) {
      this.logger.error('Error sending Twilio WhatsApp message with media:', error);
      throw error;
    }
  }

  /**
   * Validate Twilio webhook signature
   */
  validateWebhook(signature: string, url: string, params: any): boolean {
    try {
      if (!this.authToken) {
        this.logger.warn('Cannot validate webhook: Auth token not configured');
        return true; // Allow for testing
      }

      return twilio.validateRequest(this.authToken, signature, url, params);
    } catch (error) {
      this.logger.error('Error validating Twilio webhook:', error);
      return false;
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(): Promise<any> {
    try {
      const client = this.getClient();

      const account = await client.api.accounts(this.accountSid).fetch();
      return {
        accountSid: account.sid,
        friendlyName: account.friendlyName,
        status: account.status,
        type: account.type,
      };
    } catch (error) {
      this.logger.error('Error getting Twilio account info:', error);
      throw error;
    }
  }

  /**
   * Get WhatsApp sandbox settings
   */
  async getSandboxSettings(): Promise<any> {
    try {
      const client = this.getClient();

      // Get sandbox settings
      const sandbox = await client.conversations.v1.configuration().fetch();
      return sandbox;
    } catch (error) {
      this.logger.error('Error getting sandbox settings:', error);
      throw error;
    }
  }

  /**
   * Extract phone number from WhatsApp format
   */
  extractPhoneNumber(whatsappNumber: string): string {
    return whatsappNumber.replace('whatsapp:', '');
  }

  /**
   * Format phone number for WhatsApp
   */
  formatWhatsAppNumber(phoneNumber: string): string {
    if (phoneNumber.startsWith('whatsapp:')) {
      return phoneNumber;
    }
    return `whatsapp:${phoneNumber}`;
  }

  /**
   * Get configuration status
   */
  getConfiguration() {
    return {
      isConfigured: !!(this.accountSid && this.authToken),
      accountSid: this.accountSid ? `${this.accountSid.substring(0, 8)}...` : 'Not set',
      whatsappNumber: this.whatsappNumber,
      hasClient: !!this.client,
      provider: 'twilio',
      initializationMode: 'lazy',
    };
  }

  /**
   * Test connection to Twilio
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getAccountInfo();
      return true;
    } catch (error) {
      this.logger.error('Twilio connection test failed:', error);
      return false;
    }
  }

  /**
   * Format message for better WhatsApp display
   */
  formatMessage(message: string): string {
    // Add emojis and formatting for better WhatsApp experience
    return message
      .replace(/\*\*(.*?)\*\*/g, '*$1*') // Bold formatting
      .replace(/__(.*?)__/g, '_$1_') // Italic formatting
      .replace(/```(.*?)```/g, '```$1```') // Code formatting
      .trim();
  }

  /**
   * Create quick reply buttons (for future Twilio features)
   */
  createQuickReplies(options: string[]): string {
    if (options.length === 0) return '';
    
    return '\n\n' + options.map((option, index) => 
      `${index + 1}. ${option}`
    ).join('\n');
  }
}
