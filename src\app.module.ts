import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerModule } from '@nestjs/throttler';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

// Configuration
import { databaseConfig } from '@config/database.config';
import { authConfig } from '@config/auth.config';
import { throttlerConfig } from '@config/throttler.config';

// Core Modules
import { AuthModule } from '@modules/auth/auth.module';
import { UsersModule } from '@modules/users/users.module';
import { StoresModule } from '@modules/stores/stores.module';
import { ProductsModule } from '@modules/products/products.module';
import { CategoriesModule } from '@modules/categories/categories.module';
import { OrdersModule } from '@modules/orders/orders.module';
import { CustomersModule } from '@modules/customers/customers.module';
import { ReviewsModule } from '@modules/reviews/reviews.module';
import { AnalyticsModule } from '@modules/analytics/analytics.module';
import { WhatsappModule } from '@modules/whatsapp/whatsapp.module';
import { PaymentsModule } from '@modules/payments/payments.module';
import { UploadsModule } from '@modules/uploads/uploads.module';
import { PublicModule } from '@modules/public/public.module';
import { PingBotModule } from '@modules/pingbot/pingbot.module';

// Common
import { HealthModule } from '@common/health/health.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, authConfig, throttlerConfig],
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // Database
    MongooseModule.forRootAsync({
      useFactory: () => ({
        uri: process.env.MONGODB_URI,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferCommands: false,
      }),
    }),

    // Rate Limiting
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        throttlers: [{
          ttl: (parseInt(process.env.RATE_LIMIT_TTL) || 60) * 1000,
          limit: parseInt(process.env.RATE_LIMIT_MAX) || 100,
        }],
      }),
    }),

    // Static file serving
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/public',
    }),

    // Core Business Modules
    AuthModule,
    UsersModule,
    StoresModule,
    ProductsModule,
    CategoriesModule,
    OrdersModule,
    CustomersModule,
    ReviewsModule,
    AnalyticsModule,
    WhatsappModule,
    PaymentsModule,
    UploadsModule,

    // AI Modules
    PingBotModule,

    // Public APIs (No Auth Required)
    PublicModule,

    // Health Check
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
