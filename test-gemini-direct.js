const axios = require('axios');

async function testGeminiDirect() {
  console.log('🤖 Testing Gemini AI directly...');
  
  try {
    // Test 1: Simple Gemini API call
    console.log('\n1️⃣ Testing Gemini API directly...');
    const geminiResponse = await axios.post(
      'https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=AIzaSyDO-ow5P4OPg2uGA_VSxFIOiY4BouXKOC0',
      {
        contents: [{
          parts: [{
            text: 'Say hello in one word'
          }]
        }]
      },
      { timeout: 5000 }
    );
    
    console.log('✅ Gemini API Response:', geminiResponse.data.candidates[0].content.parts[0].text);
    
    // Test 2: Complex query that should trigger Gemini
    console.log('\n2️⃣ Testing complex query through webhook...');
    const webhookResponse = await axios.post('http://localhost:3000/api/pingbot/telegram/webhook', {
      update_id: 999999999,
      message: {
        message_id: 999,
        from: { id: 7520184516, first_name: 'Test' },
        chat: { id: 7520184516, type: 'private' },
        text: 'Tell me about all products you have in your store',
        date: Math.floor(Date.now() / 1000)
      }
    }, { timeout: 25000 });
    
    console.log('✅ Webhook Response Status:', webhookResponse.status);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.code === 'ECONNABORTED') {
      console.log('⏰ TIMEOUT - Gemini is taking too long!');
    }
  }
}

testGeminiDirect();
