import {
  IsString,
  IsEmail,
  IsOptional,
  IsArray,
  IsNumber,
  IsEnum,
  ValidateNested,
  Min<PERSON><PERSON>th,
  <PERSON><PERSON>ength,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  IsMongoId,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { PAYMENT_METHODS } from '@common/constants';

class OrderItemDto {
  @ApiProperty({
    description: 'Product ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId({ message: 'Please provide a valid product ID' })
  productId: string;

  @ApiPropertyOptional({
    description: 'Product variant ID',
    example: '507f1f77bcf86cd799439012',
  })
  @IsOptional()
  @IsMongoId({ message: 'Please provide a valid variant ID' })
  variantId?: string;

  @ApiProperty({
    description: 'Quantity',
    example: 2,
    minimum: 1,
    maximum: 1000,
  })
  @IsNumber()
  @Type(() => Number)
  @Min(1, { message: 'Quantity must be at least 1' })
  @Max(1000, { message: 'Quantity cannot exceed 1000' })
  quantity: number;
}

class CustomerDto {
  @ApiProperty({
    description: 'Customer name',
    example: 'John Doe',
    minLength: 2,
    maxLength: 100,
  })
  @IsString()
  @MinLength(2, { message: 'Customer name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Customer name must not exceed 100 characters' })
  name: string;

  @ApiProperty({
    description: 'Customer phone number',
    example: '+919876543210',
  })
  @IsString()
  @Matches(/^\+?[1-9]\d{1,14}$/, {
    message: 'Please enter a valid phone number',
  })
  phone: string;

  @ApiPropertyOptional({
    description: 'Customer email',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail({}, { message: 'Please enter a valid email address' })
  email?: string;
}

class ShippingAddressDto {
  @ApiProperty({
    description: 'Full address',
    example: '123 Main Street, Apartment 4B',
    maxLength: 500,
  })
  @IsString()
  @MaxLength(500, { message: 'Address must not exceed 500 characters' })
  address: string;

  @ApiProperty({
    description: 'City',
    example: 'Mumbai',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100, { message: 'City must not exceed 100 characters' })
  city: string;

  @ApiPropertyOptional({
    description: 'State',
    example: 'Maharashtra',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'State must not exceed 100 characters' })
  state?: string;

  @ApiProperty({
    description: 'Pincode',
    example: '400001',
    maxLength: 20,
  })
  @IsString()
  @MaxLength(20, { message: 'Pincode must not exceed 20 characters' })
  pincode: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'India',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Country must not exceed 100 characters' })
  country?: string;
}

export class CreateOrderDto {
  @ApiProperty({
    description: 'Store handle',
    example: 'my-store',
  })
  @IsString()
  storeHandle: string;

  @ApiProperty({
    description: 'Session ID for cart tracking',
    example: 'sess_1234567890',
  })
  @IsString()
  sessionId: string;

  @ApiProperty({
    description: 'Customer information',
    type: CustomerDto,
  })
  @ValidateNested()
  @Type(() => CustomerDto)
  customer: CustomerDto;

  @ApiProperty({
    description: 'Shipping address',
    type: ShippingAddressDto,
  })
  @ValidateNested()
  @Type(() => ShippingAddressDto)
  shippingAddress: ShippingAddressDto;

  @ApiProperty({
    description: 'Order items',
    type: [OrderItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Payment method',
    example: 'upi',
    enum: Object.values(PAYMENT_METHODS),
  })
  @IsEnum(PAYMENT_METHODS, {
    message: 'Please select a valid payment method',
  })
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Customer notes',
    example: 'Please deliver after 6 PM',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Customer notes must not exceed 1000 characters' })
  customerNotes?: string;
}
