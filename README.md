# 🚀 PingStore Backend

A comprehensive store-in-bio platform backend built with NestJS and MongoDB, featuring WhatsApp integration and AI-powered customer support.

## 🏗️ Architecture Overview

PingStore is a complete e-commerce ecosystem consisting of:

- **PingStore Backend** (This project) - Main API using NestJS + MongoDB
- **PingBot AI Agent** (Separate project) - WhatsApp AI bot for customer support  
- **Frontend** (Next.js) - Store creation dashboard + public storefronts

### Customer Journey
```
Instagram Bio → pingstore.com/storename → Browse Products → Add to Cart → 
Checkout → Payment → Order Placed → WhatsApp Message → AI Support via PingBot
```

## 📁 Project Structure

```
pingstore-backend/
├── src/
│   ├── modules/                 # Business logic modules
│   │   ├── auth/               # Authentication & authorization
│   │   ├── users/              # User management
│   │   ├── stores/             # Store management
│   │   ├── products/           # Product management
│   │   ├── orders/             # Order management
│   │   ├── customers/          # Customer management
│   │   ├── reviews/            # Review system
│   │   ├── analytics/          # Analytics & reporting
│   │   ├── payments/           # Payment processing
│   │   ├── whatsapp/           # WhatsApp integration
│   │   ├── uploads/            # File upload handling
│   │   └── public/             # Public APIs (no auth)
│   ├── common/                 # Shared utilities
│   │   ├── decorators/         # Custom decorators
│   │   ├── guards/             # Auth guards
│   │   ├── interceptors/       # Request/response interceptors
│   │   ├── pipes/              # Validation pipes
│   │   ├── filters/            # Exception filters
│   │   ├── middleware/         # Custom middleware
│   │   ├── dto/                # Data transfer objects
│   │   ├── interfaces/         # TypeScript interfaces
│   │   ├── constants/          # Application constants
│   │   └── utils/              # Utility functions
│   ├── config/                 # Configuration files
│   ├── database/               # Database related files
│   │   ├── schemas/            # Mongoose schemas
│   │   ├── seeders/            # Database seeders
│   │   └── migrations/         # Database migrations
│   ├── utils/                  # Utility functions
│   ├── app.module.ts           # Main application module
│   └── main.ts                 # Application entry point
├── test/                       # Test files
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── docs/                       # Documentation
├── scripts/                    # Build and deployment scripts
└── logs/                       # Application logs
```

## 🛠️ Tech Stack

- **Framework:** NestJS
- **Database:** MongoDB with Mongoose
- **Authentication:** JWT + Google OAuth
- **File Storage:** AWS S3 / Cloudinary
- **Payments:** Razorpay + Stripe
- **Caching:** Redis
- **Logging:** Winston
- **Documentation:** Swagger/OpenAPI
- **Testing:** Jest
- **Validation:** class-validator
- **Security:** Helmet, CORS, Rate limiting

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- MongoDB (v5 or higher)
- Redis (v6 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pingstore-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB and Redis**
   ```bash
   # MongoDB
   mongod
   
   # Redis
   redis-server
   ```

5. **Run the application**
   ```bash
   # Development
   npm run start:dev
   
   # Production
   npm run build
   npm run start:prod
   ```

### Environment Variables

Key environment variables to configure:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/pingstore

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Payment Gateways
RAZORPAY_KEY_ID=your-razorpay-key
STRIPE_SECRET_KEY=your-stripe-secret

# File Storage
AWS_ACCESS_KEY_ID=your-aws-key
CLOUDINARY_URL=your-cloudinary-url

# WhatsApp/PingBot
PINGBOT_API_URL=https://api.pingbot.com
PINGBOT_API_KEY=your-pingbot-key
```

## 📚 API Documentation

Once the application is running, visit:
- **Swagger UI:** http://localhost:3000/api/docs
- **Health Check:** http://localhost:3000/api/health

## 🔐 Security Features

- **JWT Authentication** with refresh tokens
- **Rate limiting** on all endpoints
- **Input validation** using class-validator
- **CORS protection** with configurable origins
- **Helmet** for security headers
- **Password hashing** with bcrypt
- **Request logging** and monitoring

## 🧪 Testing

```bash
# Unit tests
npm run test

# Integration tests
npm run test:e2e

# Test coverage
npm run test:cov

# Watch mode
npm run test:watch
```

## 📊 Monitoring & Logging

- **Winston** for structured logging
- **Health check** endpoint for monitoring
- **Request/response** logging
- **Error tracking** with stack traces
- **Performance metrics** collection

## 🚀 Deployment

### Production Checklist

- [ ] Set `NODE_ENV=production`
- [ ] Configure production database
- [ ] Set up Redis for caching
- [ ] Configure file storage (S3/Cloudinary)
- [ ] Set up payment gateways
- [ ] Configure WhatsApp/PingBot integration
- [ ] Set up monitoring and logging
- [ ] Configure SSL certificates
- [ ] Set up CI/CD pipeline

### Docker Deployment

```bash
# Build image
docker build -t pingstore-backend .

# Run container
docker run -p 3000:3000 --env-file .env pingstore-backend
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**Built with ❤️ by the PingStore Team**
