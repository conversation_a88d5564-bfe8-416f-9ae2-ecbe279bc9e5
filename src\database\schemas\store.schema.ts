import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';
import { AVAILABLE_THEMES } from '@common/constants';

export type StoreDocument = Store & Document;

@Schema({
  timestamps: true,
  collection: 'stores',
})
export class Store extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'User', 
    required: true,
    index: true
  })
  userId: Types.ObjectId;

  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  name: string;

  @Prop({ 
    required: true, 
    unique: true, 
    lowercase: true,
    trim: true,
    match: [/^[a-z0-9-]+$/, 'Handle can only contain lowercase letters, numbers, and hyphens'],
    minlength: 3,
    maxlength: 50
  })
  handle: string;

  @Prop({ trim: true, maxlength: 500 })
  description?: string;

  @Prop({ trim: true, maxlength: 200 })
  bio?: string;

  @Prop({ trim: true })
  logoUrl?: string;

  @Prop({ trim: true })
  coverImageUrl?: string;

  // Contact Info
  @Prop({ 
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid WhatsApp number']
  })
  whatsappNumber?: string;

  @Prop({ default: false })
  whatsappVerified: boolean;

  @Prop({ 
    trim: true,
    match: [/^[a-zA-Z0-9._]+$/, 'Please enter a valid Instagram handle']
  })
  instagramHandle?: string;

  @Prop({ 
    trim: true,
    match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
  })
  websiteUrl?: string;

  @Prop({ trim: true, maxlength: 200 })
  location?: string;

  // Store Settings
  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  isPublished: boolean;

  @Prop({ default: true })
  allowReviews: boolean;

  @Prop({ default: false })
  codEnabled: boolean;

  @Prop({ 
    type: Number, 
    default: 0, 
    min: 0,
    max: 100000
  })
  minOrderAmount: number;

  @Prop({ 
    type: Number, 
    default: 0, 
    min: 0,
    max: 10000
  })
  shippingFee: number;

  @Prop({ 
    type: Number, 
    min: 0,
    max: 100000
  })
  freeShippingAbove?: number;

  // Theme & Design
  @Prop({ 
    default: AVAILABLE_THEMES.MODERN_MINIMAL,
    enum: Object.values(AVAILABLE_THEMES)
  })
  currentTheme: string;

  @Prop({ 
    type: Object,
    default: {}
  })
  themeConfig: Record<string, any>;

  // Onboarding
  @Prop({ default: false })
  onboardingCompleted: boolean;

  @Prop({ 
    type: Object,
    default: {
      storeInfo: false,
      logo: false,
      products: false,
      whatsapp: false,
      payments: false,
      publish: false
    }
  })
  onboardingSteps: Record<string, boolean>;

  // Analytics
  @Prop({ type: Number, default: 0, min: 0 })
  totalViews: number;

  @Prop({ type: Number, default: 0, min: 0 })
  totalOrders: number;

  @Prop({ 
    type: Number, 
    default: 0, 
    min: 0,
    max: 10000000
  })
  totalRevenue: number;

  // Virtual fields
  products?: any[];
  categories?: any[];
  orders?: any[];
}

export const StoreSchema = SchemaFactory.createForClass(Store);

// Indexes
StoreSchema.index({ userId: 1 });
StoreSchema.index({ handle: 1 });
StoreSchema.index({ isPublished: 1, isActive: 1 });
StoreSchema.index({ createdAt: -1 });
StoreSchema.index({ totalRevenue: -1 });
StoreSchema.index({ totalOrders: -1 });

// Text search index
StoreSchema.index({ 
  name: 'text', 
  description: 'text', 
  bio: 'text' 
});

// Virtual populate
StoreSchema.virtual('products', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'storeId',
});

StoreSchema.virtual('categories', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'storeId',
});

StoreSchema.virtual('orders', {
  ref: 'Order',
  localField: '_id',
  foreignField: 'storeId',
});

// Pre-save middleware
StoreSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Ensure handle is lowercase and valid
  if (this.isModified('handle')) {
    this.handle = this.handle.toLowerCase().replace(/[^a-z0-9-]/g, '');
  }
  
  next();
});

// Instance methods
StoreSchema.methods.getPublicData = function() {
  return {
    id: this._id,
    name: this.name,
    handle: this.handle,
    description: this.description,
    bio: this.bio,
    logoUrl: this.logoUrl,
    coverImageUrl: this.coverImageUrl,
    instagramHandle: this.instagramHandle,
    websiteUrl: this.websiteUrl,
    location: this.location,
    whatsappNumber: this.whatsappNumber,
    allowReviews: this.allowReviews,
    codEnabled: this.codEnabled,
    minOrderAmount: this.minOrderAmount,
    shippingFee: this.shippingFee,
    freeShippingAbove: this.freeShippingAbove,
    currentTheme: this.currentTheme,
    themeConfig: this.themeConfig,
    totalViews: this.totalViews,
    isActive: this.isActive,
    isPublished: this.isPublished,
  };
};
