import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { CategoriesController } from './categories.controller';
import { CategoriesService } from './categories.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { Category, CategorySchema } from '@database/schemas/category.schema';
import { Product, ProductSchema } from '@database/schemas/product.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Category.name, schema: CategorySchema },
      { name: Product.name, schema: ProductSchema },
      { name: Store.name, schema: StoreSchema },
    ]),
    StoresModule,
  ],
  controllers: [CategoriesController],
  providers: [CategoriesService],
  exports: [CategoriesService],
})
export class CategoriesModule {}
