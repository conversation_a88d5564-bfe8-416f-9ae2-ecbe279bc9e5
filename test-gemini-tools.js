const axios = require('axios');

async function testGeminiWithTools() {
  console.log('🧪 Testing Gemini Flash with Tools...');
  
  try {
    // Test complex query that should trigger Gemini + tools
    console.log('\n1️⃣ Testing: "Tell me about all products you have"');
    const response1 = await axios.post('http://localhost:3000/api/pingbot/telegram/webhook', {
      update_id: 999999999,
      message: {
        message_id: 999,
        from: { id: 7520184516, first_name: 'Test' },
        chat: { id: 7520184516, type: 'private' },
        text: 'Tell me about all products you have',
        date: Math.floor(Date.now() / 1000)
      }
    }, { timeout: 20000 });
    
    console.log('✅ Response 1 Status:', response1.status);
    
    // Test order number (should use template)
    console.log('\n2️⃣ Testing: "TG-2024-001" (should be template)');
    const response2 = await axios.post('http://localhost:3000/api/pingbot/telegram/webhook', {
      update_id: 999999998,
      message: {
        message_id: 998,
        from: { id: 7520184516, first_name: 'Test' },
        chat: { id: 7520184516, type: 'private' },
        text: 'TG-2024-001',
        date: Math.floor(Date.now() / 1000)
      }
    }, { timeout: 10000 });
    
    console.log('✅ Response 2 Status:', response2.status);
    
    // Test simple status (should use template)
    console.log('\n3️⃣ Testing: "status" (should be template)');
    const response3 = await axios.post('http://localhost:3000/api/pingbot/telegram/webhook', {
      update_id: 999999997,
      message: {
        message_id: 997,
        from: { id: 7520184516, first_name: 'Test' },
        chat: { id: 7520184516, type: 'private' },
        text: 'status',
        date: Math.floor(Date.now() / 1000)
      }
    }, { timeout: 10000 });
    
    console.log('✅ Response 3 Status:', response3.status);
    
    console.log('\n🎯 All tests completed! Check the logs for actual responses.');
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.code === 'ECONNABORTED') {
      console.log('⏰ TIMEOUT - Gemini is still too slow!');
    }
  }
}

testGeminiWithTools();
