import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

export interface CustomerStoreMapping {
  customerPhone: string;
  storeId: string;
  storeName: string;
  source: 'order_id' | 'order_history' | 'manual_selection' | 'conversation_history';
  lastInteraction: Date;
  interactionCount: number;
  metadata?: {
    orderId?: string;
    platform?: string;
    userId?: string;
  };
}

export interface CustomerSelectionState {
  customerPhone: string;
  awaitingSelection: boolean;
  stores: any[];
  timestamp: Date;
  expiresAt: Date;
}

@Injectable()
export class CustomerStoreMappingService {
  private readonly logger = new Logger(CustomerStoreMappingService.name);
  
  // In-memory cache for fast access (in production, use Redis)
  private customerMappings = new Map<string, CustomerStoreMapping>();
  private selectionStates = new Map<string, CustomerSelectionState>();
  
  // Cache TTL (24 hours for mappings, 10 minutes for selection states)
  private readonly MAPPING_TTL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly SELECTION_TTL = 10 * 60 * 1000; // 10 minutes

  constructor() {
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanupExpiredEntries(), 5 * 60 * 1000);
  }

  /**
   * Get customer's current store mapping
   */
  async getCustomerStoreMapping(customerPhone: string): Promise<CustomerStoreMapping | null> {
    const mapping = this.customerMappings.get(customerPhone);
    
    if (!mapping) {
      return null;
    }

    // Check if mapping is still valid (24 hours)
    const now = new Date();
    const timeDiff = now.getTime() - mapping.lastInteraction.getTime();
    
    if (timeDiff > this.MAPPING_TTL) {
      this.customerMappings.delete(customerPhone);
      this.logger.log(`Expired mapping removed for ${customerPhone}`);
      return null;
    }

    return mapping;
  }

  /**
   * Save customer store mapping
   */
  async saveCustomerStoreMapping(
    customerPhone: string, 
    storeId: string, 
    storeName: string,
    source: CustomerStoreMapping['source'],
    metadata?: CustomerStoreMapping['metadata']
  ): Promise<void> {
    const existing = this.customerMappings.get(customerPhone);
    
    const mapping: CustomerStoreMapping = {
      customerPhone,
      storeId,
      storeName,
      source,
      lastInteraction: new Date(),
      interactionCount: existing ? existing.interactionCount + 1 : 1,
      metadata: metadata || {}
    };

    this.customerMappings.set(customerPhone, mapping);
    
    this.logger.log(
      `Saved customer mapping: ${customerPhone} → ${storeName} (${source}) - Count: ${mapping.interactionCount}`
    );
  }

  /**
   * Clear customer store mapping (when switching stores)
   */
  async clearCustomerStoreMapping(customerPhone: string): Promise<void> {
    const deleted = this.customerMappings.delete(customerPhone);
    if (deleted) {
      this.logger.log(`Cleared store mapping for ${customerPhone}`);
    }
  }

  /**
   * Get customer selection state
   */
  async getCustomerSelectionState(customerPhone: string): Promise<CustomerSelectionState | null> {
    const state = this.selectionStates.get(customerPhone);
    
    if (!state) {
      return null;
    }

    // Check if state is expired
    if (new Date() > state.expiresAt) {
      this.selectionStates.delete(customerPhone);
      this.logger.log(`Expired selection state removed for ${customerPhone}`);
      return null;
    }

    return state;
  }

  /**
   * Save customer selection state
   */
  async saveCustomerSelectionState(customerPhone: string, stores: any[]): Promise<void> {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.SELECTION_TTL);
    
    const state: CustomerSelectionState = {
      customerPhone,
      awaitingSelection: true,
      stores,
      timestamp: now,
      expiresAt
    };

    this.selectionStates.set(customerPhone, state);
    
    this.logger.log(`Saved selection state for ${customerPhone} with ${stores.length} stores`);
  }

  /**
   * Clear customer selection state
   */
  async clearCustomerSelectionState(customerPhone: string): Promise<void> {
    const deleted = this.selectionStates.delete(customerPhone);
    if (deleted) {
      this.logger.log(`Cleared selection state for ${customerPhone}`);
    }
  }

  /**
   * Get customer interaction statistics
   */
  async getCustomerStats(customerPhone: string): Promise<{
    hasMapping: boolean;
    interactionCount: number;
    lastInteraction?: Date;
    currentStore?: string;
  }> {
    const mapping = await this.getCustomerStoreMapping(customerPhone);
    
    return {
      hasMapping: !!mapping,
      interactionCount: mapping?.interactionCount || 0,
      lastInteraction: mapping?.lastInteraction,
      currentStore: mapping?.storeName
    };
  }

  /**
   * Get all active customer mappings (for admin/analytics)
   */
  async getActiveMappings(): Promise<CustomerStoreMapping[]> {
    return Array.from(this.customerMappings.values());
  }

  /**
   * Get store-wise customer count
   */
  async getStoreCustomerCounts(): Promise<Record<string, number>> {
    const counts: Record<string, number> = {};
    
    for (const mapping of this.customerMappings.values()) {
      counts[mapping.storeId] = (counts[mapping.storeId] || 0) + 1;
    }
    
    return counts;
  }

  /**
   * Clean up expired entries
   */
  private cleanupExpiredEntries(): void {
    const now = new Date();
    let mappingsRemoved = 0;
    let statesRemoved = 0;

    // Clean expired mappings
    for (const [phone, mapping] of this.customerMappings.entries()) {
      const timeDiff = now.getTime() - mapping.lastInteraction.getTime();
      if (timeDiff > this.MAPPING_TTL) {
        this.customerMappings.delete(phone);
        mappingsRemoved++;
      }
    }

    // Clean expired selection states
    for (const [phone, state] of this.selectionStates.entries()) {
      if (now > state.expiresAt) {
        this.selectionStates.delete(phone);
        statesRemoved++;
      }
    }

    if (mappingsRemoved > 0 || statesRemoved > 0) {
      this.logger.log(`Cleanup: Removed ${mappingsRemoved} mappings, ${statesRemoved} selection states`);
    }
  }

  /**
   * Force refresh customer mapping (extend TTL)
   */
  async refreshCustomerMapping(customerPhone: string): Promise<boolean> {
    const mapping = this.customerMappings.get(customerPhone);
    if (mapping) {
      mapping.lastInteraction = new Date();
      mapping.interactionCount++;
      this.customerMappings.set(customerPhone, mapping);
      return true;
    }
    return false;
  }

  /**
   * Check if customer needs store selection
   */
  async needsStoreSelection(customerPhone: string): Promise<boolean> {
    const mapping = await this.getCustomerStoreMapping(customerPhone);
    const selectionState = await this.getCustomerSelectionState(customerPhone);
    
    return !mapping && !selectionState;
  }
}
