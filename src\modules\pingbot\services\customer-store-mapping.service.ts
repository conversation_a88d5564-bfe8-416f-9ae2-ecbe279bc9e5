import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CustomerStoreMappingDocument } from '../schemas/customer-store-mapping.schema';

export interface CustomerStoreMapping {
  customerPhone: string;
  storeId: string;
  storeName: string;
  source: 'order_id' | 'order_history' | 'manual_selection' | 'conversation_history';
  lastInteraction: Date;
  interactionCount: number;
  metadata?: {
    orderId?: string;
    platform?: string;
    userId?: string;
  };
}

export interface CustomerSelectionState {
  customerPhone: string;
  awaitingSelection: boolean;
  stores: any[];
  timestamp: Date;
  expiresAt: Date;
}

@Injectable()
export class CustomerStoreMappingService {
  private readonly logger = new Logger(CustomerStoreMappingService.name);

  // In-memory cache for selection states only (mappings are now in DB)
  private selectionStates = new Map<string, CustomerSelectionState>();

  // Cache TTL (10 minutes for selection states)
  private readonly SELECTION_TTL = 10 * 60 * 1000; // 10 minutes

  constructor(
    @InjectModel('CustomerStoreMapping')
    private customerMappingModel: Model<CustomerStoreMappingDocument>
  ) {
    // Clean up expired selection states every 5 minutes
    setInterval(() => this.cleanupExpiredSelectionStates(), 5 * 60 * 1000);
  }

  /**
   * Get customer's current store mapping
   */
  async getCustomerStoreMapping(customerPhone: string): Promise<CustomerStoreMapping | null> {
    this.logger.log(`🔍 Looking for customer mapping in DB: ${customerPhone}`);

    try {
      const mapping = await this.customerMappingModel
        .findOne({ customerPhone })
        .sort({ lastInteraction: -1 })
        .exec();

      if (!mapping) {
        this.logger.log(`❌ No mapping found in DB for ${customerPhone}`);
        return null;
      }

      // Check if mapping is still valid (MongoDB TTL handles expiration, but double-check)
      const now = new Date();
      const timeDiff = now.getTime() - mapping.lastInteraction.getTime();
      const ttlHours = 24;

      this.logger.log(`⏰ Mapping age: ${Math.round(timeDiff / 1000 / 60)} minutes (TTL: ${ttlHours * 60} minutes)`);

      if (timeDiff > ttlHours * 60 * 60 * 1000) {
        this.logger.log(`⏰ Expired mapping found, removing: ${customerPhone}`);
        await this.customerMappingModel.deleteOne({ _id: mapping._id });
        return null;
      }

      this.logger.log(`✅ Found valid mapping in DB: ${customerPhone} → ${mapping.storeName} (${mapping.source})`);

      // Convert to interface format
      return {
        customerPhone: mapping.customerPhone,
        storeId: mapping.storeId,
        storeName: mapping.storeName,
        source: mapping.source as CustomerStoreMapping['source'],
        lastInteraction: mapping.lastInteraction,
        interactionCount: mapping.interactionCount,
        metadata: mapping.metadata
      };
    } catch (error) {
      this.logger.error(`Error getting customer mapping for ${customerPhone}:`, error);
      return null;
    }
  }

  /**
   * Save customer store mapping
   */
  async saveCustomerStoreMapping(
    customerPhone: string,
    storeId: string,
    storeName: string,
    source: CustomerStoreMapping['source'],
    metadata?: CustomerStoreMapping['metadata']
  ): Promise<void> {
    try {
      // Find existing mapping
      const existing = await this.customerMappingModel
        .findOne({ customerPhone })
        .exec();

      const interactionCount = existing ? existing.interactionCount + 1 : 1;

      // Upsert the mapping
      await this.customerMappingModel.findOneAndUpdate(
        { customerPhone },
        {
          customerPhone,
          storeId,
          storeName,
          source,
          lastInteraction: new Date(),
          interactionCount,
          metadata: metadata || {},
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );

      this.logger.log(
        `💾 Saved customer mapping to DB: ${customerPhone} → ${storeName} (${source}) - Count: ${interactionCount}`
      );
    } catch (error) {
      this.logger.error(`Error saving customer mapping for ${customerPhone}:`, error);
      throw error;
    }
  }

  /**
   * Clear customer store mapping (when switching stores)
   */
  async clearCustomerStoreMapping(customerPhone: string): Promise<void> {
    try {
      const result = await this.customerMappingModel.deleteOne({ customerPhone });
      if (result.deletedCount > 0) {
        this.logger.log(`🗑️ Cleared store mapping from DB for ${customerPhone}`);
      }
    } catch (error) {
      this.logger.error(`Error clearing customer mapping for ${customerPhone}:`, error);
    }
  }

  /**
   * Get customer selection state
   */
  async getCustomerSelectionState(customerPhone: string): Promise<CustomerSelectionState | null> {
    const state = this.selectionStates.get(customerPhone);
    
    if (!state) {
      return null;
    }

    // Check if state is expired
    if (new Date() > state.expiresAt) {
      this.selectionStates.delete(customerPhone);
      this.logger.log(`Expired selection state removed for ${customerPhone}`);
      return null;
    }

    return state;
  }

  /**
   * Save customer selection state
   */
  async saveCustomerSelectionState(customerPhone: string, stores: any[]): Promise<void> {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.SELECTION_TTL);
    
    const state: CustomerSelectionState = {
      customerPhone,
      awaitingSelection: true,
      stores,
      timestamp: now,
      expiresAt
    };

    this.selectionStates.set(customerPhone, state);
    
    this.logger.log(`Saved selection state for ${customerPhone} with ${stores.length} stores`);
  }

  /**
   * Clear customer selection state
   */
  async clearCustomerSelectionState(customerPhone: string): Promise<void> {
    const deleted = this.selectionStates.delete(customerPhone);
    if (deleted) {
      this.logger.log(`Cleared selection state for ${customerPhone}`);
    }
  }

  /**
   * Get customer interaction statistics
   */
  async getCustomerStats(customerPhone: string): Promise<{
    hasMapping: boolean;
    interactionCount: number;
    lastInteraction?: Date;
    currentStore?: string;
  }> {
    const mapping = await this.getCustomerStoreMapping(customerPhone);
    
    return {
      hasMapping: !!mapping,
      interactionCount: mapping?.interactionCount || 0,
      lastInteraction: mapping?.lastInteraction,
      currentStore: mapping?.storeName
    };
  }

  /**
   * Get all active customer mappings (for admin/analytics)
   */
  async getActiveMappings(): Promise<CustomerStoreMapping[]> {
    try {
      const mappings = await this.customerMappingModel.find({}).exec();
      return mappings.map(mapping => ({
        customerPhone: mapping.customerPhone,
        storeId: mapping.storeId,
        storeName: mapping.storeName,
        source: mapping.source as CustomerStoreMapping['source'],
        lastInteraction: mapping.lastInteraction,
        interactionCount: mapping.interactionCount,
        metadata: mapping.metadata
      }));
    } catch (error) {
      this.logger.error('Error getting active mappings:', error);
      return [];
    }
  }

  /**
   * Get store-wise customer count
   */
  async getStoreCustomerCounts(): Promise<Record<string, number>> {
    try {
      const pipeline = [
        { $group: { _id: '$storeId', count: { $sum: 1 } } }
      ];

      const results = await this.customerMappingModel.aggregate(pipeline);
      const counts: Record<string, number> = {};

      for (const result of results) {
        counts[result._id] = result.count;
      }

      return counts;
    } catch (error) {
      this.logger.error('Error getting store customer counts:', error);
      return {};
    }
  }

  /**
   * Clean up expired selection states (mappings are handled by MongoDB TTL)
   */
  private cleanupExpiredSelectionStates(): void {
    const now = new Date();
    let statesRemoved = 0;

    // Clean expired selection states
    for (const [phone, state] of this.selectionStates.entries()) {
      if (now > state.expiresAt) {
        this.selectionStates.delete(phone);
        statesRemoved++;
      }
    }

    if (statesRemoved > 0) {
      this.logger.log(`Cleanup: Removed ${statesRemoved} selection states`);
    }
  }

  /**
   * Force refresh customer mapping (extend TTL)
   */
  async refreshCustomerMapping(customerPhone: string): Promise<boolean> {
    try {
      const result = await this.customerMappingModel.findOneAndUpdate(
        { customerPhone },
        {
          lastInteraction: new Date(),
          $inc: { interactionCount: 1 },
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // Extend TTL
        },
        { new: true }
      );

      if (result) {
        this.logger.log(`🔄 Refreshed mapping for ${customerPhone}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`Error refreshing customer mapping for ${customerPhone}:`, error);
      return false;
    }
  }

  /**
   * Check if customer needs store selection
   */
  async needsStoreSelection(customerPhone: string): Promise<boolean> {
    const mapping = await this.getCustomerStoreMapping(customerPhone);
    const selectionState = await this.getCustomerSelectionState(customerPhone);
    
    return !mapping && !selectionState;
  }
}
