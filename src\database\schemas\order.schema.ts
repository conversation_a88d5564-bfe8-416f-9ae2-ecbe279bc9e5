import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';
import { ORDER_STATUS, PAYMENT_STATUS, PAYMENT_METHODS } from '@common/constants';

export interface OrderMethods {
  generateOrderNumber(): string;
  getPublicData(): any;
  canBeCancelled(): boolean;
  isDelivered(): boolean;
}

export type OrderDocument = Order & Document<any, any, Order> & OrderMethods & {
  _id: Types.ObjectId;
};

@Schema({
  timestamps: true,
  collection: 'orders',
})
export class Order extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  @Prop({ 
    required: true, 
    unique: true,
    trim: true,
    maxlength: 50
  })
  orderNumber: string;

  // Customer Info
  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  customerName: string;

  @Prop({ 
    required: true, 
    trim: true,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please enter a valid phone number']
  })
  customerPhone: string;

  @Prop({ 
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  })
  customerEmail?: string;

  // Shipping Address
  @Prop({ 
    required: true, 
    trim: true,
    maxlength: 500
  })
  shippingAddress: string;

  @Prop({ 
    required: true, 
    trim: true,
    maxlength: 100
  })
  shippingCity: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  shippingState?: string;

  @Prop({ 
    required: true, 
    trim: true,
    maxlength: 20
  })
  shippingPincode: string;

  @Prop({ 
    trim: true,
    default: 'India',
    maxlength: 100
  })
  shippingCountry: string;

  // Order Totals
  @Prop({ 
    required: true, 
    type: Number,
    min: 0,
    max: 10000000
  })
  subtotal: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 10000
  })
  shippingFee: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 1000000
  })
  taxAmount: number;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 1000000
  })
  discountAmount: number;

  @Prop({ 
    required: true, 
    type: Number,
    min: 0,
    max: 10000000
  })
  totalAmount: number;

  // Payment Info
  @Prop({ 
    enum: Object.values(PAYMENT_METHODS)
  })
  paymentMethod?: string;

  @Prop({ 
    default: PAYMENT_STATUS.PENDING,
    enum: Object.values(PAYMENT_STATUS)
  })
  paymentStatus: string;

  @Prop({ 
    trim: true,
    maxlength: 255
  })
  paymentId?: string; // From payment gateway

  // Order Status
  @Prop({ 
    default: ORDER_STATUS.CONFIRMED,
    enum: Object.values(ORDER_STATUS)
  })
  status: string;

  // Tracking
  @Prop({ 
    trim: true,
    maxlength: 100
  })
  trackingNumber?: string;

  @Prop({ 
    trim: true,
    maxlength: 100
  })
  courierPartner?: string;

  @Prop()
  estimatedDelivery?: Date;

  // WhatsApp Integration
  @Prop({ default: false })
  whatsappNotified: boolean;

  @Prop({ 
    trim: true,
    maxlength: 255
  })
  pingbotConversationId?: string;

  // Notes
  @Prop({ 
    trim: true,
    maxlength: 1000
  })
  customerNotes?: string;

  @Prop({ 
    trim: true,
    maxlength: 1000
  })
  adminNotes?: string;

  // Timestamps
  @Prop({ default: Date.now })
  confirmedAt?: Date;

  @Prop()
  shippedAt?: Date;

  @Prop()
  deliveredAt?: Date;

  // Virtual fields
  items?: any[];
  customer?: any;
}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Indexes
OrderSchema.index({ storeId: 1 });
OrderSchema.index({ storeId: 1, status: 1 });
OrderSchema.index({ storeId: 1, paymentStatus: 1 });
OrderSchema.index({ orderNumber: 1 });
OrderSchema.index({ customerPhone: 1 });
OrderSchema.index({ customerEmail: 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ confirmedAt: -1 });
OrderSchema.index({ totalAmount: -1 });
OrderSchema.index({ trackingNumber: 1 });

// Compound indexes for common queries
OrderSchema.index({ storeId: 1, createdAt: -1 });
OrderSchema.index({ storeId: 1, status: 1, createdAt: -1 });
OrderSchema.index({ customerPhone: 1, storeId: 1 });

// Text search index
OrderSchema.index({ 
  orderNumber: 'text',
  customerName: 'text',
  customerPhone: 'text',
  customerEmail: 'text'
});

// Virtual populate
OrderSchema.virtual('items', {
  ref: 'OrderItem',
  localField: '_id',
  foreignField: 'orderId',
});

OrderSchema.virtual('customer', {
  ref: 'Customer',
  localField: 'customerPhone',
  foreignField: 'phone',
  justOne: true,
});

// Pre-save middleware
OrderSchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  
  // Auto-generate order number if not set
  if (this.isNew && !this.orderNumber) {
    this.orderNumber = (this as any).generateOrderNumber();
  }
  
  // Update status timestamps
  if (this.isModified('status')) {
    const now = new Date();
    switch (this.status) {
      case ORDER_STATUS.SHIPPED:
        if (!this.shippedAt) this.shippedAt = now;
        break;
      case ORDER_STATUS.DELIVERED:
        if (!this.deliveredAt) this.deliveredAt = now;
        break;
    }
  }
  
  next();
});

// Instance methods
OrderSchema.methods.generateOrderNumber = function() {
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `ORD-${timestamp}-${random}`;
};

OrderSchema.methods.getPublicData = function() {
  return {
    id: this._id,
    orderNumber: this.orderNumber,
    status: this.status,
    paymentStatus: this.paymentStatus,
    totalAmount: this.totalAmount,
    customerName: this.customerName,
    shippingAddress: this.shippingAddress,
    shippingCity: this.shippingCity,
    shippingState: this.shippingState,
    shippingPincode: this.shippingPincode,
    trackingNumber: this.trackingNumber,
    courierPartner: this.courierPartner,
    estimatedDelivery: this.estimatedDelivery,
    createdAt: this.createdAt,
    confirmedAt: this.confirmedAt,
    shippedAt: this.shippedAt,
    deliveredAt: this.deliveredAt,
  };
};

OrderSchema.methods.canBeCancelled = function() {
  return [ORDER_STATUS.CONFIRMED, ORDER_STATUS.PROCESSING].includes(this.status);
};

OrderSchema.methods.isDelivered = function() {
  return this.status === ORDER_STATUS.DELIVERED;
};
