import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BaseSchema } from './base.schema';

export type CategoryDocument = Category & Document;

@Schema({
  timestamps: true,
  collection: 'categories',
})
export class Category extends BaseSchema {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true
  })
  storeId: Types.ObjectId;

  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  name: string;

  @Prop({ trim: true, maxlength: 500 })
  description?: string;

  @Prop({ trim: true })
  imageUrl?: string;

  @Prop({ 
    type: Number, 
    default: 0,
    min: 0,
    max: 1000
  })
  sortOrder: number;

  @Prop({ default: true })
  isActive: boolean;

  // Virtual for products count
  productCount?: number;
  products?: any[];
}

export const CategorySchema = SchemaFactory.createForClass(Category);

// Indexes
CategorySchema.index({ storeId: 1 });
CategorySchema.index({ storeId: 1, isActive: 1 });
CategorySchema.index({ storeId: 1, sortOrder: 1 });
CategorySchema.index({ createdAt: -1 });

// Text search index
CategorySchema.index({ 
  name: 'text', 
  description: 'text' 
});

// Virtual populate for products
CategorySchema.virtual('products', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'categoryId',
});

// Virtual for product count
CategorySchema.virtual('productCount', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'categoryId',
  count: true,
});

// Pre-save middleware
CategorySchema.pre('save', function(next) {
  if (this.isModified()) {
    this.updatedAt = new Date();
  }
  next();
});

// Compound unique index to prevent duplicate category names per store
CategorySchema.index({ storeId: 1, name: 1 }, { unique: true });
