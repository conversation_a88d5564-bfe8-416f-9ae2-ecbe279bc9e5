import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';

// Import other modules
import { StoresModule } from '@modules/stores/stores.module';

// Schemas
import { AnalyticsEvent, AnalyticsEventSchema } from '@database/schemas/analytics-event.schema';
import { Store, StoreSchema } from '@database/schemas/store.schema';
import { Product, ProductSchema } from '@database/schemas/product.schema';
import { Order, OrderSchema } from '@database/schemas/order.schema';
import { Customer, CustomerSchema } from '@database/schemas/customer.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AnalyticsEvent.name, schema: AnalyticsEventSchema },
      { name: Store.name, schema: StoreSchema },
      { name: Product.name, schema: ProductSchema },
      { name: Order.name, schema: OrderSchema },
      { name: Customer.name, schema: CustomerSchema },
    ]),
    StoresModule,
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
  exports: [AnalyticsService],
})
export class AnalyticsModule {}
