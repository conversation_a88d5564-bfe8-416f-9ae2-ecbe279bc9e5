import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument } from '@database/schemas/user.schema';
import { ERROR_CODES } from '@common/constants';

// DTOs
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async findById(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }
    return user;
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ email });
  }

  async findByPhone(phone: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ phone });
  }

  async findByGoogleId(googleId: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ googleId });
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<UserDocument> {
    const { email, phone, fullName, avatarUrl } = updateProfileDto;

    // Check if email or phone already exists (excluding current user)
    if (email || phone) {
      const existingUser = await this.userModel.findOne({
        _id: { $ne: userId },
        $or: [
          ...(email ? [{ email }] : []),
          ...(phone ? [{ phone }] : []),
        ],
      });

      if (existingUser) {
        if (existingUser.email === email) {
          throw new ConflictException({
            message: 'Email already exists',
            code: ERROR_CODES.DUPLICATE_VALUE,
          });
        }
        if (existingUser.phone === phone) {
          throw new ConflictException({
            message: 'Phone number already exists',
            code: ERROR_CODES.DUPLICATE_VALUE,
          });
        }
      }
    }

    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        ...(email && { email, emailVerified: false }), // Reset verification if email changed
        ...(phone && { phone, phoneVerified: false }), // Reset verification if phone changed
        ...(fullName && { fullName }),
        ...(avatarUrl && { avatarUrl }),
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    this.logger.log(`User profile updated: ${user.email}`);
    return user;
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;

    const user = await this.userModel.findById(userId).select('+passwordHash');
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    // Check current password
    if (user.passwordHash) {
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        throw new ConflictException({
          message: 'Current password is incorrect',
          code: ERROR_CODES.AUTH_INVALID,
        });
      }
    }

    // Hash new password
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await this.userModel.findByIdAndUpdate(userId, {
      passwordHash: newPasswordHash,
      updatedAt: new Date(),
    });

    this.logger.log(`Password changed for user: ${user.email}`);
  }

  async uploadAvatar(userId: string, avatarUrl: string): Promise<UserDocument> {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        avatarUrl,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    this.logger.log(`Avatar uploaded for user: ${user.email}`);
    return user;
  }

  async deleteUser(userId: string): Promise<void> {
    const user = await this.userModel.findByIdAndDelete(userId);
    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    this.logger.log(`User deleted: ${user.email}`);
  }

  async verifyEmail(userId: string): Promise<UserDocument> {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        emailVerified: true,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    this.logger.log(`Email verified for user: ${user.email}`);
    return user;
  }

  async verifyPhone(userId: string): Promise<UserDocument> {
    const user = await this.userModel.findByIdAndUpdate(
      userId,
      {
        phoneVerified: true,
        updatedAt: new Date(),
      },
      { new: true },
    );

    if (!user) {
      throw new NotFoundException({
        message: 'User not found',
        code: ERROR_CODES.USER_NOT_FOUND,
      });
    }

    this.logger.log(`Phone verified for user: ${user.email}`);
    return user;
  }
}
