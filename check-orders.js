const mongoose = require('mongoose');

async function checkOrders() {
  try {
    await mongoose.connect('mongodb+srv://ashik:<EMAIL>/pingstore?retryWrites=true&w=majority&appName=Cluster0');
    
    const orderSchema = new mongoose.Schema({}, { strict: false });
    const Order = mongoose.model('Order', orderSchema);
    
    const orders = await Order.find({}).select('orderNumber storeId customerName status totalAmount createdAt').limit(10);
    
    console.log('📦 ORDERS IN DATABASE:');
    console.log('=' .repeat(50));
    
    orders.forEach((order, index) => {
      console.log(`${index + 1}. Order Number: ${order.orderNumber}`);
      console.log(`   Customer: ${order.customerName}`);
      console.log(`   Status: ${order.status}`);
      console.log(`   Amount: ${order.totalAmount}`);
      console.log(`   Created: ${order.createdAt}`);
      console.log('');
    });
    
    await mongoose.disconnect();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkOrders();
