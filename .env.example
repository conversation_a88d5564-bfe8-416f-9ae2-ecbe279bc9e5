# Application
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3001

# Database
MONGODB_URI=mongodb://localhost:27017/pingstore
MONGODB_TEST_URI=mongodb://localhost:27017/pingstore_test

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/google/callback

# Payment Gateways
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
RAZORPAY_WEBHOOK_SECRET=your-razorpay-webhook-secret

STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# File Storage - AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=pingstore-uploads
AWS_S3_REGION=us-east-1

# File Storage - Cloudinary (Alternative)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# OpenAI Configuration (for PingBot AI)
OPENAI_API_KEY=your-openai-api-key-here

# WhatsApp Business API Configuration (for PingBot)
WHATSAPP_API_BASE_URL=https://graph.facebook.com/v18.0
WHATSAPP_ACCESS_TOKEN=your-whatsapp-access-token
WHATSAPP_PHONE_NUMBER_ID=your-phone-number-id
WHATSAPP_VERIFY_TOKEN=your-whatsapp-verify-token

# Legacy PingBot Integration (if using external service)
PINGBOT_API_URL=https://api.pingbot.com
PINGBOT_API_KEY=your-pingbot-api-key

# Email Service - SendGrid
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=PingStore

# SMS Service - Twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# WhatsApp/PingBot Integration (Centralized Bot)
PINGBOT_API_URL=https://api.pingbot.com
PINGBOT_API_KEY=your-pingbot-api-key
PINGBOT_WEBHOOK_SECRET=your-pingbot-webhook-secret
PINGBOT_CENTRAL_NUMBER=+91-XXXXX-XXXXX

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your-session-secret

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CDN
CDN_BASE_URL=https://cdn.pingstore.com

# Analytics
GOOGLE_ANALYTICS_ID=your-ga-id

# Feature Flags
ENABLE_WHATSAPP_INTEGRATION=true
ENABLE_PAYMENT_GATEWAYS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=true
