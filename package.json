{"name": "pingstore-backend", "version": "1.0.0", "description": "PingStore Backend API - Store-in-bio platform with WhatsApp integration", "author": "PingStore Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "ts-node -r tsconfig-paths/register src/database/seeders/index.ts", "migration:generate": "ts-node -r tsconfig-paths/register src/database/migrations/generate.ts", "migration:run": "ts-node -r tsconfig-paths/register src/database/migrations/run.ts"}, "dependencies": {"@google/generative-ai": "^0.1.3", "@langchain/community": "^0.0.25", "@langchain/google-genai": "^0.0.8", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.2", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.0.1", "aws-sdk": "^2.1506.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cache-manager": "^5.3.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "helmet": "^7.1.0", "ioredis": "^5.3.2", "langchain": "^0.1.25", "lodash": "^4.17.21", "moment": "^2.29.4", "mongodb": "^5.9.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nest-winston": "^1.9.4", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "razorpay": "^2.9.2", "redis": "^4.6.11", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sharp": "^0.33.1", "stripe": "^14.9.0", "twilio": "^4.23.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.202", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapping": {"^@/(.*)$": "<rootDir>/$1", "^@common/(.*)$": "<rootDir>/common/$1", "^@modules/(.*)$": "<rootDir>/modules/$1", "^@config/(.*)$": "<rootDir>/config/$1"}}}