import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiParam,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';

import { ProductsService } from './products.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductQueryDto } from './dto/product-query.dto';

@ApiTags('Products')
@Controller('stores/:storeId/products')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new product' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 201, description: 'Product created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async create(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() createProductDto: CreateProductDto,
  ) {
    const product = await this.productsService.create(storeId, user._id.toString(), createProductDto);
    return {
      message: API_MESSAGES.PRODUCT_CREATED,
      product,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all products in store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Products retrieved successfully' })
  async findAll(
    @Param('storeId') storeId: string,
    @Query() query: ProductQueryDto,
  ) {
    const result = await this.productsService.findAll(storeId, query);
    return {
      message: 'Products retrieved successfully',
      ...result,
    };
  }

  @Get(':productId')
  @ApiOperation({ summary: 'Get product by ID' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async findById(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
  ) {
    const product = await this.productsService.findByIdAndStore(productId, storeId);
    return {
      message: 'Product retrieved successfully',
      product,
    };
  }

  @Put(':productId')
  @ApiOperation({ summary: 'Update product' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product updated successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async update(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @Body() updateProductDto: UpdateProductDto,
  ) {
    const product = await this.productsService.update(
      productId,
      storeId,
      user._id.toString(),
      updateProductDto,
    );
    return {
      message: API_MESSAGES.PRODUCT_UPDATED,
      product,
    };
  }

  @Delete(':productId')
  @ApiOperation({ summary: 'Delete product' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  @ApiResponse({ status: 404, description: 'Product not found' })
  async delete(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
  ) {
    await this.productsService.delete(productId, storeId, user._id.toString());
    return {
      message: API_MESSAGES.PRODUCT_DELETED,
    };
  }

  @Put(':productId/status')
  @ApiOperation({ summary: 'Update product status' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product status updated successfully' })
  async updateStatus(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { status: string },
  ) {
    const product = await this.productsService.updateStatus(
      productId,
      storeId,
      user._id.toString(),
      body.status,
    );
    return {
      message: 'Product status updated successfully',
      product,
    };
  }

  @Put(':productId/featured')
  @ApiOperation({ summary: 'Toggle product featured status' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product featured status updated' })
  async toggleFeatured(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { isFeatured: boolean },
  ) {
    const product = await this.productsService.toggleFeatured(
      productId,
      storeId,
      user._id.toString(),
      body.isFeatured,
    );
    return {
      message: 'Product featured status updated',
      product,
    };
  }

  // Image Management
  @Post(':productId/images')
  @UseInterceptors(FilesInterceptor('images', 10))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload product images' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Images uploaded successfully' })
  async uploadImages(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    // In a real implementation, upload to S3/Cloudinary
    const imageUrls = files.map((file, index) =>
      `https://example.com/products/${productId}/image-${index}.jpg`
    );

    const images = await this.productsService.addImages(
      productId,
      storeId,
      user._id.toString(),
      imageUrls,
    );
    return {
      message: API_MESSAGES.FILES_UPLOADED,
      images,
    };
  }

  @Delete(':productId/images/:imageId')
  @ApiOperation({ summary: 'Delete product image' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID' })
  @ApiResponse({ status: 200, description: 'Image deleted successfully' })
  async deleteImage(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @Param('imageId') imageId: string,
    @CurrentUser() user: UserDocument,
  ) {
    await this.productsService.deleteImage(imageId, storeId, user._id.toString());
    return {
      message: API_MESSAGES.FILE_DELETED,
    };
  }

  @Put(':productId/images/:imageId/main')
  @ApiOperation({ summary: 'Set image as main product image' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID' })
  @ApiResponse({ status: 200, description: 'Main image set successfully' })
  async setMainImage(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @Param('imageId') imageId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const image = await this.productsService.setMainImage(imageId, storeId, user._id.toString());
    return {
      message: 'Main image set successfully',
      image,
    };
  }

  @Put(':productId/images/reorder')
  @ApiOperation({ summary: 'Reorder product images' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Images reordered successfully' })
  async reorderImages(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { imageIds: string[] },
  ) {
    const images = await this.productsService.reorderImages(
      productId,
      storeId,
      user._id.toString(),
      body.imageIds,
    );
    return {
      message: 'Images reordered successfully',
      images,
    };
  }

  // Variant Management
  @Post(':productId/variants')
  @ApiOperation({ summary: 'Add product variant' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 201, description: 'Variant created successfully' })
  async addVariant(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @CurrentUser() user: UserDocument,
    @Body() variantData: any,
  ) {
    const variant = await this.productsService.addVariant(
      productId,
      storeId,
      user._id.toString(),
      variantData,
    );
    return {
      message: 'Variant created successfully',
      variant,
    };
  }

  @Put(':productId/variants/:variantId')
  @ApiOperation({ summary: 'Update product variant' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'variantId', description: 'Variant ID' })
  @ApiResponse({ status: 200, description: 'Variant updated successfully' })
  async updateVariant(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @Param('variantId') variantId: string,
    @CurrentUser() user: UserDocument,
    @Body() variantData: any,
  ) {
    const variant = await this.productsService.updateVariant(
      variantId,
      storeId,
      user._id.toString(),
      variantData,
    );
    return {
      message: 'Variant updated successfully',
      variant,
    };
  }

  @Delete(':productId/variants/:variantId')
  @ApiOperation({ summary: 'Delete product variant' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'variantId', description: 'Variant ID' })
  @ApiResponse({ status: 200, description: 'Variant deleted successfully' })
  async deleteVariant(
    @Param('storeId') storeId: string,
    @Param('productId') productId: string,
    @Param('variantId') variantId: string,
    @CurrentUser() user: UserDocument,
  ) {
    await this.productsService.deleteVariant(variantId, storeId, user._id.toString());
    return {
      message: 'Variant deleted successfully',
    };
  }

  // Bulk Operations
  @Post('bulk-update')
  @ApiOperation({ summary: 'Bulk update products' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Products updated successfully' })
  async bulkUpdate(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { productIds: string[]; updates: any },
  ) {
    const result = await this.productsService.bulkUpdateProducts(
      storeId,
      user._id.toString(),
      body.productIds,
      body.updates,
    );
    return {
      message: API_MESSAGES.PRODUCTS_BULK_UPDATED,
      ...result,
    };
  }

  @Post('bulk-delete')
  @ApiOperation({ summary: 'Bulk delete products' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Products deleted successfully' })
  async bulkDelete(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { productIds: string[] },
  ) {
    const result = await this.productsService.bulkDeleteProducts(
      storeId,
      user._id.toString(),
      body.productIds,
    );
    return {
      message: 'Products deleted successfully',
      ...result,
    };
  }
}
