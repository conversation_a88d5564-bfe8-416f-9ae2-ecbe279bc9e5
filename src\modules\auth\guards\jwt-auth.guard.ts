import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

import { IS_PUBLIC_KEY } from '@common/decorators/public.decorator';
import { ERROR_CODES } from '@common/constants';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    return super.canActivate(context);
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    if (err || !user) {
      let message = 'Authentication required';
      let code = ERROR_CODES.AUTH_REQUIRED;

      if (info?.name === 'TokenExpiredError') {
        message = 'Token has expired';
        code = ERROR_CODES.AUTH_EXPIRED;
      } else if (info?.name === 'JsonWebTokenError') {
        message = 'Invalid token';
        code = ERROR_CODES.AUTH_INVALID;
      }

      throw new UnauthorizedException({
        message,
        code,
      });
    }

    return user;
  }
}
