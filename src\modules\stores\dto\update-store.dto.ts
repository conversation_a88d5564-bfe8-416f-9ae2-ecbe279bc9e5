import {
  IsString,
  Is<PERSON><PERSON>al,
  IsBoolean,
  Is<PERSON><PERSON>ber,
  <PERSON><PERSON>rl,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Min,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { AVAILABLE_THEMES } from '@common/constants';

export class UpdateStoreDto {
  @ApiPropertyOptional({
    description: 'Store name',
    example: 'My Updated Store',
    minLength: 2,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(2, { message: 'Store name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Store name must not exceed 100 characters' })
  name?: string;

  @ApiPropertyOptional({
    description: 'Store handle (URL slug)',
    example: 'my-updated-store',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Store handle must be at least 3 characters long' })
  @MaxLength(50, { message: 'Store handle must not exceed 50 characters' })
  @Matches(/^[a-z0-9-]+$/, {
    message: 'Store handle can only contain lowercase letters, numbers, and hyphens',
  })
  handle?: string;

  @ApiPropertyOptional({
    description: 'Store description',
    example: 'Updated store description',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Description must not exceed 500 characters' })
  description?: string;

  @ApiPropertyOptional({
    description: 'Store bio',
    example: 'Updated bio',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Bio must not exceed 200 characters' })
  bio?: string;

  @ApiPropertyOptional({
    description: 'Instagram handle',
    example: 'mystore',
  })
  @IsOptional()
  @IsString()
  @Matches(/^[a-zA-Z0-9._]+$/, {
    message: 'Please enter a valid Instagram handle',
  })
  instagramHandle?: string;

  @ApiPropertyOptional({
    description: 'Website URL',
    example: 'https://mystore.com',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Please enter a valid website URL' })
  websiteUrl?: string;

  @ApiPropertyOptional({
    description: 'Store location',
    example: 'Mumbai, India',
    maxLength: 200,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200, { message: 'Location must not exceed 200 characters' })
  location?: string;

  @ApiPropertyOptional({
    description: 'Allow reviews',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  allowReviews?: boolean;

  @ApiPropertyOptional({
    description: 'Enable Cash on Delivery',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  codEnabled?: boolean;

  @ApiPropertyOptional({
    description: 'Minimum order amount',
    example: 500,
    minimum: 0,
    maximum: 100000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Minimum order amount cannot be negative' })
  @Max(100000, { message: 'Minimum order amount cannot exceed 100000' })
  minOrderAmount?: number;

  @ApiPropertyOptional({
    description: 'Shipping fee',
    example: 50,
    minimum: 0,
    maximum: 10000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Shipping fee cannot be negative' })
  @Max(10000, { message: 'Shipping fee cannot exceed 10000' })
  shippingFee?: number;

  @ApiPropertyOptional({
    description: 'Free shipping above amount',
    example: 1000,
    minimum: 0,
    maximum: 100000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Free shipping threshold cannot be negative' })
  @Max(100000, { message: 'Free shipping threshold cannot exceed 100000' })
  freeShippingAbove?: number;

  @ApiPropertyOptional({
    description: 'Current theme',
    example: 'modern-minimal',
    enum: Object.values(AVAILABLE_THEMES),
  })
  @IsOptional()
  @IsEnum(AVAILABLE_THEMES, {
    message: 'Please select a valid theme',
  })
  currentTheme?: string;

  @ApiPropertyOptional({
    description: 'Theme configuration',
    example: {
      colors: {
        primary: '#007bff',
        secondary: '#6c757d',
      },
    },
  })
  @IsOptional()
  themeConfig?: Record<string, any>;
}
