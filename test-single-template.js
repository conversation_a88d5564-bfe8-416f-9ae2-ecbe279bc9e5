const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 15000
    });
    console.log(`✅ Response: ${response.status}`);
    return true;
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return false;
  }
}

async function testSingleTemplate() {
  console.log('🧪 TESTING SINGLE TEMPLATE\n');
  
  console.log('1️⃣ Testing connection...');
  const result1 = await sendMessage('hi');
  
  if (result1) {
    console.log('\n2️⃣ Testing order connection...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    const result2 = await sendMessage('TG-2024-001');
    
    if (result2) {
      console.log('\n3️⃣ Testing template...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      await sendMessage('track');
    }
  }

  console.log('\n🎉 Single template test completed!');
}

testSingleTemplate().catch(console.error);
