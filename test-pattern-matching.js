const axios = require('axios');

const TELEGRAM_WEBHOOK_URL = 'http://localhost:3000/api/pingbot/telegram/webhook';

// Test specific patterns that should match
const testMessages = [
  'support',
  'help',
  'contact',
  'hours',
  'location'
];

async function sendMessage(text) {
  const payload = {
    update_id: Date.now(),
    message: {
      message_id: Date.now(),
      from: {
        id: 7520184516,
        is_bot: false,
        first_name: "Test",
        username: "test_user"
      },
      chat: {
        id: 7520184516,
        first_name: "Test",
        type: "private"
      },
      date: Math.floor(Date.now() / 1000),
      text: text
    }
  };

  try {
    console.log(`📤 Testing pattern: "${text}"`);
    const response = await axios.post(TELEGRAM_WEBHOOK_URL, payload, {
      timeout: 5000
    });
    console.log(`✅ Response: ${response.status}`);
    
    // Delay between messages
    await new Promise(resolve => setTimeout(resolve, 2000));
  } catch (error) {
    console.error(`❌ Error testing "${text}":`, error.message);
  }
}

async function testPatternMatching() {
  console.log('🧪 TESTING PATTERN MATCHING WITH DEBUG LOGS\n');
  console.log('============================================================\n');

  // First establish connection
  console.log('1️⃣ Establishing connection...');
  await sendMessage('hi');
  await sendMessage('TG-2024-001');
  
  console.log('\n2️⃣ Testing pattern matching...');
  for (let i = 0; i < testMessages.length; i++) {
    const message = testMessages[i];
    console.log(`\n${i + 3}️⃣ Testing pattern: "${message}"`);
    await sendMessage(message);
  }

  console.log('\n🎉 Pattern matching tests completed!');
  console.log('📊 Check server logs for detailed pattern matching results');
}

testPatternMatching().catch(console.error);
