const axios = require('axios');

const API_BASE = 'http://localhost:3000/api/pingbot/telegram';

async function sendMessage(text) {
    try {
        const response = await axios.post(`${API_BASE}/webhook`, {
            message: {
                message_id: Math.floor(Math.random() * 1000000),
                from: {
                    id: 7520184516,
                    first_name: "Test",
                    username: "testuser"
                },
                chat: {
                    id: 7520184516,
                    type: "private"
                },
                date: Math.floor(Date.now() / 1000),
                text: text
            }
        });
        
        console.log(`📤 Sending: "${text}"`);
        return response.data;
    } catch (error) {
        console.error(`❌ Error sending "${text}":`, error.message);
        return null;
    }
}

async function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function testAllTemplates() {
    try {
        console.log('🧪 COMPREHENSIVE BOT FLOW TEST - ALL TEMPLATES\n');
        console.log('='.repeat(60));

        // PHASE 1: INITIAL CONNECTION
        console.log('\n🔥 PHASE 1: INITIAL CONNECTION');
        console.log('-'.repeat(40));
        
        console.log('1️⃣ Testing initial "hi" message...');
        await sendMessage('hi');
        await wait(2000);

        console.log('2️⃣ Testing order number connection...');
        await sendMessage('TG-2024-001');
        await wait(2000);

        // PHASE 2: ORDER STATUS TEMPLATES
        console.log('\n🔥 PHASE 2: ORDER STATUS TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('3️⃣ Testing "track"...');
        await sendMessage('track');
        await wait(2000);

        console.log('4️⃣ Testing "status"...');
        await sendMessage('status');
        await wait(2000);

        console.log('5️⃣ Testing "order status"...');
        await sendMessage('order status');
        await wait(2000);

        console.log('6️⃣ Testing "my order"...');
        await sendMessage('my order');
        await wait(2000);

        console.log('7️⃣ Testing "order"...');
        await sendMessage('order');
        await wait(2000);

        // PHASE 3: DELIVERY TEMPLATES
        console.log('\n🔥 PHASE 3: DELIVERY TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('8️⃣ Testing "delivery"...');
        await sendMessage('delivery');
        await wait(2000);

        console.log('9️⃣ Testing "shipping"...');
        await sendMessage('shipping');
        await wait(2000);

        console.log('🔟 Testing "delivery status"...');
        await sendMessage('delivery status');
        await wait(2000);

        console.log('1️⃣1️⃣ Testing "when will my order arrive"...');
        await sendMessage('when will my order arrive');
        await wait(2000);

        console.log('1️⃣2️⃣ Testing "tracking"...');
        await sendMessage('tracking');
        await wait(2000);

        // PHASE 4: PAYMENT TEMPLATES
        console.log('\n🔥 PHASE 4: PAYMENT TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('1️⃣3️⃣ Testing "payment"...');
        await sendMessage('payment');
        await wait(2000);

        console.log('1️⃣4️⃣ Testing "payment status"...');
        await sendMessage('payment status');
        await wait(2000);

        console.log('1️⃣5️⃣ Testing "refund"...');
        await sendMessage('refund');
        await wait(2000);

        console.log('1️⃣6️⃣ Testing "receipt"...');
        await sendMessage('receipt');
        await wait(2000);

        console.log('1️⃣7️⃣ Testing "invoice"...');
        await sendMessage('invoice');
        await wait(2000);

        // PHASE 5: PRODUCT TEMPLATES
        console.log('\n🔥 PHASE 5: PRODUCT TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('1️⃣8️⃣ Testing "products"...');
        await sendMessage('products');
        await wait(2000);

        console.log('1️⃣9️⃣ Testing "catalog"...');
        await sendMessage('catalog');
        await wait(2000);

        console.log('2️⃣0️⃣ Testing "categories"...');
        await sendMessage('categories');
        await wait(2000);

        console.log('2️⃣1️⃣ Testing "featured"...');
        await sendMessage('featured');
        await wait(2000);

        console.log('2️⃣2️⃣ Testing "new arrivals"...');
        await sendMessage('new arrivals');
        await wait(2000);

        // PHASE 6: ORDER MANAGEMENT TEMPLATES
        console.log('\n🔥 PHASE 6: ORDER MANAGEMENT TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('2️⃣3️⃣ Testing "cancel"...');
        await sendMessage('cancel');
        await wait(2000);

        console.log('2️⃣4️⃣ Testing "cancel order"...');
        await sendMessage('cancel order');
        await wait(2000);

        console.log('2️⃣5️⃣ Testing "modify"...');
        await sendMessage('modify');
        await wait(2000);

        console.log('2️⃣6️⃣ Testing "change order"...');
        await sendMessage('change order');
        await wait(2000);

        console.log('2️⃣7️⃣ Testing "update order"...');
        await sendMessage('update order');
        await wait(2000);

        // PHASE 7: SUPPORT TEMPLATES
        console.log('\n🔥 PHASE 7: SUPPORT TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('2️⃣8️⃣ Testing "support"...');
        await sendMessage('support');
        await wait(2000);

        console.log('2️⃣9️⃣ Testing "help"...');
        await sendMessage('help');
        await wait(2000);

        console.log('3️⃣0️⃣ Testing "customer service"...');
        await sendMessage('customer service');
        await wait(2000);

        console.log('3️⃣1️⃣ Testing "talk to human"...');
        await sendMessage('talk to human');
        await wait(2000);

        console.log('3️⃣2️⃣ Testing "agent"...');
        await sendMessage('agent');
        await wait(2000);

        // PHASE 8: STORE INFO TEMPLATES
        console.log('\n🔥 PHASE 8: STORE INFO TEMPLATES');
        console.log('-'.repeat(40));
        
        console.log('3️⃣3️⃣ Testing "store info"...');
        await sendMessage('store info');
        await wait(2000);

        console.log('3️⃣4️⃣ Testing "about"...');
        await sendMessage('about');
        await wait(2000);

        console.log('3️⃣5️⃣ Testing "contact"...');
        await sendMessage('contact');
        await wait(2000);

        console.log('3️⃣6️⃣ Testing "hours"...');
        await sendMessage('hours');
        await wait(2000);

        console.log('3️⃣7️⃣ Testing "location"...');
        await sendMessage('location');
        await wait(2000);

        // PHASE 9: SPECIFIC PRODUCT SEARCHES
        console.log('\n🔥 PHASE 9: SPECIFIC PRODUCT SEARCHES');
        console.log('-'.repeat(40));
        
        console.log('3️⃣8️⃣ Testing "iphone"...');
        await sendMessage('iphone');
        await wait(2000);

        console.log('3️⃣9️⃣ Testing "laptop"...');
        await sendMessage('laptop');
        await wait(2000);

        console.log('4️⃣0️⃣ Testing "smartphone"...');
        await sendMessage('smartphone');
        await wait(2000);

        // PHASE 10: EDGE CASES AND VARIATIONS
        console.log('\n🔥 PHASE 10: EDGE CASES AND VARIATIONS');
        console.log('-'.repeat(40));
        
        console.log('4️⃣1️⃣ Testing "what is my order status"...');
        await sendMessage('what is my order status');
        await wait(2000);

        console.log('4️⃣2️⃣ Testing "where is my package"...');
        await sendMessage('where is my package');
        await wait(2000);

        console.log('4️⃣3️⃣ Testing "how much did I pay"...');
        await sendMessage('how much did I pay');
        await wait(2000);

        console.log('4️⃣4️⃣ Testing "show me products"...');
        await sendMessage('show me products');
        await wait(2000);

        console.log('4️⃣5️⃣ Testing "I need help"...');
        await sendMessage('I need help');
        await wait(2000);

        console.log('\n🎉 COMPREHENSIVE TEST COMPLETED!');
        console.log('✅ All 45 templates tested with connected order context');
        console.log('📊 Check server logs for detailed template responses');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAllTemplates();
