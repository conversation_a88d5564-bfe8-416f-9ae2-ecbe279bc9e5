import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { CustomersService } from './customers.service';
import { JwtAuthGuard } from '@modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@common/decorators';
import { UserDocument } from '@database/schemas/user.schema';

@ApiTags('Customers')
@Controller('stores/:storeId/customers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class CustomersController {
  constructor(private readonly customersService: CustomersService) {}

  @Get()
  @ApiOperation({ summary: 'Get all customers for a store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'segment', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  @ApiResponse({ status: 200, description: 'Customers retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async findAll(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query() query: any,
  ) {
    const result = await this.customersService.findAll(storeId, user._id.toString(), query);
    return {
      message: 'Customers retrieved successfully',
      ...result,
    };
  }

  @Get('analytics')
  @ApiOperation({ summary: 'Get customer analytics for store' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Customer analytics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getAnalytics(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const analytics = await this.customersService.getCustomerAnalytics(storeId, user._id.toString());
    return {
      message: 'Customer analytics retrieved successfully',
      analytics,
    };
  }

  @Get('segments')
  @ApiOperation({ summary: 'Get customer segments breakdown' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Customer segments retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getSegments(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const segments = await this.customersService.getCustomerSegments(storeId, user._id.toString());
    return {
      message: 'Customer segments retrieved successfully',
      segments,
    };
  }

  @Get('top')
  @ApiOperation({ summary: 'Get top customers by spending' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of top customers to return' })
  @ApiResponse({ status: 200, description: 'Top customers retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getTopCustomers(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
    @Query('limit') limit?: number,
  ) {
    const customers = await this.customersService.getTopCustomers(storeId, user._id.toString(), limit);
    return {
      message: 'Top customers retrieved successfully',
      customers,
    };
  }

  @Get('export')
  @ApiOperation({ summary: 'Export customers data' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiResponse({ status: 200, description: 'Customers data exported successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async exportCustomers(
    @Param('storeId') storeId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const customers = await this.customersService.exportCustomers(storeId, user._id.toString());
    return {
      message: 'Customers data exported successfully',
      customers,
    };
  }

  @Get(':customerId')
  @ApiOperation({ summary: 'Get customer details' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiResponse({ status: 200, description: 'Customer details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Customer not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async getCustomerDetails(
    @Param('storeId') storeId: string,
    @Param('customerId') customerId: string,
    @CurrentUser() user: UserDocument,
  ) {
    const details = await this.customersService.getCustomerDetails(customerId, storeId, user._id.toString());
    return {
      message: 'Customer details retrieved successfully',
      ...details,
    };
  }

  @Put(':customerId')
  @ApiOperation({ summary: 'Update customer information' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiResponse({ status: 200, description: 'Customer updated successfully' })
  @ApiResponse({ status: 404, description: 'Customer not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async updateCustomer(
    @Param('storeId') storeId: string,
    @Param('customerId') customerId: string,
    @CurrentUser() user: UserDocument,
    @Body() updateData: any,
  ) {
    const customer = await this.customersService.updateCustomer(
      customerId,
      storeId,
      user._id.toString(),
      updateData,
    );
    return {
      message: 'Customer updated successfully',
      customer,
    };
  }

  @Post(':customerId/tags')
  @ApiOperation({ summary: 'Add tags to customer' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiResponse({ status: 200, description: 'Tags added successfully' })
  @ApiResponse({ status: 404, description: 'Customer not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async addTags(
    @Param('storeId') storeId: string,
    @Param('customerId') customerId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { tags: string[] },
  ) {
    const customer = await this.customersService.addCustomerTags(
      customerId,
      storeId,
      user._id.toString(),
      body.tags,
    );
    return {
      message: 'Tags added successfully',
      customer,
    };
  }

  @Put(':customerId/tags/remove')
  @ApiOperation({ summary: 'Remove tags from customer' })
  @ApiParam({ name: 'storeId', description: 'Store ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiResponse({ status: 200, description: 'Tags removed successfully' })
  @ApiResponse({ status: 404, description: 'Customer not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - not store owner' })
  async removeTags(
    @Param('storeId') storeId: string,
    @Param('customerId') customerId: string,
    @CurrentUser() user: UserDocument,
    @Body() body: { tags: string[] },
  ) {
    const customer = await this.customersService.removeCustomerTags(
      customerId,
      storeId,
      user._id.toString(),
      body.tags,
    );
    return {
      message: 'Tags removed successfully',
      customer,
    };
  }
}
