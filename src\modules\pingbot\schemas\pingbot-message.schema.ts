import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type PingBotMessageDocument = PingBotMessage & Document;

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  VIDEO = 'video',
  LOCATION = 'location',
  CONTACT = 'contact',
  TEMPLATE = 'template',
  INTERACTIVE = 'interactive',
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  PENDING = 'pending',
}

@Schema({
  collection: 'pingbot_messages',
  timestamps: true,
})
export class PingBotMessage {
  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Conversation', 
    required: true,
    index: true 
  })
  conversationId: Types.ObjectId;

  @Prop({ 
    type: Types.ObjectId, 
    ref: 'Store', 
    required: true,
    index: true 
  })
  storeId: Types.ObjectId;

  @Prop({ 
    required: true,
    trim: true 
  })
  customerPhone: string;

  @Prop({ 
    type: String,
    enum: MessageDirection,
    required: true,
    index: true 
  })
  direction: MessageDirection;

  @Prop({ 
    type: String,
    enum: MessageType,
    default: MessageType.TEXT,
    index: true 
  })
  type: MessageType;

  @Prop({ 
    required: true 
  })
  content: string;

  @Prop({ 
    type: Object,
    default: {} 
  })
  metadata: Record<string, any>;

  @Prop({ 
    type: String,
    enum: MessageStatus,
    default: MessageStatus.PENDING,
    index: true 
  })
  status: MessageStatus;

  @Prop({ 
    trim: true 
  })
  whatsappMessageId?: string;

  @Prop({ 
    trim: true 
  })
  errorMessage?: string;

  @Prop({ 
    type: Object,
    default: {} 
  })
  aiContext: Record<string, any>;

  @Prop({ 
    trim: true 
  })
  intent?: string;

  @Prop({ 
    type: Object,
    default: {} 
  })
  entities: Record<string, any>;

  @Prop({ 
    min: 0,
    max: 1 
  })
  confidence?: number;

  @Prop({ 
    type: Date 
  })
  deliveredAt?: Date;

  @Prop({ 
    type: Date 
  })
  readAt?: Date;

  @Prop({ 
    default: false 
  })
  isProcessed: boolean;

  @Prop({ 
    type: Date 
  })
  processedAt?: Date;

  @Prop({ 
    type: [String],
    default: [] 
  })
  attachments: string[];

  @Prop({ 
    type: Object,
    default: {} 
  })
  templateData?: Record<string, any>;
}

export const PingBotMessageSchema = SchemaFactory.createForClass(PingBotMessage);

// Indexes for performance
PingBotMessageSchema.index({ conversationId: 1, createdAt: -1 });
PingBotMessageSchema.index({ storeId: 1, createdAt: -1 });
PingBotMessageSchema.index({ customerPhone: 1, createdAt: -1 });
PingBotMessageSchema.index({ direction: 1, status: 1 });
PingBotMessageSchema.index({ whatsappMessageId: 1 });
PingBotMessageSchema.index({ isProcessed: 1, createdAt: 1 });

// Methods
PingBotMessageSchema.methods.markAsDelivered = function() {
  this.status = MessageStatus.DELIVERED;
  this.deliveredAt = new Date();
  return this.save();
};

PingBotMessageSchema.methods.markAsRead = function() {
  this.status = MessageStatus.READ;
  this.readAt = new Date();
  return this.save();
};

PingBotMessageSchema.methods.markAsProcessed = function() {
  this.isProcessed = true;
  this.processedAt = new Date();
  return this.save();
};
