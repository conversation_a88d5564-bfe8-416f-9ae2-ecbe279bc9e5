import { registerAs } from '@nestjs/config';

export const throttlerConfig = registerAs('throttler', () => ({
  ttl: parseInt(process.env.RATE_LIMIT_TTL) || 60,
  limit: parseInt(process.env.RATE_LIMIT_MAX) || 100,
  
  // Specific rate limits for different endpoints
  auth: {
    ttl: 60, // 1 minute
    limit: 5, // 5 requests per minute
  },
  upload: {
    ttl: 60, // 1 minute
    limit: 10, // 10 uploads per minute
  },
  public: {
    ttl: 60, // 1 minute
    limit: 1000, // 1000 requests per minute for public APIs
  },
  orders: {
    ttl: 3600, // 1 hour
    limit: 50, // 50 order creations per hour
  },
}));
