import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface TelegramMessage {
  message_id: number;
  from: {
    id: number;
    first_name: string;
    username?: string;
  };
  chat: {
    id: number;
    type: string;
  };
  text?: string;
  date: number;
}

export interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
}

@Injectable()
export class TelegramService {
  private readonly logger = new Logger(TelegramService.name);
  private readonly botToken: string;
  private readonly apiUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.botToken = this.configService.get('TELEGRAM_BOT_TOKEN') || '';
    this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  /**
   * Verify webhook for Telegram
   */
  verifyWebhook(token: string): boolean {
    const expectedToken = this.configService.get('TELEGRAM_WEBHOOK_TOKEN');
    return token === expectedToken;
  }

  /**
   * Parse incoming Telegram webhook message
   */
  parseWebhookMessage(body: any): TelegramMessage | null {
    try {
      if (body.message && body.message.text) {
        return body.message;
      }
      return null;
    } catch (error) {
      this.logger.error('Error parsing Telegram message:', error);
      return null;
    }
  }

  /**
   * Send text message to Telegram chat
   */
  async sendTextMessage(chatId: number, text: string): Promise<any> {
    try {
      // LOG THE COMPLETE BOT RESPONSE FOR TESTING
      this.logger.log(`🤖 BOT RESPONSE TO CHAT ${chatId}:`);
      this.logger.log(`📝 MESSAGE: ${text}`);
      this.logger.log(`⏰ TIME: ${new Date().toISOString()}`);
      this.logger.log(`${'='.repeat(80)}`);

      if (!this.botToken) {
        throw new Error('Telegram bot token not configured');
      }

      const response = await axios.post(`${this.apiUrl}/sendMessage`, {
        chat_id: chatId,
        text: text,
        parse_mode: 'HTML',
      });

      this.logger.log(`✅ Message sent successfully to Telegram chat ${chatId}`);
      return response.data;
    } catch (error) {
      this.logger.error('❌ Error sending Telegram message:', error);
      // Still log what we tried to send even if it failed
      this.logger.log(`❌ FAILED TO SEND TO CHAT ${chatId}: ${text}`);
      throw error;
    }
  }

  /**
   * Send typing action to show bot is processing
   */
  async sendTypingAction(chatId: number): Promise<void> {
    try {
      if (!this.botToken) return;

      await axios.post(`${this.apiUrl}/sendChatAction`, {
        chat_id: chatId,
        action: 'typing',
      });
    } catch (error) {
      this.logger.error('Error sending typing action:', error);
    }
  }

  /**
   * Set webhook URL for Telegram bot
   */
  async setWebhook(webhookUrl: string): Promise<any> {
    try {
      if (!this.botToken) {
        throw new Error('Telegram bot token not configured');
      }

      const response = await axios.post(`${this.apiUrl}/setWebhook`, {
        url: webhookUrl,
        allowed_updates: ['message'],
      });

      this.logger.log(`Telegram webhook set to: ${webhookUrl}`);
      return response.data;
    } catch (error) {
      this.logger.error('Error setting Telegram webhook:', error);
      throw error;
    }
  }

  /**
   * Get webhook info
   */
  async getWebhookInfo(): Promise<any> {
    try {
      if (!this.botToken) {
        throw new Error('Telegram bot token not configured');
      }

      const response = await axios.get(`${this.apiUrl}/getWebhookInfo`);
      return response.data;
    } catch (error) {
      this.logger.error('Error getting webhook info:', error);
      throw error;
    }
  }

  /**
   * Get bot information
   */
  async getBotInfo(): Promise<any> {
    try {
      if (!this.botToken) {
        throw new Error('Telegram bot token not configured');
      }

      const response = await axios.get(`${this.apiUrl}/getMe`);
      return response.data;
    } catch (error) {
      this.logger.error('Error getting bot info:', error);
      throw error;
    }
  }

  /**
   * Get configuration status
   */
  getConfiguration() {
    return {
      isConfigured: !!this.botToken,
      apiUrl: this.apiUrl.replace(this.botToken, '***'),
      hasToken: !!this.botToken,
    };
  }

  /**
   * Format message for Telegram (HTML formatting)
   */
  formatMessage(text: string): string {
    // Basic HTML formatting for Telegram
    return text
      .replace(/\*\*(.*?)\*\*/g, '<b>$1</b>') // Bold
      .replace(/\*(.*?)\*/g, '<i>$1</i>') // Italic
      .replace(/`(.*?)`/g, '<code>$1</code>') // Code
      .replace(/\n/g, '\n'); // Preserve line breaks
  }

  /**
   * Create inline keyboard for common actions
   */
  createInlineKeyboard(actions: string[]): any {
    const keyboard = actions.map(action => [{
      text: action,
      callback_data: action.toLowerCase().replace(/\s+/g, '_')
    }]);

    return {
      reply_markup: {
        inline_keyboard: keyboard
      }
    };
  }
}
