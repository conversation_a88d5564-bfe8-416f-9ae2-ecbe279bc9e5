import {
  Controller,
  Post,
  Get,
  Body,
  Headers,
  Logger,
  HttpStatus,
  HttpException,
  Res,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiHeader,
} from '@nestjs/swagger';
import { Request, Response } from 'express';
import { PingBotService } from '../pingbot.service';
import { TwilioWhatsAppService } from '../services/twilio-whatsapp.service';
import { StoresService } from '../../stores/stores.service';
import { MessageType } from '../schemas/pingbot-message.schema';

@ApiTags('Twilio WhatsApp Webhook')
@Controller('pingbot/twilio')
export class TwilioWebhookController {
  private readonly logger = new Logger(TwilioWebhookController.name);

  constructor(
    private readonly pingBotService: PingBotService,
    private readonly twilioService: TwilioWhatsAppService,
    private readonly storesService: StoresService,
  ) {}

  @Post('webhook')
  @ApiOperation({ summary: 'Handle incoming Twilio WhatsApp messages' })
  @ApiHeader({ name: 'X-Twilio-Signature', description: 'Twilio webhook signature' })
  @ApiBody({ description: 'Twilio webhook payload' })
  @ApiResponse({ status: 200, description: 'Message processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload' })
  async handleWebhook(
    @Body() body: any,
    @Headers('x-twilio-signature') signature: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      this.logger.debug('Received Twilio webhook:', JSON.stringify(body, null, 2));

      // Validate webhook signature (optional for testing)
      const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
      const isValid = this.twilioService.validateWebhook(signature, url, body);
      
      if (!isValid) {
        this.logger.warn('Invalid Twilio webhook signature');
        // For testing, we'll allow invalid signatures
        // return res.status(403).send('Forbidden');
      }

      // Parse the webhook message
      const message = this.twilioService.parseWebhookMessage(body);
      
      if (!message) {
        this.logger.warn('No valid message found in Twilio webhook payload');
        return res.status(200).type('text/xml').send('<Response></Response>');
      }

      const phoneNumber = this.twilioService.extractPhoneNumber(message.From);
      this.logger.log(`Processing Twilio WhatsApp message from ${phoneNumber}: ${message.Body}`);

      // Get default store for testing
      const defaultStore = await this.getDefaultStore();
      
      if (!defaultStore) {
        this.logger.error('No default store found for Twilio WhatsApp integration');
        await this.twilioService.sendMessage(
          message.From,
          '❌ Sorry, our store is temporarily unavailable. Please try again later.'
        );
        return res.status(200).type('text/xml').send('<Response></Response>');
      }

      // Process the message with PingBot
      const result = await this.pingBotService.processMessage({
        storeId: defaultStore._id.toString(),
        customerPhone: phoneNumber,
        message: message.Body,
        messageType: this.getMessageType(message),
        metadata: {
          twilioAccountSid: message.AccountSid,
          twilioMessageSid: message.MessageSid,
          platform: 'twilio_whatsapp',
          hasMedia: message.NumMedia && parseInt(message.NumMedia) > 0,
          mediaUrl: message.MediaUrl0,
          mediaType: message.MediaContentType0,
        },
      });

      // Send response back via Twilio WhatsApp
      if (result.response.message) {
        const formattedMessage = this.twilioService.formatMessage(result.response.message);
        
        // Add quick replies if suggested actions exist
        let finalMessage = formattedMessage;
        if (result.response.suggestedActions && result.response.suggestedActions.length > 0) {
          finalMessage += this.twilioService.createQuickReplies(
            result.response.suggestedActions.slice(0, 3)
          );
        }

        await this.twilioService.sendMessage(message.From, finalMessage);
      }

      // Handle escalation if needed
      if (result.response.requiresHuman) {
        this.logger.log(`Twilio message escalated to human agent for conversation ${result.conversationId}`);
        
        await this.twilioService.sendMessage(
          message.From,
          '👨‍💼 Your request has been escalated to our support team. A human agent will assist you shortly.'
        );
      }

      // Return TwiML response
      return res.status(200).type('text/xml').send('<Response></Response>');
    } catch (error) {
      this.logger.error('Error processing Twilio webhook:', error);
      return res.status(200).type('text/xml').send('<Response></Response>');
    }
  }

  @Post('send-message')
  @ApiOperation({ summary: 'Send WhatsApp message via Twilio (for testing)' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        to: { type: 'string', description: 'Recipient phone number (with country code)' },
        message: { type: 'string', description: 'Message text' },
      },
      required: ['to', 'message'],
    },
  })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendMessage(@Body() body: { to: string; message: string }) {
    try {
      const result = await this.twilioService.sendMessage(body.to, body.message);
      
      return {
        message: 'WhatsApp message sent successfully via Twilio',
        data: {
          messageSid: result.sid,
          status: result.status,
          to: result.to,
          from: result.from,
        },
      };
    } catch (error) {
      this.logger.error('Error sending Twilio WhatsApp message:', error);
      throw new HttpException(
        'Failed to send message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('config')
  @ApiOperation({ summary: 'Get Twilio WhatsApp configuration status' })
  @ApiResponse({ status: 200, description: 'Configuration retrieved successfully' })
  async getConfig() {
    try {
      const config = this.twilioService.getConfiguration();

      // Debug: Check environment variables
      const envDebug = {
        TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID ? `${process.env.TWILIO_ACCOUNT_SID.substring(0, 8)}...` : 'Not set',
        TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN ? `${process.env.TWILIO_AUTH_TOKEN.substring(0, 8)}...` : 'Not set',
        TWILIO_WHATSAPP_NUMBER: process.env.TWILIO_WHATSAPP_NUMBER || 'Not set',
      };

      return {
        message: 'Twilio WhatsApp configuration retrieved successfully',
        data: {
          ...config,
          envDebug,
        },
      };
    } catch (error) {
      this.logger.error('Error getting Twilio config:', error);
      throw new HttpException(
        'Failed to get configuration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('test-connection')
  @ApiOperation({ summary: 'Test Twilio connection' })
  @ApiResponse({ status: 200, description: 'Connection test completed' })
  async testConnection() {
    try {
      const isConnected = await this.twilioService.testConnection();
      const accountInfo = isConnected ? await this.twilioService.getAccountInfo() : null;
      
      return {
        message: 'Twilio connection test completed',
        data: {
          isConnected,
          accountInfo,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      this.logger.error('Error testing Twilio connection:', error);
      return {
        message: 'Twilio connection test failed',
        data: {
          isConnected: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  @Get('sandbox-info')
  @ApiOperation({ summary: 'Get Twilio WhatsApp sandbox information' })
  @ApiResponse({ status: 200, description: 'Sandbox info retrieved successfully' })
  async getSandboxInfo() {
    try {
      const config = this.twilioService.getConfiguration();
      
      return {
        message: 'Twilio WhatsApp sandbox info retrieved successfully',
        data: {
          sandboxNumber: config.whatsappNumber,
          isConfigured: config.isConfigured,
          instructions: {
            step1: 'Send a WhatsApp message to +1 415 523 8886',
            step2: 'Include the code: "join <your-sandbox-name>"',
            step3: 'Once joined, you can test the bot by sending messages',
            step4: 'The bot will respond with AI-powered answers',
          },
          webhookUrl: `https://381b-103-70-36-156.ngrok-free.app/api/pingbot/twilio/webhook`,
        },
      };
    } catch (error) {
      this.logger.error('Error getting sandbox info:', error);
      throw new HttpException(
        'Failed to get sandbox info',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async getDefaultStore() {
    try {
      const stores = await this.storesService.findAll({ limit: 1, page: 1 });
      return stores.data[0] || null;
    } catch (error) {
      this.logger.error('Error getting default store:', error);
      return null;
    }
  }

  private getMessageType(message: any): MessageType {
    if (message.NumMedia && parseInt(message.NumMedia) > 0) {
      const mediaType = message.MediaContentType0;
      if (mediaType?.startsWith('image/')) return MessageType.IMAGE;
      if (mediaType?.startsWith('video/')) return MessageType.VIDEO;
      if (mediaType?.startsWith('audio/')) return MessageType.AUDIO;
      return MessageType.DOCUMENT;
    }
    return MessageType.TEXT;
  }
}
