import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  Headers,
  HttpCode,
  HttpStatus,
  RawBodyRequest,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { PaymentsService } from './payments.service';
import { Public } from '@common/decorators';
import { API_MESSAGES } from '@common/constants';

// DTOs
import { ProcessPaymentDto, VerifyPaymentDto } from './dto/process-payment.dto';

@ApiTags('Payments')
@Controller('payments')
@Public() // All payment endpoints are public (no auth required)
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('create-intent')
  @ApiOperation({ summary: 'Create payment intent (public)' })
  @ApiResponse({ status: 201, description: 'Payment intent created successfully' })
  @ApiResponse({ status: 400, description: 'Validation error' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async createPaymentIntent(@Body() processPaymentDto: ProcessPaymentDto) {
    const paymentIntent = await this.paymentsService.createPaymentIntent(processPaymentDto);
    return {
      message: 'Payment intent created successfully',
      paymentIntent,
    };
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify payment (public)' })
  @ApiResponse({ status: 200, description: 'Payment verified successfully' })
  @ApiResponse({ status: 400, description: 'Payment verification failed' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async verifyPayment(@Body() verifyPaymentDto: VerifyPaymentDto) {
    const result = await this.paymentsService.verifyPayment(verifyPaymentDto);
    return {
      message: 'Payment verified successfully',
      ...result,
    };
  }

  @Get('orders/:orderId/status')
  @ApiOperation({ summary: 'Get payment status for order (public)' })
  @ApiParam({ name: 'orderId', description: 'Order ID' })
  @ApiQuery({ name: 'phone', required: false, description: 'Customer phone for verification' })
  @ApiResponse({ status: 200, description: 'Payment status retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async getPaymentStatus(
    @Param('orderId') orderId: string,
    @Query('phone') phone?: string,
  ) {
    const status = await this.paymentsService.getPaymentStatus(orderId, phone);
    return {
      message: 'Payment status retrieved successfully',
      ...status,
    };
  }

  // Webhook endpoints
  @Post('webhooks/razorpay')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Razorpay webhook endpoint' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async handleRazorpayWebhook(
    @Body() payload: any,
    @Headers('x-razorpay-signature') signature: string,
  ) {
    await this.paymentsService.handleWebhook('razorpay', payload, signature);
    return {
      success: true,
    };
  }

  @Post('webhooks/stripe')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Stripe webhook endpoint' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  async handleStripeWebhook(
    @Req() req: RawBodyRequest<Request>,
    @Headers('stripe-signature') signature: string,
  ) {
    await this.paymentsService.handleWebhook('stripe', req.rawBody, signature);
    return {
      success: true,
    };
  }
}
